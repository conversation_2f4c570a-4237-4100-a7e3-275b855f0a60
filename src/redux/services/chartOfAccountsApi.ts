import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { Account, BankAccount } from '@/lib/types';

// Define the response type for paginated accounts (matches Go backend response)
export interface AccountsResponse {
  data: Account[] | null;
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

// Define the response type for bank account linked to chart of account
export interface LinkedBankAccountResponse extends BankAccount {
  linked: boolean;
}

export const chartOfAccountsApi = createApi({
  reducerPath: 'chartOfAccountsApi',
  baseQuery: fetchBaseQuery({ baseUrl: '/api/' }),
  tagTypes: ['Account'],
  endpoints: (builder) => ({
    // Get all unlinked accounts (not linked to bank accounts)
    getUnlinkedAccounts: builder.query<AccountsResponse, Partial<{
      accountType: string;
      isActive: boolean;
    }>>({
      query: (params) => {
        const queryParams = new URLSearchParams();
        if (params.accountType) queryParams.append('accountType', params.accountType);
        if (params.isActive !== undefined) queryParams.append('isActive', params.isActive.toString());

        return `coa/unlinked?${queryParams.toString()}`;
      },
      providesTags: [{ type: 'Account', id: 'UNLINKED' }],
    }),
    // Get accounts with pagination and filtering
    getAccounts: builder.query<
      AccountsResponse,
      {
        search?: string;
        accountType?: string;
        isActive?: boolean;
        page?: number;
        limit?: number;
        sortBy?: string;
        sortOrder?: 'asc' | 'desc';
      } | void
    >({
      query: (params = {}) => {
        let url = 'coa';
        const queryParams = new URLSearchParams();

        if (params.search) queryParams.append('search', params.search);
        if (params.accountType) queryParams.append('accountType', params.accountType);
        if (params.isActive !== undefined) queryParams.append('isActive', String(params.isActive));
        if (params.page) queryParams.append('page', String(params.page));
        if (params.limit) queryParams.append('limit', String(params.limit));
        if (params.sortBy) queryParams.append('sortBy', params.sortBy);
        if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);

        const queryString = queryParams.toString();
        if (queryString) {
          url += `?${queryString}`;
        }

        return url;
      },
      providesTags: (result) =>
        result && result.data
          ? [
              ...result.data.map(({ id }) => ({ type: 'Account' as const, id })),
              { type: 'Account', id: 'LIST' },
            ]
          : [{ type: 'Account', id: 'LIST' }],
    }),

    // Get a specific account by ID
    getAccountById: builder.query<Account, string>({
      query: (id) => `coa/${id}`,
      providesTags: (result, error, id) => [{ type: 'Account', id }],
    }),

    // Create a new account
    createAccount: builder.mutation<Account, Partial<Account>>({
      query: (body) => ({
        url: 'coa',
        method: 'POST',
        body,
      }),
      invalidatesTags: [{ type: 'Account', id: 'LIST' }],
    }),

    // Update an account
    updateAccount: builder.mutation<Account, { id: string; body: Partial<Account> }>({
      query: ({ id, body }) => ({
        url: `coa/${id}`,
        method: 'PUT',
        body,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Account', id },
        { type: 'Account', id: 'LIST' }
      ],
    }),

    // Delete an account
    deleteAccount: builder.mutation<void, string>({
      query: (id) => ({
        url: `coa/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: [{ type: 'Account', id: 'LIST' }],
    }),

    // Get bank account linked to a chart of account
    getLinkedBankAccount: builder.query<LinkedBankAccountResponse, string>({
      query: (id) => `coa/${id}/bank-account`,
      providesTags: (result, error, id) => [
        { type: 'Account', id },
        { type: 'Account', id: 'BANK_LINK' }
      ],
    }),
  }),
});

export const {
  useGetUnlinkedAccountsQuery,
  useGetAccountsQuery,
  useGetAccountByIdQuery,
  useCreateAccountMutation,
  useUpdateAccountMutation,
  useDeleteAccountMutation,
  useGetLinkedBankAccountQuery,
} = chartOfAccountsApi;
