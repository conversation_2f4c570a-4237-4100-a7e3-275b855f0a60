// src/redux/services/organizationsApi.ts
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

// Define types for organization management
interface Organization {
  id: string;
  name: string;
  description?: string;
  address?: string;
  phone?: string;
  email?: string;
  website?: string;
  taxId?: string;
  logoUrl?: string;
  isActive: boolean;
  currency: string;
  fiscalYearStart?: string;
  legalName?: string;
  createdAt: string;
  updatedAt: string;
  branches?: Branch[];
  userPermissionLevel?: string;
}

interface Branch {
  id: string;
  organizationId: string;
  name: string;
  description?: string;
  address?: string;
  phone?: string;
  email?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  managerName?: string;
  branchCode?: string;
}

interface OrganizationsResponse {
  data: Organization[];
  page: number;
  limit: number;
  total: number;
}

interface BranchesResponse {
  branches: Branch[];
}

interface CreateOrganizationRequest {
  name: string;
  description?: string;
  address?: string;
  phone?: string;
  email?: string;
  website?: string;
  taxId?: string;
  logoUrl?: string;
  currency?: string;
  fiscalYearStart?: string;
  legalName?: string;
  isActive?: boolean;
}

interface CreateBranchRequest {
  name: string;
  description?: string;
  address?: string;
  phone?: string;
  email?: string;
  managerName?: string;
  branchCode?: string;
  isActive?: boolean;
}

interface UpdateOrganizationRequest extends Partial<CreateOrganizationRequest> {}
interface UpdateBranchRequest extends Partial<CreateBranchRequest> {}

export const organizationsApi = createApi({
  reducerPath: 'organizationsApi',
  baseQuery: fetchBaseQuery({ baseUrl: '/api/' }),
  tagTypes: ['Organization', 'Branch'],
  endpoints: (builder) => ({
    // Get all organizations the user has access to
    getOrganizations: builder.query<OrganizationsResponse, void>({
      query: () => 'organizations',
      providesTags: ['Organization'],
    }),

    // Get a specific organization by ID
    getOrganizationById: builder.query<Organization, string>({
      query: (id) => `organizations/${id}`,
      providesTags: (result, error, id) => [{ type: 'Organization', id }],
    }),

    // Create a new organization
    createOrganization: builder.mutation<Organization, CreateOrganizationRequest>({
      query: (body) => ({
        url: 'organizations',
        method: 'POST',
        body,
      }),
      invalidatesTags: ['Organization'],
    }),

    // Update an existing organization
    updateOrganization: builder.mutation<Organization, { id: string; data: UpdateOrganizationRequest }>({
      query: ({ id, data }) => ({
        url: `organizations/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Organization', id },
        'Organization',
      ],
    }),

    // Delete an organization
    deleteOrganization: builder.mutation<{ message: string }, string>({
      query: (id) => ({
        url: `organizations/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Organization'],
    }),

    // Get all branches for a specific organization
    getOrganizationBranches: builder.query<BranchesResponse, string>({
      query: (organizationId) => `organizations/${organizationId}/branches`,
      providesTags: (result, error, organizationId) => [
        { type: 'Branch', id: organizationId },
      ],
    }),

    // Create a new branch
    createBranch: builder.mutation<Branch, { organizationId: string; data: CreateBranchRequest }>({
      query: ({ organizationId, data }) => ({
        url: `organizations/${organizationId}/branches`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (result, error, { organizationId }) => [
        { type: 'Branch', id: organizationId },
      ],
    }),

    // Get a specific branch by ID
    getBranchById: builder.query<Branch, { organizationId: string; branchId: string }>({
      query: ({ organizationId, branchId }) => `organizations/${organizationId}/branches/${branchId}`,
      providesTags: (result, error, { organizationId, branchId }) => [
        { type: 'Branch', id: `${organizationId}-${branchId}` },
      ],
    }),

    // Update an existing branch
    updateBranch: builder.mutation<Branch, { organizationId: string; branchId: string; data: UpdateBranchRequest }>({
      query: ({ organizationId, branchId, data }) => ({
        url: `organizations/${organizationId}/branches/${branchId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { organizationId, branchId }) => [
        { type: 'Branch', id: `${organizationId}-${branchId}` },
        { type: 'Branch', id: organizationId },
      ],
    }),

    // Delete a branch
    deleteBranch: builder.mutation<{ message: string }, { organizationId: string; branchId: string }>({
      query: ({ organizationId, branchId }) => ({
        url: `organizations/${organizationId}/branches/${branchId}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, { organizationId }) => [
        { type: 'Branch', id: organizationId },
      ],
    }),
  }),
});

export const {
  useGetOrganizationsQuery,
  useGetOrganizationByIdQuery,
  useCreateOrganizationMutation,
  useUpdateOrganizationMutation,
  useDeleteOrganizationMutation,
  useGetOrganizationBranchesQuery,
  useCreateBranchMutation,
  useGetBranchByIdQuery,
  useUpdateBranchMutation,
  useDeleteBranchMutation,
} = organizationsApi;
