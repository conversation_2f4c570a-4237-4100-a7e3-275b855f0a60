'use client';

import React, { useState } from 'react';
import { useTranslations } from 'next-intl';
import { Link } from '@/i18n/navigation';
import type { Account, AccountType } from '@/lib/types';
import {
  FileText,
  Wallet,
  Building2,
  Search,
  Plus,
  Edit,
  Trash,
  MoreHorizontal,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  Link2
} from "lucide-react";

// Import RTK Query hooks
import {
  useGetAccountsQuery,
  useCreateAccountMutation,
  useUpdateAccountMutation,
  useDeleteAccountMutation,
  useGetLinkedBankAccountQuery
} from '@/redux/services/chartOfAccountsApi';
import { useGetBankAccountsQuery } from '@/redux/services/bankingApi';

// Import custom hooks
import { useUserPermissions } from '@/hooks/useUserPermissions';
import { useToast } from '@/components/ui/use-toast';

// Import custom components
import { BankAccountLinkDialog } from './_components/BankAccountLinkDialog';

// Bank Account Status Component
const BankAccountStatus = ({ accountId }: { accountId: string }) => {
  const t = useTranslations('chartOfAccounts');
  const common = useTranslations('common');

  const { data: bankAccount, isLoading, error } = useGetLinkedBankAccountQuery(accountId, {
    // Skip the query if no accountId is provided
    skip: !accountId
  });

  if (isLoading) {
    return <span className="text-gray-400 text-sm">{common('loading')}</span>;
  }

  if (error) {
    return <span className="text-red-500 text-sm">{common('error')}</span>;
  }

  if (!bankAccount || !bankAccount.linked) {
    return (
      <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
        {t('status.notLinked')}
      </Badge>
    );
  }

  return (
    <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
      <Link href={`/banking?account=${bankAccount.id}`} className="flex items-center">
        <Wallet className="mr-1 h-3 w-3" />
        {bankAccount.account_name}
      </Link>
    </Badge>
  );
};

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Tabs,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Skeleton } from "@/components/ui/skeleton";

export default function ChartOfAccountsPage() {
  const t = useTranslations('chartOfAccounts');
  const common = useTranslations('common');

  const { toast } = useToast();
  const {
    canCreateAccount,
    canEditAccount,
    canDeleteAccount,
    isLoading: permissionsLoading,
    error: permissionsError
  } = useUserPermissions();

  // Pagination state
  const [page, setPage] = useState<number>(1);
  const [limit, setLimit] = useState<number>(10);
  const [goToPage, setGoToPage] = useState<string>('');

  // Filtering and sorting state
  const [activeTab, setActiveTab] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [sortBy, setSortBy] = useState<string>("accountCode");
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  // RTK Query hooks
  const {
    data,
    isLoading,
    error: fetchError
  } = useGetAccountsQuery({
    // Pass the active tab as the account type filter
    // The API will handle mapping extended types to base types
    accountType: activeTab !== "all" ? activeTab : undefined,
    search: searchQuery || undefined,
    page,
    limit,
    sortBy,
    sortOrder
  });

  const [createAccount, { isLoading: isCreating }] = useCreateAccountMutation();
  const [updateAccount, { isLoading: isUpdating }] = useUpdateAccountMutation();
  const [deleteAccount, { isLoading: isDeleting }] = useDeleteAccountMutation();

  // Derived state
  const error = fetchError ? (typeof fetchError === 'string' ? fetchError : 'Failed to fetch accounts') : null;

  // Dialog states
  const [isAddDialogOpen, setIsAddDialogOpen] = useState<boolean>(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState<boolean>(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState<boolean>(false);
  const [isBankLinkDialogOpen, setIsBankLinkDialogOpen] = useState<boolean>(false);
  const [selectedAccount, setSelectedAccount] = useState<Account | null>(null);

  // Form states
  const [accountCode, setAccountCode] = useState<string>('');
  const [accountName, setAccountName] = useState<string>('');
  const [accountType, setAccountType] = useState<AccountType>('Asset');
  const [accountDescription, setAccountDescription] = useState<string>('');
  const [accountIsActive, setAccountIsActive] = useState<boolean>(true);
  const [submitError, setSubmitError] = useState<string | null>(null);

  const isSubmitting = isCreating || isUpdating || isDeleting;

  // Reset form fields for Add dialog
  const resetAddForm = () => {
    setAccountCode('');
    setAccountName('');
    setAccountType('Asset');
    setAccountDescription('');
    setAccountIsActive(true);
    setSubmitError(null);
  };

  // Reset form fields for Edit dialog is handled in the openEditDialog function

  // Open Add Dialog
  const openAddDialog = () => {
    resetAddForm();
    setIsAddDialogOpen(true);
  };

  // Open Edit Dialog
  const openEditDialog = (account: Account) => {
    setSelectedAccount(account);
    setAccountCode(account.account_code);
    setAccountName(account.account_name);
    setAccountType(account.account_type);
    setAccountDescription(account.description || '');
    setAccountIsActive(account.is_active);
    setSubmitError(null);
    setIsEditDialogOpen(true);
  };

  // Open Delete Dialog
  const openDeleteDialog = (account: Account) => {
    setSelectedAccount(account);
    setIsDeleteDialogOpen(true);
  };

  // Open Bank Link Dialog
  const openBankLinkDialog = (account: Account) => {
    // Only allow linking Asset type accounts
    if (!account.account_type.includes('Asset')) {
      toast({
        title: "Cannot link to bank account",
        description: "Only Asset type accounts can be linked to bank accounts.",
        variant: "destructive",
      });
      return;
    }

    setSelectedAccount(account);
    setIsBankLinkDialogOpen(true);
  };

  // Handle Add form submission
  const handleSaveAccount = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setSubmitError(null);

    const accountData = {
      account_code: accountCode,
      account_name: accountName,
      account_type: accountType,
      description: accountDescription,
      is_active: accountIsActive
    };

    try {
      await createAccount(accountData).unwrap();
      toast({
        title: "Account created",
        description: "The account was created successfully.",
        variant: "default",
      });
      resetAddForm();
      setIsAddDialogOpen(false);
    } catch (err: any) {
      const errorMsg = err.data?.message || err.message || 'An unknown error occurred';
      setSubmitError(errorMsg);
      console.error("Submit error:", err);
    }
  };

  // Handle Edit form submission
  const handleUpdateAccount = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    if (!selectedAccount) return;

    setSubmitError(null);

    const accountData = {
      account_code: accountCode,
      account_name: accountName,
      account_type: accountType,
      description: accountDescription,
      is_active: accountIsActive
    };

    try {
      await updateAccount({
        id: selectedAccount.id,
        body: accountData
      }).unwrap();

      toast({
        title: "Account updated",
        description: "The account was updated successfully.",
        variant: "default",
      });

      setIsEditDialogOpen(false);
      setSelectedAccount(null);
    } catch (err: any) {
      const errorMsg = err.data?.message || err.message || 'An unknown error occurred';
      setSubmitError(errorMsg);
      console.error("Update error:", err);
    }
  };

  // Handle Delete confirmation
  const handleDeleteAccount = async () => {
    if (!selectedAccount) return;

    try {
      await deleteAccount(selectedAccount.id).unwrap();

      toast({
        title: "Account deleted",
        description: "The account was deleted successfully.",
        variant: "default",
      });

      setIsDeleteDialogOpen(false);
      setSelectedAccount(null);
    } catch (err: any) {
      const errorMsg = err.data?.message || err.message || 'An unknown error occurred';

      toast({
        title: "Error",
        description: errorMsg,
        variant: "destructive",
      });

      console.error("Delete error:", err);
    }
  };

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    setPage(1); // Reset to first page when changing tabs
  };

  // Handle search
  const handleSearch = (value: string) => {
    setSearchQuery(value);
    setPage(1); // Reset to first page when searching
  };

  // Handle page size change
  const handlePageSizeChange = (value: string) => {
    const newLimit = parseInt(value, 10);
    setLimit(newLimit);
    setPage(1); // Reset to first page when changing page size
  };

  // Handle go to page
  const handleGoToPage = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const pageNum = parseInt(goToPage, 10);
    if (data && pageNum >= 1 && pageNum <= data.totalPages) {
      setPage(pageNum);
      setGoToPage('');
    } else {
      toast({
        title: "Invalid page number",
        description: `Please enter a page number between 1 and ${data?.totalPages || 1}`,
        variant: "destructive",
      });
    }
  };

  // Handle sorting
  const handleSort = (column: string) => {
    if (sortBy === column) {
      // Toggle sort order if clicking the same column
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new sort column and default to ascending
      setSortBy(column);
      setSortOrder('asc');
    }
  };

  return (
    <div className="container mx-auto py-10">
      <div className="flex flex-col gap-6">
        {/* Header with title and related links */}
        <div className="flex flex-col gap-4">
          <div className="flex justify-between items-center">
            <h1 className="text-3xl font-bold">{t('title')}</h1>
            {!permissionsLoading && (
              canCreateAccount() ? (
                <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                  <DialogTrigger asChild>
                    <Button onClick={openAddDialog}>
                      <Plus className="mr-2 h-4 w-4" />
                      {t('addNewAccount')}
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-[425px]">
                    <DialogHeader>
                      <DialogTitle>{t('dialog.add.title')}</DialogTitle>
                      <DialogDescription>
                        {t('dialog.add.description')}
                      </DialogDescription>
                    </DialogHeader>
                    <form onSubmit={handleSaveAccount}>
                      <div className="grid gap-4 py-4">
                        <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="add-account_code" className="text-right">
                            {t('dialog.form.code')}
                          </Label>
                          <Input
                            id="add-account_code"
                            value={accountCode}
                            onChange={(e) => setAccountCode(e.target.value)}
                            className="col-span-3"
                            required
                            disabled={isSubmitting}
                          />
                        </div>
                        <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="add-account_name" className="text-right">
                            {t('dialog.form.name')}
                          </Label>
                          <Input
                            id="add-account_name"
                            value={accountName}
                            onChange={(e) => setAccountName(e.target.value)}
                            className="col-span-3"
                            required
                            disabled={isSubmitting}
                          />
                        </div>
                        <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="add-account_type" className="text-right">
                            {t('dialog.form.type')}
                          </Label>
                          <Select
                            value={accountType}
                            onValueChange={(value) => setAccountType(value as AccountType)}
                            required
                            disabled={isSubmitting}
                          >
                            <SelectTrigger className="col-span-3">
                              <SelectValue placeholder={t('dialog.form.selectType')} />
                            </SelectTrigger>
                            <SelectContent>
                              {/* Asset Types */}
                              <SelectItem value="Asset">{t('dialog.form.assetTypes.general')}</SelectItem>
                              <SelectItem value="CurrentAsset">{t('dialog.form.assetTypes.current')}</SelectItem>
                              <SelectItem value="FixedAsset">{t('dialog.form.assetTypes.fixed')}</SelectItem>
                              <SelectItem value="IntangibleAsset">{t('dialog.form.assetTypes.intangible')}</SelectItem>
                              <SelectItem value="OtherAsset">{t('dialog.form.assetTypes.other')}</SelectItem>

                              {/* Liability Types */}
                              <SelectItem value="Liability">{t('dialog.form.liabilityTypes.general')}</SelectItem>
                              <SelectItem value="CurrentLiability">{t('dialog.form.liabilityTypes.current')}</SelectItem>
                              <SelectItem value="LongTermLiability">{t('dialog.form.liabilityTypes.longTerm')}</SelectItem>
                              <SelectItem value="OtherLiability">{t('dialog.form.liabilityTypes.other')}</SelectItem>

                              {/* Equity Types */}
                              <SelectItem value="Equity">{t('dialog.form.equityTypes.general')}</SelectItem>
                              <SelectItem value="CommonStock">{t('dialog.form.equityTypes.commonStock')}</SelectItem>
                              <SelectItem value="RetainedEarnings">{t('dialog.form.equityTypes.retainedEarnings')}</SelectItem>
                              <SelectItem value="OtherEquity">{t('dialog.form.equityTypes.other')}</SelectItem>

                              {/* Revenue Types */}
                              <SelectItem value="Revenue">{t('dialog.form.revenueTypes.general')}</SelectItem>
                              <SelectItem value="OperatingRevenue">{t('dialog.form.revenueTypes.operating')}</SelectItem>
                              <SelectItem value="NonOperatingRevenue">{t('dialog.form.revenueTypes.nonOperating')}</SelectItem>
                              <SelectItem value="OtherRevenue">{t('dialog.form.revenueTypes.other')}</SelectItem>

                              {/* Expense Types */}
                              <SelectItem value="Expense">{t('dialog.form.expenseTypes.general')}</SelectItem>
                              <SelectItem value="OperatingExpense">{t('dialog.form.expenseTypes.operating')}</SelectItem>
                              <SelectItem value="NonOperatingExpense">{t('dialog.form.expenseTypes.nonOperating')}</SelectItem>
                              <SelectItem value="OtherExpense">{t('dialog.form.expenseTypes.other')}</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="add-account_description" className="text-right">
                            {t('dialog.form.description')}
                          </Label>
                          <Input
                            id="add-account_description"
                            value={accountDescription}
                            onChange={(e) => setAccountDescription(e.target.value)}
                            className="col-span-3"
                            disabled={isSubmitting}
                          />
                        </div>
                        {submitError && <p className="col-span-4 text-red-600 text-sm">{`Error: ${submitError}`}</p>}
                      </div>
                      <DialogFooter>
                        <DialogClose asChild>
                          <Button type="button" variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                            {t('dialog.buttons.cancel')}
                          </Button>
                        </DialogClose>
                        <Button type="submit" disabled={isSubmitting}>
                          {isSubmitting ? t('dialog.buttons.saving') : t('dialog.buttons.save')}
                        </Button>
                      </DialogFooter>
                    </form>
                  </DialogContent>
                </Dialog>
              ) : (
                <div className="relative inline-block group">
                  <Button disabled>
                    <Plus className="mr-2 h-4 w-4" />
                    {t('addNewAccount')}
                  </Button>
                  <div className="absolute bottom-full mb-2 right-0 bg-black text-white text-xs rounded py-1 px-2 whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity">
                    {t('permissions.staffRequired')}
                  </div>
                </div>
              )
            )}
          </div>

          <div className="flex flex-wrap gap-3">
            <Button variant="outline" size="sm" asChild>
              <Link href="/journal-entries">
                <FileText className="mr-2 h-4 w-4" />
                {t('journalEntries')}
              </Link>
            </Button>
            <Button variant="outline" size="sm" asChild>
              <Link href="/banking">
                <Wallet className="mr-2 h-4 w-4" />
                {t('banking')}
              </Link>
            </Button>
            <Button variant="outline" size="sm" asChild>
              <Link href="/assets">
                <Building2 className="mr-2 h-4 w-4" />
                {t('assets')}
              </Link>
            </Button>
          </div>
        </div>

        {/* Search and filter section */}
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="relative w-full sm:w-64">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={t('searchPlaceholder')}
              className="pl-8"
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
            />
          </div>

          <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full sm:w-auto">
            <TabsList className="grid grid-cols-6">
              <TabsTrigger value="all">{t('tabs.all')}</TabsTrigger>
              <TabsTrigger value="Asset">{t('tabs.assets')}</TabsTrigger>
              <TabsTrigger value="Liability">{t('tabs.liabilities')}</TabsTrigger>
              <TabsTrigger value="Equity">{t('tabs.equity')}</TabsTrigger>
              <TabsTrigger value="Revenue">{t('tabs.revenue')}</TabsTrigger>
              <TabsTrigger value="Expense">{t('tabs.expenses')}</TabsTrigger>
            </TabsList>

            {/* Asset Subtypes */}
            {activeTab === 'Asset' && (
              <TabsList className="mt-2">
                <TabsTrigger value="CurrentAsset">{t('tabs.assetSubtypes.current')}</TabsTrigger>
                <TabsTrigger value="FixedAsset">{t('tabs.assetSubtypes.fixed')}</TabsTrigger>
                <TabsTrigger value="IntangibleAsset">{t('tabs.assetSubtypes.intangible')}</TabsTrigger>
                <TabsTrigger value="OtherAsset">{t('tabs.assetSubtypes.other')}</TabsTrigger>
              </TabsList>
            )}

            {/* Liability Subtypes */}
            {activeTab === 'Liability' && (
              <TabsList className="mt-2">
                <TabsTrigger value="CurrentLiability">{t('tabs.liabilitySubtypes.current')}</TabsTrigger>
                <TabsTrigger value="LongTermLiability">{t('tabs.liabilitySubtypes.longTerm')}</TabsTrigger>
                <TabsTrigger value="OtherLiability">{t('tabs.liabilitySubtypes.other')}</TabsTrigger>
              </TabsList>
            )}

            {/* Equity Subtypes */}
            {activeTab === 'Equity' && (
              <TabsList className="mt-2">
                <TabsTrigger value="CommonStock">{t('tabs.equitySubtypes.commonStock')}</TabsTrigger>
                <TabsTrigger value="RetainedEarnings">{t('tabs.equitySubtypes.retainedEarnings')}</TabsTrigger>
                <TabsTrigger value="OtherEquity">{t('tabs.equitySubtypes.other')}</TabsTrigger>
              </TabsList>
            )}

            {/* Revenue Subtypes */}
            {activeTab === 'Revenue' && (
              <TabsList className="mt-2">
                <TabsTrigger value="OperatingRevenue">{t('tabs.revenueSubtypes.operating')}</TabsTrigger>
                <TabsTrigger value="NonOperatingRevenue">{t('tabs.revenueSubtypes.nonOperating')}</TabsTrigger>
                <TabsTrigger value="OtherRevenue">{t('tabs.revenueSubtypes.other')}</TabsTrigger>
              </TabsList>
            )}

            {/* Expense Subtypes */}
            {activeTab === 'Expense' && (
              <TabsList className="mt-2">
                <TabsTrigger value="OperatingExpense">{t('tabs.expenseSubtypes.operating')}</TabsTrigger>
                <TabsTrigger value="NonOperatingExpense">{t('tabs.expenseSubtypes.nonOperating')}</TabsTrigger>
                <TabsTrigger value="OtherExpense">{t('tabs.expenseSubtypes.other')}</TabsTrigger>
              </TabsList>
            )}
          </Tabs>
        </div>

        {permissionsLoading && (
          <div className="space-y-2">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
          </div>
        )}

        {permissionsError && (
          <div className="bg-red-50 border border-red-200 text-red-700 p-4 rounded-md">
            <h3 className="text-lg font-semibold mb-2">{t('permissions.error.title')}</h3>
            <p>{permissionsError}</p>
            <p className="mt-2 text-sm">{t('permissions.error.message')}</p>
          </div>
        )}

        {isLoading && !permissionsLoading && (
          <div className="space-y-2">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
          </div>
        )}

        {error && !permissionsError && (
          <div className="bg-red-50 border border-red-200 text-red-700 p-4 rounded-md">
            <h3 className="text-lg font-semibold mb-2">Error</h3>
            <p>Error loading accounts: {error}</p>
          </div>
        )}

        {!isLoading && !error && data && (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>{t('table.title')}</CardTitle>
              <CardDescription>
                {t('table.accountsFound', { count: data.total })}
                {activeTab !== "all" ? t('table.inCategory', { category: activeTab }) : ''}
                {searchQuery ? t('table.matching', { query: searchQuery }) : ''}
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => handleSort('accountCode')}
                    >
                      <div className="flex items-center">
                        {t('table.code')}
                        {sortBy === 'accountCode' && (
                          <span className="ml-1">
                            {sortOrder === 'asc' ? '↑' : '↓'}
                          </span>
                        )}
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => handleSort('accountName')}
                    >
                      <div className="flex items-center">
                        {t('table.name')}
                        {sortBy === 'accountName' && (
                          <span className="ml-1">
                            {sortOrder === 'asc' ? '↑' : '↓'}
                          </span>
                        )}
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => handleSort('accountType')}
                    >
                      <div className="flex items-center">
                        {t('table.type')}
                        {sortBy === 'accountType' && (
                          <span className="ml-1">
                            {sortOrder === 'asc' ? '↑' : '↓'}
                          </span>
                        )}
                      </div>
                    </TableHead>
                    <TableHead>{t('table.description')}</TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => handleSort('isActive')}
                    >
                      <div className="flex items-center">
                        {t('table.status')}
                        {sortBy === 'isActive' && (
                          <span className="ml-1">
                            {sortOrder === 'asc' ? '↑' : '↓'}
                          </span>
                        )}
                      </div>
                    </TableHead>
                    <TableHead>{t('table.banking')}</TableHead>
                    <TableHead className="text-right">{t('table.actions')}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {data.data && data.data.length > 0 ? (
                    data.data.map((account) => (
                      <TableRow key={account.id}>
                        <TableCell className="font-medium">{account.account_code}</TableCell>
                        <TableCell>{account.account_name}</TableCell>
                        <TableCell>
                          <Badge variant={
                            // Use only the variants that are defined in the Badge component
                            account.account_type.startsWith('Asset') ? 'default' :
                            account.account_type.startsWith('Liability') ? 'destructive' :
                            account.account_type.startsWith('Equity') ? 'secondary' :
                            'outline'
                          }
                          className={`
                            ${account.account_type.startsWith('Revenue') ? 'bg-green-50 border-green-200 text-green-700' : ''}
                            ${account.account_type.startsWith('Expense') ? 'bg-orange-50 border-orange-200 text-orange-700' : ''}

                            ${account.account_type.includes('Current') ? 'bg-blue-50 border-blue-200 text-blue-700' : ''}
                            ${account.account_type.includes('Fixed') ? 'bg-indigo-50 border-indigo-200 text-indigo-700' : ''}
                            ${account.account_type.includes('Intangible') ? 'bg-purple-50 border-purple-200 text-purple-700' : ''}
                            ${account.account_type.includes('LongTerm') ? 'bg-pink-50 border-pink-200 text-pink-700' : ''}
                            ${account.account_type.includes('Operating') ? 'bg-emerald-50 border-emerald-200 text-emerald-700' : ''}
                            ${account.account_type.includes('NonOperating') ? 'bg-amber-50 border-amber-200 text-amber-700' : ''}
                            ${account.account_type.includes('Other') ? 'bg-gray-50 border-gray-200 text-gray-700' : ''}
                          `}
                          >
                            {account.account_type}
                          </Badge>
                        </TableCell>
                        <TableCell>{account.description || '-'}</TableCell>
                        <TableCell>
                          {account.is_active ?
                            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">{t('status.active')}</Badge> :
                            <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">{t('status.inactive')}</Badge>
                          }
                        </TableCell>
                        <TableCell>
                          {account.account_type.includes('Asset') ? (
                            <BankAccountStatus accountId={account.id} />
                          ) : (
                            <span className="text-gray-400 text-sm">{t('status.na')}</span>
                          )}
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>{t('table.actions')}</DropdownMenuLabel>
                              {canEditAccount() ? (
                                <DropdownMenuItem onClick={() => openEditDialog(account)}>
                                  <Edit className="mr-2 h-4 w-4" />
                                  {common('edit')}
                                </DropdownMenuItem>
                              ) : (
                                <DropdownMenuItem disabled className="text-muted-foreground">
                                  <Edit className="mr-2 h-4 w-4" />
                                  {common('edit')} ({t('permissions.managerRequired')})
                                </DropdownMenuItem>
                              )}

                              {/* Bank Account Link Option - Only for Asset accounts */}
                              {canEditAccount() && account.account_type.includes('Asset') ? (
                                <DropdownMenuItem onClick={() => openBankLinkDialog(account)}>
                                  <Link2 className="mr-2 h-4 w-4" />
                                  {t('dialog.bankLink.title')}
                                </DropdownMenuItem>
                              ) : account.account_type.includes('Asset') ? (
                                <DropdownMenuItem disabled className="text-muted-foreground">
                                  <Link2 className="mr-2 h-4 w-4" />
                                  {t('dialog.bankLink.title')} ({t('permissions.managerRequired')})
                                </DropdownMenuItem>
                              ) : null}

                              <DropdownMenuSeparator />

                              {canDeleteAccount() ? (
                                <DropdownMenuItem
                                  onClick={() => openDeleteDialog(account)}
                                  className="text-red-600"
                                >
                                  <Trash className="mr-2 h-4 w-4" />
                                  {common('delete')}
                                </DropdownMenuItem>
                              ) : (
                                <DropdownMenuItem disabled className="text-muted-foreground">
                                  <Trash className="mr-2 h-4 w-4" />
                                  {common('delete')} ({t('permissions.adminRequired')})
                                </DropdownMenuItem>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} className="h-24 text-center">
                        {t('table.noAccountsFound')} {canCreateAccount() && t('table.addOnePrompt')}
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
            <CardFooter className="flex flex-col sm:flex-row justify-between items-center gap-4 border-t p-4">
              <div className="flex flex-col sm:flex-row items-center gap-4 w-full sm:w-auto">
                <div className="text-sm text-muted-foreground">
                  {t('table.showingAccounts', { showing: data.data?.length || 0, total: data.total })}
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-muted-foreground">{t('table.rowsPerPage')}</span>
                  <Select value={limit.toString()} onValueChange={handlePageSizeChange}>
                    <SelectTrigger className="w-16">
                      <SelectValue placeholder={limit.toString()} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="5">5</SelectItem>
                      <SelectItem value="10">10</SelectItem>
                      <SelectItem value="20">20</SelectItem>
                      <SelectItem value="50">50</SelectItem>
                      <SelectItem value="100">100</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {data.totalPages > 1 && (
                <div className="flex flex-col sm:flex-row items-center gap-4">
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => setPage(1)}
                      disabled={page === 1 || isLoading}
                      aria-label="First page"
                    >
                      <ChevronsLeft className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => setPage(page - 1)}
                      disabled={page === 1 || isLoading}
                      aria-label="Previous page"
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <span className="text-sm min-w-[100px] text-center">
                      {t('table.page', { current: page, total: data.totalPages })}
                    </span>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => setPage(page + 1)}
                      disabled={page === data.totalPages || isLoading}
                      aria-label="Next page"
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => setPage(data.totalPages)}
                      disabled={page === data.totalPages || isLoading}
                      aria-label="Last page"
                    >
                      <ChevronsRight className="h-4 w-4" />
                    </Button>
                  </div>

                  <form onSubmit={handleGoToPage} className="flex items-center space-x-2">
                    <Input
                      type="number"
                      min="1"
                      max={data.totalPages}
                      value={goToPage}
                      onChange={(e) => setGoToPage(e.target.value)}
                      placeholder={t('table.goToPage')}
                      className="w-20"
                    />
                    <Button type="submit" variant="outline" size="sm" disabled={isLoading}>
                      {common('go')}
                    </Button>
                  </form>
                </div>
              )}
            </CardFooter>
          </Card>
        )}
      </div>

      {/* Edit Account Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>{t('dialog.edit.title')}</DialogTitle>
            <DialogDescription>
              {t('dialog.edit.description')}
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleUpdateAccount}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-account_code" className="text-right">
                  {t('dialog.form.code')}
                </Label>
                <Input
                  id="edit-account_code"
                  value={accountCode}
                  onChange={(e) => setAccountCode(e.target.value)}
                  className="col-span-3"
                  required
                  disabled={isSubmitting}
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-account_name" className="text-right">
                  {t('dialog.form.name')}
                </Label>
                <Input
                  id="edit-account_name"
                  value={accountName}
                  onChange={(e) => setAccountName(e.target.value)}
                  className="col-span-3"
                  required
                  disabled={isSubmitting}
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-account_type" className="text-right">
                  {t('dialog.form.type')}
                </Label>
                <Select
                  value={accountType}
                  onValueChange={(value) => setAccountType(value as AccountType)}
                  required
                  disabled={isSubmitting}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder={t('dialog.form.selectType')} />
                  </SelectTrigger>
                  <SelectContent>
                    {/* Asset Types */}
                    <SelectItem value="Asset">{t('dialog.form.assetTypes.general')}</SelectItem>
                    <SelectItem value="CurrentAsset">{t('dialog.form.assetTypes.current')}</SelectItem>
                    <SelectItem value="FixedAsset">{t('dialog.form.assetTypes.fixed')}</SelectItem>
                    <SelectItem value="IntangibleAsset">{t('dialog.form.assetTypes.intangible')}</SelectItem>
                    <SelectItem value="OtherAsset">{t('dialog.form.assetTypes.other')}</SelectItem>

                    {/* Liability Types */}
                    <SelectItem value="Liability">{t('dialog.form.liabilityTypes.general')}</SelectItem>
                    <SelectItem value="CurrentLiability">{t('dialog.form.liabilityTypes.current')}</SelectItem>
                    <SelectItem value="LongTermLiability">{t('dialog.form.liabilityTypes.longTerm')}</SelectItem>
                    <SelectItem value="OtherLiability">{t('dialog.form.liabilityTypes.other')}</SelectItem>

                    {/* Equity Types */}
                    <SelectItem value="Equity">{t('dialog.form.equityTypes.general')}</SelectItem>
                    <SelectItem value="CommonStock">{t('dialog.form.equityTypes.commonStock')}</SelectItem>
                    <SelectItem value="RetainedEarnings">{t('dialog.form.equityTypes.retainedEarnings')}</SelectItem>
                    <SelectItem value="OtherEquity">{t('dialog.form.equityTypes.other')}</SelectItem>

                    {/* Revenue Types */}
                    <SelectItem value="Revenue">{t('dialog.form.revenueTypes.general')}</SelectItem>
                    <SelectItem value="OperatingRevenue">{t('dialog.form.revenueTypes.operating')}</SelectItem>
                    <SelectItem value="NonOperatingRevenue">{t('dialog.form.revenueTypes.nonOperating')}</SelectItem>
                    <SelectItem value="OtherRevenue">{t('dialog.form.revenueTypes.other')}</SelectItem>

                    {/* Expense Types */}
                    <SelectItem value="Expense">{t('dialog.form.expenseTypes.general')}</SelectItem>
                    <SelectItem value="OperatingExpense">{t('dialog.form.expenseTypes.operating')}</SelectItem>
                    <SelectItem value="NonOperatingExpense">{t('dialog.form.expenseTypes.nonOperating')}</SelectItem>
                    <SelectItem value="OtherExpense">{t('dialog.form.expenseTypes.other')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-account_description" className="text-right">
                  {t('dialog.form.description')}
                </Label>
                <Input
                  id="edit-account_description"
                  value={accountDescription}
                  onChange={(e) => setAccountDescription(e.target.value)}
                  className="col-span-3"
                  disabled={isSubmitting}
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-account_status" className="text-right">
                  {t('dialog.form.status')}
                </Label>
                <Select
                  value={accountIsActive ? "active" : "inactive"}
                  onValueChange={(value) => setAccountIsActive(value === "active")}
                  disabled={isSubmitting}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder={t('dialog.form.selectStatus')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">{t('status.active')}</SelectItem>
                    <SelectItem value="inactive">{t('status.inactive')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              {submitError && <p className="col-span-4 text-red-600 text-sm">{`Error: ${submitError}`}</p>}
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                {t('dialog.buttons.cancel')}
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? t('dialog.buttons.updating') : t('dialog.buttons.update')}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Account Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('dialog.delete.title')}</AlertDialogTitle>
            <AlertDialogDescription>
              {selectedAccount && t('dialog.delete.description', {
                code: selectedAccount.account_code,
                name: selectedAccount.account_name
              })}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              {t('dialog.buttons.cancel')}
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteAccount}
              disabled={isSubmitting}
            >
              {isSubmitting ? t('dialog.buttons.deleting') : t('dialog.buttons.delete')}
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Bank Account Link Dialog */}
      {selectedAccount && isBankLinkDialogOpen && (
        <BankAccountLinkDialog
          isOpen={isBankLinkDialogOpen}
          onClose={(refresh) => {
            setIsBankLinkDialogOpen(false);
            if (refresh) {
              // Refresh the accounts list
              toast({
                title: common('success'),
                description: t('dialog.messages.linkSuccess'),
                variant: "default",
              });
            }
          }}
          chartOfAccount={selectedAccount}
        />
      )}
    </div>
  );
}
