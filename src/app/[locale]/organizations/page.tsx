'use client';

import React, { useState, useCallback, useMemo } from 'react';
import { useGetOrganizationsQuery, useCreateOrganizationMutation, useUpdateOrganizationMutation, useDeleteOrganizationMutation } from '@/redux/services/organizationsApi';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog-fixed';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { toast } from 'sonner';
import { Building2, Plus, MoreHorizontal, Edit, Trash2, MapPin, Users, Calendar, Grid3X3, List, AlertTriangle, ExternalLink } from 'lucide-react';
import Link from 'next/link';
import { formatDistanceToNow, isValid, parseISO } from 'date-fns';
import { useErrorHandler } from '@/hooks/useErrorHandler';

interface Organization {
  id: string;
  name: string;
  slug: string;
  description?: string;
  address?: string;
  phone?: string;
  email?: string;
  website?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  branches?: Array<{
    id: string;
    name: string;
    isActive: boolean;
  }>;
  _count?: {
    branches: number;
    users: number;
  };
}

interface OrganizationForm {
  name: string;
  description: string;
  address: string;
  phone: string;
  email: string;
  website: string;
  isActive: boolean;
}

// Helper function to safely format dates
const formatCreatedDate = (dateString: string) => {
  try {
    const date = parseISO(dateString);
    if (isValid(date)) {
      return formatDistanceToNow(date, { addSuffix: true });
    }
    return 'Unknown date';
  } catch (error) {
    console.warn('Invalid date format:', dateString);
    return 'Unknown date';
  }
};

// Helper function to generate slug from organization name
const generateSlug = (name: string): string => {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .trim();
};

export default function OrganizationsPage() {
  // Error handler hook
  const { handleError } = useErrorHandler();

  // State for dialogs and forms
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentOrganization, setCurrentOrganization] = useState<Organization | null>(null);

  // State for view mode (grid or list)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // State for delete confirmation
  const [deleteConfirmation, setDeleteConfirmation] = useState('');

  // Form state
  const [organizationForm, setOrganizationForm] = useState<OrganizationForm>({
    name: '',
    description: '',
    address: '',
    phone: '',
    email: '',
    website: '',
    isActive: true,
  });

  // RTK Query hooks
  const { data, isLoading, isFetching } = useGetOrganizationsQuery(undefined, {
    // Reduce refetching frequency
    refetchOnMountOrArgChange: 30, // Only refetch if data is older than 30 seconds
    refetchOnFocus: false, // Don't refetch when window gains focus
    refetchOnReconnect: true, // Refetch when reconnecting
  });
  const [createOrganization, { isLoading: isCreating }] = useCreateOrganizationMutation();
  const [updateOrganization, { isLoading: isUpdating }] = useUpdateOrganizationMutation();
  const [deleteOrganization, { isLoading: isDeleting }] = useDeleteOrganizationMutation();

  // Memoize organizations array to prevent unnecessary re-renders
  const organizations = useMemo(() => data?.data || [], [data?.data]);

  // Form handlers with useCallback to prevent re-renders
  const resetForm = useCallback(() => {
    setOrganizationForm({
      name: '',
      description: '',
      address: '',
      phone: '',
      email: '',
      website: '',
      isActive: true,
    });
  }, []);

  const openCreateDialog = useCallback(() => {
    resetForm();
    setIsCreateDialogOpen(true);
  }, [resetForm]);

  const openEditDialog = useCallback((organization: Organization) => {
    setCurrentOrganization(organization);
    setOrganizationForm({
      name: organization.name,
      description: organization.description || '',
      address: organization.address || '',
      phone: organization.phone || '',
      email: organization.email || '',
      website: organization.website || '',
      isActive: organization.isActive,
    });
    setIsEditDialogOpen(true);
  }, []);

  const openDeleteDialog = useCallback((organization: Organization) => {
    setCurrentOrganization(organization);
    setDeleteConfirmation('');
    setIsDeleteDialogOpen(true);
  }, []);

  const handleCreateOrganization = useCallback(async () => {
    try {
      await createOrganization(organizationForm).unwrap();
      toast.success('Organization created successfully');
      setIsCreateDialogOpen(false);
      resetForm();
    } catch (error) {
      handleError(error, 'Failed to create organization');
    }
  }, [createOrganization, organizationForm, resetForm, handleError]);

  const handleUpdateOrganization = useCallback(async () => {
    if (!currentOrganization) return;

    try {
      await updateOrganization({
        id: currentOrganization.id,
        data: organizationForm,
      }).unwrap();
      toast.success('Organization updated successfully');
      setIsEditDialogOpen(false);
      setCurrentOrganization(null);
      resetForm();
    } catch (error) {
      handleError(error, 'Failed to update organization');
    }
  }, [updateOrganization, currentOrganization, organizationForm, resetForm, handleError]);

  const handleDeleteOrganization = useCallback(async () => {
    if (!currentOrganization) return;

    // Confirmation check - user must type the organization name
    if (deleteConfirmation !== currentOrganization.name) {
      toast.error('Please type the organization name to confirm deletion');
      return;
    }

    try {
      await deleteOrganization(currentOrganization.id).unwrap();
      toast.success('Organization deleted successfully');
      setIsDeleteDialogOpen(false);
      setCurrentOrganization(null);
      setDeleteConfirmation('');
    } catch (error) {
      handleError(error, 'Failed to delete organization');
    }
  }, [deleteOrganization, currentOrganization, deleteConfirmation, handleError]);

  const handleInputChange = useCallback((field: keyof OrganizationForm, value: string | boolean) => {
    setOrganizationForm(prev => ({
      ...prev,
      [field]: value,
    }));
  }, []);

  if (isLoading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex justify-center items-center h-64">
          <div className="text-lg">Loading organizations...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Building2 className="h-8 w-8" />
            Organizations
          </h1>
          <p className="text-muted-foreground mt-2">
            Manage your organizations and their settings
          </p>
        </div>
        <div className="flex items-center gap-3">
          {/* Manage Branches Button */}
          <Link href="/en/organizations/test-restaurant-group/branches">
            <Button variant="outline" className="flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              Manage Branches
              <ExternalLink className="h-3 w-3" />
            </Button>
          </Link>

          {/* View Mode Toggle */}
          <div className="flex items-center border rounded-lg p-1">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('grid')}
              className="h-8 px-3"
            >
              <Grid3X3 className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
              className="h-8 px-3"
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
          <Button onClick={openCreateDialog}>
            <Plus className="mr-2 h-4 w-4" />
            Add Organization
          </Button>
        </div>
      </div>

      {/* Organizations Content - Grid or List View */}
      {viewMode === 'grid' ? (
        /* Grid View */
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-8">
          {organizations.map((organization) => (
            <Card key={organization.id} className="relative">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-2">
                    <Building2 className="h-5 w-5 text-primary" />
                    <CardTitle className="text-lg">{organization.name}</CardTitle>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant={organization.isActive ? "default" : "secondary"}>
                      {organization.isActive ? "Active" : "Inactive"}
                    </Badge>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <Link href={`/en/organizations/${organization.slug || generateSlug(organization.name)}/branches`} className="flex items-center">
                            <MapPin className="mr-2 h-4 w-4" />
                            View Branches
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => openEditDialog(organization)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => openDeleteDialog(organization)}
                          className="text-destructive"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
                {organization.description && (
                  <CardDescription>{organization.description}</CardDescription>
                )}
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm">
                  {organization.email && (
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <span>📧</span>
                      <span>{organization.email}</span>
                    </div>
                  )}
                  {organization.phone && (
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <span>📞</span>
                      <span>{organization.phone}</span>
                    </div>
                  )}
                  <div className="flex items-center gap-4 pt-2">
                    <div className="flex items-center gap-1">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground">
                        {organization._count?.branches || 0} branches
                      </span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground">
                        {organization._count?.users || 0} users
                      </span>
                    </div>
                  </div>
                  {organization.createdAt && (
                    <div className="flex items-center gap-1 pt-1">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground text-xs">
                        Created {formatCreatedDate(organization.createdAt)}
                      </span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        /* List View */
        <div className="mb-8">
          <Card>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Organization</TableHead>
                  <TableHead>Contact</TableHead>
                  <TableHead>Branches</TableHead>
                  <TableHead>Users</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead className="w-[70px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {organizations.map((organization) => (
                  <TableRow key={organization.id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Building2 className="h-4 w-4 text-primary" />
                        <div>
                          <div className="font-medium">{organization.name}</div>
                          {organization.description && (
                            <div className="text-sm text-muted-foreground truncate max-w-[200px]">
                              {organization.description}
                            </div>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        {organization.email && (
                          <div className="text-sm">{organization.email}</div>
                        )}
                        {organization.phone && (
                          <div className="text-sm text-muted-foreground">{organization.phone}</div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <MapPin className="h-4 w-4 text-muted-foreground" />
                        <span>{organization._count?.branches || 0}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Users className="h-4 w-4 text-muted-foreground" />
                        <span>{organization._count?.users || 0}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={organization.isActive ? "default" : "secondary"}>
                        {organization.isActive ? "Active" : "Inactive"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {organization.createdAt && (
                        <div className="text-sm text-muted-foreground">
                          {formatCreatedDate(organization.createdAt)}
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem asChild>
                            <Link href={`/en/organizations/${organization.slug || generateSlug(organization.name)}/branches`} className="flex items-center">
                              <MapPin className="mr-2 h-4 w-4" />
                              View Branches
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => openEditDialog(organization)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => openDeleteDialog(organization)}
                            className="text-destructive"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </Card>
        </div>
      )}

      {organizations.length === 0 && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Building2 className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No organizations found</h3>
            <p className="text-muted-foreground text-center mb-4">
              Get started by creating your first organization
            </p>
            <Button onClick={openCreateDialog}>
              <Plus className="mr-2 h-4 w-4" />
              Create Organization
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Create Organization Dialog */}
      <Dialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
      >
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Create New Organization</DialogTitle>
            <DialogDescription>
              Add a new organization to your account. You can manage branches and users for each organization.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="name">Organization Name *</Label>
              <Input
                id="name"
                value={organizationForm.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Enter organization name"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={organizationForm.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Enter organization description"
                rows={3}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={organizationForm.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                placeholder="Enter organization email"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="phone">Phone</Label>
              <Input
                id="phone"
                value={organizationForm.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                placeholder="Enter phone number"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="website">Website</Label>
              <Input
                id="website"
                value={organizationForm.website}
                onChange={(e) => handleInputChange('website', e.target.value)}
                placeholder="Enter website URL"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="address">Address</Label>
              <Textarea
                id="address"
                value={organizationForm.address}
                onChange={(e) => handleInputChange('address', e.target.value)}
                placeholder="Enter organization address"
                rows={2}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateOrganization} disabled={isCreating || !organizationForm.name.trim()}>
              {isCreating ? 'Creating...' : 'Create Organization'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Organization Dialog */}
      <Dialog
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
      >
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit Organization</DialogTitle>
            <DialogDescription>
              Update the organization information and settings.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="edit-name">Organization Name *</Label>
              <Input
                id="edit-name"
                value={organizationForm.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Enter organization name"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit-description">Description</Label>
              <Textarea
                id="edit-description"
                value={organizationForm.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Enter organization description"
                rows={3}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit-email">Email</Label>
              <Input
                id="edit-email"
                type="email"
                value={organizationForm.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                placeholder="Enter organization email"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit-phone">Phone</Label>
              <Input
                id="edit-phone"
                value={organizationForm.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                placeholder="Enter phone number"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit-website">Website</Label>
              <Input
                id="edit-website"
                value={organizationForm.website}
                onChange={(e) => handleInputChange('website', e.target.value)}
                placeholder="Enter website URL"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit-address">Address</Label>
              <Textarea
                id="edit-address"
                value={organizationForm.address}
                onChange={(e) => handleInputChange('address', e.target.value)}
                placeholder="Enter organization address"
                rows={2}
              />
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="edit-isActive"
                checked={organizationForm.isActive}
                onChange={(e) => handleInputChange('isActive', e.target.checked)}
                className="rounded"
              />
              <Label htmlFor="edit-isActive">Active</Label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateOrganization} disabled={isUpdating || !organizationForm.name.trim()}>
              {isUpdating ? 'Updating...' : 'Update Organization'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Organization Dialog */}
      <Dialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
      >
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <div className="flex items-center gap-3">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-destructive/10">
                <AlertTriangle className="h-6 w-6 text-destructive" />
              </div>
              <div>
                <DialogTitle className="text-destructive">Delete Organization</DialogTitle>
                <DialogDescription className="mt-1">
                  This action cannot be undone
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="rounded-lg border border-destructive/20 bg-destructive/5 p-4">
              <h4 className="font-medium text-destructive mb-2">⚠️ Warning: Permanent Deletion</h4>
              <p className="text-sm text-muted-foreground mb-3">
                You are about to permanently delete <strong>"{currentOrganization?.name}"</strong>
              </p>
              <div className="text-sm text-muted-foreground space-y-1">
                <div>• All organization data will be permanently deleted</div>
                <div>• All associated branches will be removed</div>
                <div>• All user permissions will be revoked</div>
                <div>• This action cannot be undone</div>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="delete-confirmation" className="text-sm font-medium">
                Type <span className="font-mono bg-muted px-1 rounded">{currentOrganization?.name}</span> to confirm:
              </Label>
              <Input
                id="delete-confirmation"
                value={deleteConfirmation}
                onChange={(e) => setDeleteConfirmation(e.target.value)}
                placeholder={`Type "${currentOrganization?.name}" here`}
                className="font-mono"
              />
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setIsDeleteDialogOpen(false);
                setDeleteConfirmation('');
              }}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteOrganization}
              disabled={isDeleting || deleteConfirmation !== currentOrganization?.name}
            >
              {isDeleting ? 'Deleting...' : 'Delete Organization'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
