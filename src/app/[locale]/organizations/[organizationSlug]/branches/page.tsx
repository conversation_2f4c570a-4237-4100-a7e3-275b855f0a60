'use client';

import React, { useState, useCallback, useMemo } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { toast } from 'sonner';
import { Building2, Plus, MoreHorizontal, Edit, Trash2, MapPin, Users, Calendar, Grid3X3, List, AlertTriangle, ArrowLeft } from 'lucide-react';
import Link from 'next/link';

// UI Components
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

// Types
interface Organization {
  id: string;
  name: string;
  slug: string;
  description?: string;
  isActive: boolean;
}

interface Branch {
  id: string;
  name: string;
  description?: string;
  address?: string;
  phone?: string;
  email?: string;
  isActive: boolean;
  organizationId: string;
  branchCode?: string;
  managerName?: string;
  createdAt: string;
  updatedAt: string;
  _count?: {
    users?: number;
  };
}

interface BranchForm {
  name: string;
  description: string;
  address: string;
  phone: string;
  email: string;
  isActive: boolean;
  branchCode: string;
  managerName: string;
}

// Mock data for demonstration
const mockOrganization: Organization = {
  id: 'org1',
  name: 'Test Restaurant Group',
  slug: 'test-restaurant-group',
  description: 'A leading restaurant chain with multiple locations',
  isActive: true
};

const mockBranches: Branch[] = [
  {
    id: '1',
    name: 'Downtown Branch',
    description: 'Main downtown location',
    address: '123 Main St, Downtown, City, State 12345',
    phone: '******-123-4567',
    email: '<EMAIL>',
    isActive: true,
    organizationId: 'org1',
    branchCode: 'DT001',
    managerName: 'John Smith',
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
    _count: { users: 5 }
  },
  {
    id: '2',
    name: 'Uptown Branch',
    description: 'Secondary uptown location',
    address: '456 Second Ave, Uptown, City, State 67890',
    phone: '******-987-6543',
    email: '<EMAIL>',
    isActive: true,
    organizationId: 'org1',
    branchCode: 'UT002',
    managerName: 'Jane Doe',
    createdAt: '2024-02-01T14:30:00Z',
    updatedAt: '2024-02-01T14:30:00Z',
    _count: { users: 3 }
  }
];

export default function OrganizationBranchesPage() {
  const params = useParams();
  const router = useRouter();
  const t = useTranslations();

  const organizationSlug = params.organizationSlug as string;
  const locale = params.locale as string;

  // State for dialogs and forms
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentBranch, setCurrentBranch] = useState<Branch | null>(null);

  // State for view mode (grid or list)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // State for delete confirmation
  const [deleteConfirmation, setDeleteConfirmation] = useState('');

  // Form state
  const [branchForm, setBranchForm] = useState<BranchForm>({
    name: '',
    description: '',
    address: '',
    phone: '',
    email: '',
    isActive: true,
    branchCode: '',
    managerName: ''
  });

  // Loading states
  const [isCreating, setIsCreating] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Mock data - replace with actual API calls
  const organization = useMemo(() => mockOrganization, []);
  const branches = useMemo(() => mockBranches, []);
  const isLoading = false;

  // Helper functions
  const formatCreatedDate = useCallback((dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  }, []);

  const resetForm = useCallback(() => {
    setBranchForm({
      name: '',
      description: '',
      address: '',
      phone: '',
      email: '',
      isActive: true,
      branchCode: '',
      managerName: ''
    });
  }, []);

  const handleError = useCallback((error: any, defaultMessage: string) => {
    console.error('Branch operation error:', error);
    toast.error(defaultMessage);
  }, []);

  // Dialog handlers
  const openCreateDialog = useCallback(() => {
    resetForm();
    setIsCreateDialogOpen(true);
  }, [resetForm]);

  const openEditDialog = useCallback((branch: Branch) => {
    setBranchForm({
      name: branch.name,
      description: branch.description || '',
      address: branch.address || '',
      phone: branch.phone || '',
      email: branch.email || '',
      isActive: branch.isActive,
      branchCode: branch.branchCode || '',
      managerName: branch.managerName || ''
    });
    setCurrentBranch(branch);
    setIsEditDialogOpen(true);
  }, []);

  const openDeleteDialog = useCallback((branch: Branch) => {
    setCurrentBranch(branch);
    setDeleteConfirmation('');
    setIsDeleteDialogOpen(true);
  }, []);

  // Form handlers
  const handleInputChange = useCallback((field: keyof BranchForm, value: string | boolean) => {
    setBranchForm(prev => ({
      ...prev,
      [field]: value
    }));
  }, []);

  const handleCreateBranch = useCallback(async () => {
    if (isCreating) return;

    setIsCreating(true);
    try {
      // Prepare branch data for API
      const branchData = {
        name: branchForm.name.trim(),
        description: branchForm.description.trim() || null,
        address: branchForm.address.trim() || null,
        phone: branchForm.phone.trim() || null,
        email: branchForm.email.trim() || null,
        isActive: branchForm.isActive,
        branchCode: branchForm.branchCode.trim() || null,
        managerName: branchForm.managerName.trim() || null,
        organizationSlug: organizationSlug
      };

      console.log('Creating branch for organization:', organizationSlug, branchData);

      // Make API call to create branch using organization slug endpoint
      const response = await fetch(`/api/organizations/slug/${organizationSlug}/branches`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(branchData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const createdBranch = await response.json();
      console.log('Branch created successfully:', createdBranch);

      toast.success('Branch created successfully');
      setIsCreateDialogOpen(false);
      resetForm();

      // TODO: Refresh the branches list or add the new branch to the state
      // For now, we'll just show success - in a real app, you'd update the branches list

    } catch (error) {
      console.error('Failed to create branch:', error);
      handleError(error, 'Failed to create branch');
    } finally {
      setIsCreating(false);
    }
  }, [isCreating, organizationSlug, branchForm, resetForm, handleError]);

  const handleUpdateBranch = useCallback(async () => {
    if (!currentBranch || isUpdating) return;

    setIsUpdating(true);
    try {
      // Prepare branch data for API
      const branchData = {
        name: branchForm.name.trim(),
        description: branchForm.description.trim() || null,
        address: branchForm.address.trim() || null,
        phone: branchForm.phone.trim() || null,
        email: branchForm.email.trim() || null,
        isActive: branchForm.isActive,
        branchCode: branchForm.branchCode.trim() || null,
        managerName: branchForm.managerName.trim() || null,
        organizationSlug: organizationSlug
      };

      console.log('Updating branch:', currentBranch.id, branchData);

      // Make API call to update branch using organization slug endpoint
      const response = await fetch(`/api/organizations/slug/${organizationSlug}/branches/${currentBranch.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(branchData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const updatedBranch = await response.json();
      console.log('Branch updated successfully:', updatedBranch);

      toast.success('Branch updated successfully');
      setIsEditDialogOpen(false);
      setCurrentBranch(null);
      resetForm();

      // TODO: Refresh the branches list or update the branch in the state

    } catch (error) {
      console.error('Failed to update branch:', error);
      handleError(error, 'Failed to update branch');
    } finally {
      setIsUpdating(false);
    }
  }, [currentBranch, branchForm, organizationSlug, resetForm, handleError, isUpdating]);

  const handleDeleteBranch = useCallback(async () => {
    if (!currentBranch || isDeleting) return;

    // Confirmation check - user must type the branch name
    if (deleteConfirmation !== currentBranch.name) {
      toast.error('Please type the branch name to confirm deletion');
      return;
    }

    setIsDeleting(true);
    try {
      console.log('Deleting branch:', currentBranch.id);

      // Make API call to delete branch using organization slug endpoint
      const response = await fetch(`/api/organizations/slug/${organizationSlug}/branches/${currentBranch.id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      console.log('Branch deleted successfully');

      toast.success('Branch deleted successfully');
      setIsDeleteDialogOpen(false);
      setCurrentBranch(null);
      setDeleteConfirmation('');

      // TODO: Refresh the branches list or remove the branch from the state

    } catch (error) {
      console.error('Failed to delete branch:', error);
      handleError(error, 'Failed to delete branch');
    } finally {
      setIsDeleting(false);
    }
  }, [currentBranch, deleteConfirmation, handleError, isDeleting]);

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">Loading branches...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      {/* Header with Breadcrumb */}
      <div className="flex items-center gap-2 mb-4">
        <Link href={`/${locale}/organizations`}>
          <Button variant="ghost" size="sm" className="flex items-center gap-2">
            <ArrowLeft className="h-4 w-4" />
            Organizations
          </Button>
        </Link>
        <span className="text-muted-foreground">/</span>
        <span className="text-muted-foreground">{organization.name}</span>
        <span className="text-muted-foreground">/</span>
        <span className="font-medium">Branches</span>
      </div>

      {/* Organization Info Card */}
      <Card className="mb-6">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Building2 className="h-6 w-6 text-primary" />
              <div>
                <CardTitle className="text-xl">{organization.name}</CardTitle>
                {organization.description && (
                  <CardDescription className="mt-1">{organization.description}</CardDescription>
                )}
              </div>
            </div>
            <Badge variant={organization.isActive ? "default" : "secondary"}>
              {organization.isActive ? "Active" : "Inactive"}
            </Badge>
          </div>
        </CardHeader>
      </Card>

      {/* Main Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <MapPin className="h-8 w-8" />
            Branches
          </h1>
          <p className="text-muted-foreground mt-2">
            Manage branches for {organization.name}
          </p>
        </div>
        <div className="flex items-center gap-3">
          {/* View Mode Toggle */}
          <div className="flex items-center border rounded-lg p-1">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('grid')}
              className="h-8 px-3"
            >
              <Grid3X3 className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
              className="h-8 px-3"
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
          <Button onClick={openCreateDialog}>
            <Plus className="mr-2 h-4 w-4" />
            Add Branch
          </Button>
        </div>
      </div>

      {/* Empty State */}
      {branches.length === 0 ? (
        <Card className="text-center py-12">
          <CardContent>
            <MapPin className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No branches found</h3>
            <p className="text-muted-foreground mb-4">
              Get started by creating the first branch for {organization.name}.
            </p>
            <Button onClick={openCreateDialog}>
              <Plus className="mr-2 h-4 w-4" />
              Create Branch
            </Button>
          </CardContent>
        </Card>
      ) : (
        <>
          {/* Branches Content - Grid or List View */}
          {viewMode === 'grid' ? (
            /* Grid View */
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-8">
              {branches.map((branch) => (
                <Card key={branch.id} className="relative">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-2">
                        <MapPin className="h-5 w-5 text-primary" />
                        <CardTitle className="text-lg">{branch.name}</CardTitle>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant={branch.isActive ? "default" : "secondary"}>
                          {branch.isActive ? "Active" : "Inactive"}
                        </Badge>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => openEditDialog(branch)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => openDeleteDialog(branch)}
                              className="text-destructive"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                    {branch.description && (
                      <CardDescription>{branch.description}</CardDescription>
                    )}
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2 text-sm">
                      {branch.branchCode && (
                        <div className="flex items-center gap-2 text-muted-foreground">
                          <span>🏷️</span>
                          <span>Code: {branch.branchCode}</span>
                        </div>
                      )}
                      {branch.managerName && (
                        <div className="flex items-center gap-2 text-muted-foreground">
                          <Users className="h-4 w-4" />
                          <span>Manager: {branch.managerName}</span>
                        </div>
                      )}
                      {branch.email && (
                        <div className="flex items-center gap-2 text-muted-foreground">
                          <span>📧</span>
                          <span>{branch.email}</span>
                        </div>
                      )}
                      {branch.phone && (
                        <div className="flex items-center gap-2 text-muted-foreground">
                          <span>📞</span>
                          <span>{branch.phone}</span>
                        </div>
                      )}
                      {branch.address && (
                        <div className="flex items-start gap-2 text-muted-foreground">
                          <MapPin className="h-4 w-4 mt-0.5 flex-shrink-0" />
                          <span className="text-xs">{branch.address}</span>
                        </div>
                      )}
                      <div className="flex items-center gap-4 pt-2">
                        <div className="flex items-center gap-1">
                          <Users className="h-4 w-4 text-muted-foreground" />
                          <span className="text-muted-foreground">
                            {branch._count?.users || 0} users
                          </span>
                        </div>
                      </div>
                      {branch.createdAt && (
                        <div className="flex items-center gap-1 pt-1">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span className="text-muted-foreground text-xs">
                            Created {formatCreatedDate(branch.createdAt)}
                          </span>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            /* List View */
            <div className="mb-8">
              <Card>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Branch</TableHead>
                      <TableHead>Manager</TableHead>
                      <TableHead>Contact</TableHead>
                      <TableHead>Users</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Created</TableHead>
                      <TableHead className="w-[70px]">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {branches.map((branch) => (
                      <TableRow key={branch.id}>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <MapPin className="h-4 w-4 text-primary" />
                            <div>
                              <div className="font-medium">{branch.name}</div>
                              {branch.branchCode && (
                                <div className="text-sm text-muted-foreground">
                                  Code: {branch.branchCode}
                                </div>
                              )}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Users className="h-3 w-3 text-muted-foreground" />
                            <span className="text-sm">{branch.managerName || 'Not assigned'}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            {branch.email && (
                              <div className="text-sm">{branch.email}</div>
                            )}
                            {branch.phone && (
                              <div className="text-sm text-muted-foreground">{branch.phone}</div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Users className="h-4 w-4 text-muted-foreground" />
                            <span>{branch._count?.users || 0}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={branch.isActive ? "default" : "secondary"}>
                            {branch.isActive ? "Active" : "Inactive"}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {branch.createdAt && (
                            <div className="text-sm text-muted-foreground">
                              {formatCreatedDate(branch.createdAt)}
                            </div>
                          )}
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => openEditDialog(branch)}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => openDeleteDialog(branch)}
                                className="text-destructive"
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </Card>
            </div>
          )}
        </>
      )}

      {/* Create Branch Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Create New Branch</DialogTitle>
            <DialogDescription>
              Add a new branch to {organization.name}.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="create-name" className="text-right">
                Name *
              </Label>
              <Input
                id="create-name"
                value={branchForm.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className="col-span-3"
                placeholder="Branch name"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="create-branchCode" className="text-right">
                Branch Code
              </Label>
              <Input
                id="create-branchCode"
                value={branchForm.branchCode}
                onChange={(e) => handleInputChange('branchCode', e.target.value)}
                className="col-span-3"
                placeholder="e.g., BR001"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="create-managerName" className="text-right">
                Manager
              </Label>
              <Input
                id="create-managerName"
                value={branchForm.managerName}
                onChange={(e) => handleInputChange('managerName', e.target.value)}
                className="col-span-3"
                placeholder="Manager name"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="create-description" className="text-right">
                Description
              </Label>
              <Textarea
                id="create-description"
                value={branchForm.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                className="col-span-3"
                placeholder="Branch description"
                rows={3}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="create-address" className="text-right">
                Address
              </Label>
              <Textarea
                id="create-address"
                value={branchForm.address}
                onChange={(e) => handleInputChange('address', e.target.value)}
                className="col-span-3"
                placeholder="Full address"
                rows={2}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="create-email" className="text-right">
                Email
              </Label>
              <Input
                id="create-email"
                type="email"
                value={branchForm.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className="col-span-3"
                placeholder="<EMAIL>"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="create-phone" className="text-right">
                Phone
              </Label>
              <Input
                id="create-phone"
                value={branchForm.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                className="col-span-3"
                placeholder="******-123-4567"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="create-active" className="text-right">
                Active
              </Label>
              <Switch
                id="create-active"
                checked={branchForm.isActive}
                onCheckedChange={(checked) => handleInputChange('isActive', checked)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateBranch} disabled={isCreating || !branchForm.name.trim()}>
              {isCreating ? 'Creating...' : 'Create Branch'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Branch Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit Branch</DialogTitle>
            <DialogDescription>
              Update the branch information.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-name" className="text-right">
                Name *
              </Label>
              <Input
                id="edit-name"
                value={branchForm.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className="col-span-3"
                placeholder="Branch name"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-branchCode" className="text-right">
                Branch Code
              </Label>
              <Input
                id="edit-branchCode"
                value={branchForm.branchCode}
                onChange={(e) => handleInputChange('branchCode', e.target.value)}
                className="col-span-3"
                placeholder="e.g., BR001"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-managerName" className="text-right">
                Manager
              </Label>
              <Input
                id="edit-managerName"
                value={branchForm.managerName}
                onChange={(e) => handleInputChange('managerName', e.target.value)}
                className="col-span-3"
                placeholder="Manager name"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-description" className="text-right">
                Description
              </Label>
              <Textarea
                id="edit-description"
                value={branchForm.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                className="col-span-3"
                placeholder="Branch description"
                rows={3}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-address" className="text-right">
                Address
              </Label>
              <Textarea
                id="edit-address"
                value={branchForm.address}
                onChange={(e) => handleInputChange('address', e.target.value)}
                className="col-span-3"
                placeholder="Full address"
                rows={2}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-email" className="text-right">
                Email
              </Label>
              <Input
                id="edit-email"
                type="email"
                value={branchForm.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className="col-span-3"
                placeholder="<EMAIL>"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-phone" className="text-right">
                Phone
              </Label>
              <Input
                id="edit-phone"
                value={branchForm.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                className="col-span-3"
                placeholder="******-123-4567"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-active" className="text-right">
                Active
              </Label>
              <Switch
                id="edit-active"
                checked={branchForm.isActive}
                onCheckedChange={(checked) => handleInputChange('isActive', checked)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateBranch} disabled={isUpdating || !branchForm.name.trim()}>
              {isUpdating ? 'Updating...' : 'Update Branch'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Branch Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <div className="flex items-center gap-3">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-destructive/10">
                <AlertTriangle className="h-6 w-6 text-destructive" />
              </div>
              <div>
                <DialogTitle className="text-destructive">Delete Branch</DialogTitle>
                <DialogDescription className="mt-1">
                  This action cannot be undone
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="rounded-lg border border-destructive/20 bg-destructive/5 p-4">
              <h4 className="font-medium text-destructive mb-2">⚠️ Warning: Permanent Deletion</h4>
              <p className="text-sm text-muted-foreground mb-3">
                You are about to permanently delete <strong>"{currentBranch?.name}"</strong>
              </p>
              <div className="text-sm text-muted-foreground space-y-1">
                <div>• All branch data will be permanently deleted</div>
                <div>• All associated user assignments will be removed</div>
                <div>• This action cannot be undone</div>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="delete-confirmation" className="text-sm font-medium">
                Type <span className="font-mono bg-muted px-1 rounded">{currentBranch?.name}</span> to confirm:
              </Label>
              <Input
                id="delete-confirmation"
                value={deleteConfirmation}
                onChange={(e) => setDeleteConfirmation(e.target.value)}
                placeholder={`Type "${currentBranch?.name}" here`}
                className="font-mono"
              />
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setIsDeleteDialogOpen(false);
                setDeleteConfirmation('');
              }}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteBranch}
              disabled={isDeleting || deleteConfirmation !== currentBranch?.name}
            >
              {isDeleting ? 'Deleting...' : 'Delete Branch'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
