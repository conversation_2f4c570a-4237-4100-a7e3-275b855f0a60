import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/options';

const GO_BACKEND_URL = process.env.GO_BACKEND_URL || 'http://localhost:8050';

// Helper function to get authorization header
async function getAuthHeader(request: NextRequest): Promise<string | null> {
  try {
    // Try to get session from NextAuth
    const session = await getServerSession(authOptions);
    if (session?.accessToken) {
      return `Bearer ${session.accessToken}`;
    }

    // Fallback to Authorization header from request
    const authHeader = request.headers.get('authorization');
    if (authHeader) {
      return authHeader;
    }

    return null;
  } catch (error) {
    console.error('Error getting auth header:', error);
    return null;
  }
}

// Helper function to make requests to Go backend
async function makeBackendRequest(
  endpoint: string,
  method: string = 'GET',
  body?: any,
  authHeader?: string | null
): Promise<Response> {
  const url = `${GO_BACKEND_URL}/api/${endpoint}`;
  
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  if (authHeader) {
    headers['Authorization'] = authHeader;
  }

  const fetchOptions: RequestInit = {
    method,
    headers,
    ...(body && method !== 'GET' && { body: JSON.stringify(body) }),
  };

  try {
    const response = await fetch(url, fetchOptions);
    return response;
  } catch (error) {
    console.error('Backend request failed:', error);
    throw error;
  }
}

// GET /api/coa/[id] - Get a specific account
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const authHeader = await getAuthHeader(request);
    
    // Make request to Go backend
    const response = await makeBackendRequest(`accounts/${id}`, 'GET', undefined, authHeader);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('Backend error:', response.status, errorText);
      
      if (response.status === 401) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
      }
      
      if (response.status === 404) {
        return NextResponse.json({ error: 'Account not found' }, { status: 404 });
      }
      
      return NextResponse.json(
        { error: 'Failed to fetch account', details: errorText },
        { status: response.status }
      );
    }
    
    const data = await response.json();
    return NextResponse.json(data);
    
  } catch (error) {
    console.error('GET /api/coa/[id] error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// PUT /api/coa/[id] - Update a specific account
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const authHeader = await getAuthHeader(request);
    const body = await request.json();
    
    // Make request to Go backend
    const response = await makeBackendRequest(`accounts/${id}`, 'PUT', body, authHeader);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('Backend error:', response.status, errorText);
      
      if (response.status === 401) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
      }
      
      if (response.status === 404) {
        return NextResponse.json({ error: 'Account not found' }, { status: 404 });
      }
      
      if (response.status === 400) {
        return NextResponse.json({ error: 'Bad request', details: errorText }, { status: 400 });
      }
      
      return NextResponse.json(
        { error: 'Failed to update account', details: errorText },
        { status: response.status }
      );
    }
    
    const data = await response.json();
    return NextResponse.json(data);
    
  } catch (error) {
    console.error('PUT /api/coa/[id] error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// DELETE /api/coa/[id] - Delete a specific account
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const authHeader = await getAuthHeader(request);
    
    // Make request to Go backend
    const response = await makeBackendRequest(`accounts/${id}`, 'DELETE', undefined, authHeader);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('Backend error:', response.status, errorText);
      
      if (response.status === 401) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
      }
      
      if (response.status === 404) {
        return NextResponse.json({ error: 'Account not found' }, { status: 404 });
      }
      
      return NextResponse.json(
        { error: 'Failed to delete account', details: errorText },
        { status: response.status }
      );
    }
    
    // For successful deletion, return 204 No Content
    return new NextResponse(null, { status: 204 });
    
  } catch (error) {
    console.error('DELETE /api/coa/[id] error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// OPTIONS for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, Accept',
      'Access-Control-Max-Age': '86400',
    },
  });
}
