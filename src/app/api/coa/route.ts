import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../auth/[...nextauth]/options';

const GO_BACKEND_URL = process.env.GO_BACKEND_URL || 'http://localhost:8050';

// Helper function to get authorization header
async function getAuthHeader(request: NextRequest): Promise<string | null> {
  try {
    // Try to get session from NextAuth
    const session = await getServerSession(authOptions);
    console.log('[COA API] Session debug:', {
      hasSession: !!session,
      hasUser: !!session?.user,
      userEmail: session?.user?.email,
      hasAccessToken: !!session?.accessToken,
      accessTokenPreview: session?.accessToken ? session.accessToken.substring(0, 50) + '...' : 'No token'
    });

    if (session?.accessToken) {
      return `Bearer ${session.accessToken}`;
    }

    // Fallback to Authorization header from request
    const authHeader = request.headers.get('authorization');
    if (authHeader) {
      console.log('[COA API] Using fallback auth header');
      return authHeader;
    }

    console.log('[COA API] No auth header available');
    return null;
  } catch (error) {
    console.error('Error getting auth header:', error);
    return null;
  }
}

// Helper function to make requests to Go backend
async function makeBackendRequest(
  endpoint: string,
  method: string = 'GET',
  body?: any,
  authHeader?: string | null
): Promise<Response> {
  const url = `${GO_BACKEND_URL}/api/${endpoint}`;

  const headers: HeadersInit = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  if (authHeader) {
    headers['Authorization'] = authHeader;
  }

  const fetchOptions: RequestInit = {
    method,
    headers,
    ...(body && method !== 'GET' && { body: JSON.stringify(body) }),
  };

  try {
    const response = await fetch(url, fetchOptions);
    return response;
  } catch (error) {
    console.error('Backend request failed:', error);
    throw error;
  }
}

// GET /api/coa - Get all accounts with pagination and filtering
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const url = new URL(request.url);
    const searchParams = url.searchParams;

    // Get auth header
    const authHeader = await getAuthHeader(request);

    // Build the backend endpoint with query parameters
    const queryString = searchParams.toString();
    const endpoint = queryString ? `accounts?${queryString}` : 'accounts';

    // Make request to Go backend
    const response = await makeBackendRequest(endpoint, 'GET', undefined, authHeader);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Backend error:', response.status, errorText);

      // Handle specific error cases
      if (response.status === 401) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
      }

      if (response.status === 404) {
        return NextResponse.json({ error: 'Accounts not found' }, { status: 404 });
      }

      return NextResponse.json(
        { error: 'Failed to fetch accounts', details: errorText },
        { status: response.status }
      );
    }

    // Parse and return the response
    const data = await response.json();

    // Transform the response to match frontend expectations
    const transformedData = {
      accounts: data.accounts || data || [],
      pagination: data.pagination || {
        total: Array.isArray(data) ? data.length : 0,
        page: parseInt(searchParams.get('page') || '1'),
        limit: parseInt(searchParams.get('limit') || '10'),
        totalPages: 1,
        hasNextPage: false,
        hasPreviousPage: false
      }
    };

    return NextResponse.json(transformedData);

  } catch (error) {
    console.error('GET /api/coa error:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// POST /api/coa - Create a new account
export async function POST(request: NextRequest) {
  try {
    const authHeader = await getAuthHeader(request);
    const body = await request.json();

    // Make request to Go backend
    const response = await makeBackendRequest('accounts', 'POST', body, authHeader);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Backend error:', response.status, errorText);

      if (response.status === 401) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
      }

      if (response.status === 400) {
        return NextResponse.json({ error: 'Bad request', details: errorText }, { status: 400 });
      }

      return NextResponse.json(
        { error: 'Failed to create account', details: errorText },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data, { status: 201 });

  } catch (error) {
    console.error('POST /api/coa error:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// PUT /api/coa/:id - Update an account (handled by dynamic route)
// DELETE /api/coa/:id - Delete an account (handled by dynamic route)

// OPTIONS for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, Accept',
      'Access-Control-Max-Age': '86400',
    },
  });
}
