import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/options';

const GO_BACKEND_URL = process.env.GO_BACKEND_URL || 'http://localhost:8050';

// List of endpoints that should be handled by Next.js instead of proxied
const NEXTJS_ONLY_ENDPOINTS = [
  '/api/auth',
  '/api/webhooks/stripe',
  '/api/setup-stripe',
  '/api/subscriptions/checkout',
  '/api/subscriptions/portal',
  '/api/upload', // File uploads might need special handling
  '/api/proxy', // Don't proxy the proxy route itself
];

// Helper function to check if endpoint should be handled by Next.js
function shouldHandleInNextJS(path: string): boolean {
  return NEXTJS_ONLY_ENDPOINTS.some(endpoint => path.startsWith(endpoint));
}

// Helper function to get authorization header
async function getAuthHeader(request: NextRequest): Promise<string | null> {
  try {
    // Try to get session from NextAuth
    const session = await getServerSession(authOptions);
    if (session?.accessToken) {
      return `Bearer ${session.accessToken}`;
    }

    // Fallback to Authorization header from request
    const authHeader = request.headers.get('authorization');
    if (authHeader) {
      return authHeader;
    }

    return null;
  } catch (error) {
    console.error('Error getting auth header:', error);
    return null;
  }
}

// Helper function to transform request headers
function getProxyHeaders(request: NextRequest, authHeader: string | null): HeadersInit {
  const headers: HeadersInit = {
    'Content-Type': request.headers.get('content-type') || 'application/json',
    'Accept': request.headers.get('accept') || 'application/json',
    'User-Agent': request.headers.get('user-agent') || 'ADC-Account-Web',
  };

  // Add authorization header if available
  if (authHeader) {
    headers['Authorization'] = authHeader;
  }

  // Forward other important headers
  const forwardHeaders = [
    'x-forwarded-for',
    'x-real-ip',
    'x-forwarded-proto',
    'x-forwarded-host',
    'accept-language',
    'accept-encoding',
  ];

  forwardHeaders.forEach(headerName => {
    const value = request.headers.get(headerName);
    if (value) {
      headers[headerName] = value;
    }
  });

  return headers;
}

// Helper function to handle different HTTP methods
async function proxyRequest(
  method: string,
  url: string,
  headers: HeadersInit,
  body?: string | null
): Promise<Response> {
  const fetchOptions: RequestInit = {
    method,
    headers,
    // Don't include body for GET and HEAD requests
    ...(body && method !== 'GET' && method !== 'HEAD' && { body }),
  };

  try {
    const response = await fetch(url, fetchOptions);
    return response;
  } catch (error) {
    console.error('Proxy request failed:', error);
    throw error;
  }
}

// Main proxy handler
async function handleProxy(request: NextRequest, path: string) {
  try {
    // Construct the target URL
    const targetUrl = `${GO_BACKEND_URL}/api/${path}`;

    // Add query parameters if they exist
    const url = new URL(request.url);
    const searchParams = url.searchParams.toString();
    const fullTargetUrl = searchParams ? `${targetUrl}?${searchParams}` : targetUrl;

    // Get authorization header
    const authHeader = await getAuthHeader(request);

    // Prepare headers for the proxy request
    const proxyHeaders = getProxyHeaders(request, authHeader);

    // Get request body for non-GET requests
    let body: string | null = null;
    if (request.method !== 'GET' && request.method !== 'HEAD') {
      try {
        body = await request.text();
      } catch (error) {
        console.error('Error reading request body:', error);
      }
    }

    // Make the proxy request
    const response = await proxyRequest(
      request.method,
      fullTargetUrl,
      proxyHeaders,
      body
    );

    // Create response headers
    const responseHeaders = new Headers();

    // Copy important headers from the backend response
    const headersToForward = [
      'content-type',
      'content-length',
      'cache-control',
      'etag',
      'last-modified',
      'location',
    ];

    headersToForward.forEach(headerName => {
      const value = response.headers.get(headerName);
      if (value) {
        responseHeaders.set(headerName, value);
      }
    });

    // Add CORS headers for browser requests
    responseHeaders.set('Access-Control-Allow-Origin', '*');
    responseHeaders.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS');
    responseHeaders.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, Accept, X-Requested-With');

    // Get response body
    const responseBody = await response.text();

    // Return the proxied response
    return new NextResponse(responseBody, {
      status: response.status,
      statusText: response.statusText,
      headers: responseHeaders,
    });

  } catch (error) {
    console.error('Proxy error:', error);

    // Return error response
    return NextResponse.json(
      {
        error: 'Proxy request failed',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 502 }
    );
  }
}

// Handle OPTIONS requests for CORS
async function handleOptions() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, Accept, X-Requested-With',
      'Access-Control-Max-Age': '86400',
    },
  });
}

// Export handlers for all HTTP methods
export async function GET(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  const path = params.path.join('/');

  // Check if this should be handled by Next.js
  if (shouldHandleInNextJS(`/api/${path}`)) {
    return NextResponse.json({ error: 'Endpoint not found' }, { status: 404 });
  }

  return handleProxy(request, path);
}

export async function POST(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  const path = params.path.join('/');

  if (shouldHandleInNextJS(`/api/${path}`)) {
    return NextResponse.json({ error: 'Endpoint not found' }, { status: 404 });
  }

  return handleProxy(request, path);
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  const path = params.path.join('/');

  if (shouldHandleInNextJS(`/api/${path}`)) {
    return NextResponse.json({ error: 'Endpoint not found' }, { status: 404 });
  }

  return handleProxy(request, path);
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  const path = params.path.join('/');

  if (shouldHandleInNextJS(`/api/${path}`)) {
    return NextResponse.json({ error: 'Endpoint not found' }, { status: 404 });
  }

  return handleProxy(request, path);
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  const path = params.path.join('/');

  if (shouldHandleInNextJS(`/api/${path}`)) {
    return NextResponse.json({ error: 'Endpoint not found' }, { status: 404 });
  }

  return handleProxy(request, path);
}

export async function OPTIONS(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  return handleOptions();
}
