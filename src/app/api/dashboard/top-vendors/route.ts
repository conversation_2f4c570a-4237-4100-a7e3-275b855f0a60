import { NextRequest, NextResponse } from 'next/server';
import { proxyGet } from '@/lib/utils/proxy';
import { getMerchantId } from '@/lib/utils/auth';

export async function GET(request: NextRequest) {
  try {
    // Get the merchant ID from the session
    const merchantId = await getMerchantId();
    if (!merchantId) {
      return NextResponse.json({ error: 'Unauthorized: Merchant ID not found' }, { status: 401 });
    }

    // Add merchant_id to the query parameters
    const url = new URL(request.url);
    url.searchParams.set('merchant_id', merchantId);

    // Create a new request with the updated URL
    const modifiedRequest = new NextRequest(url, {
      method: request.method,
      headers: request.headers,
      body: request.body,
    });

    return proxyGet('/test-dashboard/top-vendors', modifiedRequest);
  } catch (error) {
    console.error('Error in top-vendors API:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
