import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../../../auth/[...nextauth]/options';

const GO_BACKEND_URL = process.env.GO_BACKEND_URL || 'http://localhost:8050';

// Helper function to get authorization header
async function getAuthHeader(request: NextRequest): Promise<string | null> {
  try {
    // Try to get session from NextAuth
    const session = await getServerSession(authOptions);
    if (session?.accessToken) {
      return `Bearer ${session.accessToken}`;
    }

    // Fallback to Authorization header from request
    const authHeader = request.headers.get('authorization');
    if (authHeader) {
      return authHeader;
    }

    return null;
  } catch (error) {
    console.error('Error getting auth header:', error);
    return null;
  }
}

// Helper function to get organization ID from slug
async function getOrganizationIdFromSlug(organizationSlug: string, authHeader: string | null): Promise<string | null> {
  try {
    const headers: HeadersInit = {
      'Accept': 'application/json',
    };

    if (authHeader) {
      headers['Authorization'] = authHeader;
    }

    // Get organizations and find the one with matching slug
    const response = await fetch(`${GO_BACKEND_URL}/api/organizations`, {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      console.error('Failed to fetch organizations:', response.status);
      return null;
    }

    const data = await response.json();
    const organizations = data.data || [];
    
    // Find organization by slug (convert name to slug format for comparison)
    const organization = organizations.find((org: any) => {
      const orgSlug = org.name
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();
      return orgSlug === organizationSlug;
    });

    return organization ? organization.id : null;
  } catch (error) {
    console.error('Error getting organization ID from slug:', error);
    return null;
  }
}

// Helper function to proxy requests to Go backend
async function proxyToBackend(request: NextRequest, method: string, organizationId: string, branchId: string, body?: string) {
  try {
    // Get authorization header
    const authHeader = await getAuthHeader(request);
    
    // Construct the target URL
    const url = new URL(request.url);
    const searchParams = url.searchParams.toString();
    const targetUrl = `${GO_BACKEND_URL}/api/organizations/${organizationId}/branches/${branchId}${searchParams ? `?${searchParams}` : ''}`;

    // Prepare headers for the proxy request
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (authHeader) {
      headers['Authorization'] = authHeader;
    }

    console.log(`Proxying ${method} request to: ${targetUrl}`);

    // Make the request to Go backend
    const response = await fetch(targetUrl, {
      method,
      headers,
      ...(body && method !== 'GET' && { body }),
    });

    // Get response data
    const data = await response.text();

    console.log(`Backend response: ${response.status} - ${data.substring(0, 200)}...`);

    // Return the response
    return new NextResponse(data, {
      status: response.status,
      headers: {
        'Content-Type': 'application/json',
      },
    });

  } catch (error) {
    console.error('Branch API proxy error:', error);
    return NextResponse.json(
      { error: 'Failed to proxy request to backend' },
      { status: 500 }
    );
  }
}

// GET /api/organizations/[organizationSlug]/branches/[branchId] - Get a specific branch
export async function GET(request: NextRequest, { params }: { params: Promise<{ organizationSlug: string; branchId: string }> }) {
  const { organizationSlug, branchId } = await params;
  
  // Get authorization header
  const authHeader = await getAuthHeader(request);
  
  // Get organization ID from slug
  const organizationId = await getOrganizationIdFromSlug(organizationSlug, authHeader);
  if (!organizationId) {
    return NextResponse.json(
      { error: 'Organization not found' },
      { status: 404 }
    );
  }

  return proxyToBackend(request, 'GET', organizationId, branchId);
}

// PUT /api/organizations/[organizationSlug]/branches/[branchId] - Update a specific branch
export async function PUT(request: NextRequest, { params }: { params: Promise<{ organizationSlug: string; branchId: string }> }) {
  const { organizationSlug, branchId } = await params;
  
  // Get authorization header
  const authHeader = await getAuthHeader(request);
  
  // Get organization ID from slug
  const organizationId = await getOrganizationIdFromSlug(organizationSlug, authHeader);
  if (!organizationId) {
    return NextResponse.json(
      { error: 'Organization not found' },
      { status: 404 }
    );
  }

  const body = await request.text();
  return proxyToBackend(request, 'PUT', organizationId, branchId, body);
}

// DELETE /api/organizations/[organizationSlug]/branches/[branchId] - Delete a specific branch
export async function DELETE(request: NextRequest, { params }: { params: Promise<{ organizationSlug: string; branchId: string }> }) {
  const { organizationSlug, branchId } = await params;
  
  // Get authorization header
  const authHeader = await getAuthHeader(request);
  
  // Get organization ID from slug
  const organizationId = await getOrganizationIdFromSlug(organizationSlug, authHeader);
  if (!organizationId) {
    return NextResponse.json(
      { error: 'Organization not found' },
      { status: 404 }
    );
  }

  return proxyToBackend(request, 'DELETE', organizationId, branchId);
}
