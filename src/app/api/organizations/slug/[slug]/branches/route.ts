import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../../../auth/[...nextauth]/options';

const GO_BACKEND_URL = process.env.GO_BACKEND_URL || 'http://localhost:8050';

// Helper function to get authorization header
async function getAuthHeader(request: NextRequest): Promise<string | null> {
  try {
    // Try to get session from NextAuth
    const session = await getServerSession(authOptions);
    if (session?.accessToken) {
      return `Bearer ${session.accessToken}`;
    }

    // Fallback to Authorization header from request
    const authHeader = request.headers.get('authorization');
    if (authHeader) {
      return authHeader;
    }

    return null;
  } catch (error) {
    console.error('Error getting auth header:', error);
    return null;
  }
}

// Helper function to proxy requests to Go backend
async function proxyToBackend(request: NextRequest, method: string, slug: string, body?: string) {
  try {
    // Get authorization header
    const authHeader = await getAuthHeader(request);
    
    // Construct the target URL using slug-based endpoint
    const url = new URL(request.url);
    const searchParams = url.searchParams.toString();
    const targetUrl = `${GO_BACKEND_URL}/api/organizations/slug/${slug}/branches${searchParams ? `?${searchParams}` : ''}`;

    // Prepare headers for the proxy request
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (authHeader) {
      headers['Authorization'] = authHeader;
    }

    console.log(`Proxying ${method} request to: ${targetUrl}`);

    // Make the request to Go backend
    const response = await fetch(targetUrl, {
      method,
      headers,
      ...(body && method !== 'GET' && { body }),
    });

    // Get response data
    const data = await response.text();

    console.log(`Backend response: ${response.status} - ${data.substring(0, 200)}...`);

    // Return the response
    return new NextResponse(data, {
      status: response.status,
      headers: {
        'Content-Type': 'application/json',
      },
    });

  } catch (error) {
    console.error('Organization branches API proxy error:', error);
    return NextResponse.json(
      { error: 'Failed to proxy request to backend' },
      { status: 500 }
    );
  }
}

// GET /api/organizations/slug/[slug]/branches - Get all branches for an organization by slug
export async function GET(request: NextRequest, { params }: { params: Promise<{ slug: string }> }) {
  const { slug } = await params;
  return proxyToBackend(request, 'GET', slug);
}

// POST /api/organizations/slug/[slug]/branches - Create a new branch by organization slug
export async function POST(request: NextRequest, { params }: { params: Promise<{ slug: string }> }) {
  const { slug } = await params;
  const body = await request.text();
  return proxyToBackend(request, 'POST', slug, body);
}
