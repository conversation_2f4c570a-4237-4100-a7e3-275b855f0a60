import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/lib/auth/session';
import { proxyGet, proxyPost, proxyPut, proxyDelete } from '@/lib/utils/proxy';
import { z } from 'zod';

// Zod schema for creating/updating a tax rate
const taxRateSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  rate: z.number().min(0, 'Rate must be a non-negative number'),
  description: z.string().optional(),
  isActive: z.boolean().optional().default(true),
});

// GET /api/taxes/rates - Get all tax rates
export async function GET(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    return await proxyGet('/taxes/rates', request);
  } catch (error) {
    console.error('GET /api/taxes/rates error:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}

// POST /api/taxes/rates - Create a new tax rate
export async function POST(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    // Parse and validate the request body
    const body = await request.json();
    const validationResult = taxRateSchema.safeParse({
      name: body.name,
      rate: body.rate,
      description: body.description,
      isActive: body.is_active,
    });
    
    if (!validationResult.success) {
      return NextResponse.json(
        { errors: validationResult.error.errors },
        { status: 400 }
      );
    }

    const validatedData = validationResult.data;
    
    // Transform the validated data to match the Rust backend's expected format
    const rustRequestBody = {
      name: validatedData.name,
      rate: validatedData.rate,
      description: validatedData.description,
      is_active: validatedData.isActive,
    };

    // Proxy to Rust backend
    return await proxyPost('/taxes/rates', request, rustRequestBody);
  } catch (error) {
    console.error('POST /api/taxes/rates error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { errors: error.errors },
        { status: 400 }
      );
    }
    
    if (error instanceof SyntaxError) {
      return NextResponse.json(
        { message: 'Invalid JSON body' },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { message: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
