import { NextRequest, NextResponse } from 'next/server';
import { taxService } from '@/lib/services/taxService';
import { getMerchantId } from '@/lib/utils/auth';
import { requirePermission } from '@/lib/utils/permissions';
import { PermissionLevel } from '@prisma/client';

interface Params {
  params: { id: string };
}

// GET /api/taxes/rates/[id] - Get a specific tax rate
export async function GET(request: Request, { params }: Params) {
  try {
    const { id } = params;

    // Get the merchant ID from the session
    const merchantId = await getMerchantId();
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
    }

    // Check permissions
    const permissionCheckGet = await requirePermission(request, merchantId, PermissionLevel.ReadOnly);
    if (permissionCheckGet) {
      console.warn(`GET /api/taxes/rates/${id} - Permission denied: User does not have ReadOnly level access`);
      return permissionCheckGet;
    }

    // Try to fetch from the database
    try {
        const taxRate = await taxService.getTaxRateById(merchantId, id);
        if (!taxRate) {
            return NextResponse.json({ message: 'Tax rate not found' }, { status: 404 });
        }
        console.log(`GET /api/taxes/rates/${id} - Fetched tax rate from database`);
        return NextResponse.json(taxRate);
    } catch (dbError) {
        console.error(`Database error fetching tax rate ${id}:`, dbError);
        // Removed fallback to mock data
        return NextResponse.json({ message: 'Failed to fetch tax rate due to database error' }, { status: 500 });
    }
} catch (error) {
    console.error(`Failed to fetch tax rate ${id}:`, error);
    if (error instanceof Error) {
        if (error.message.includes('Merchant ID not found')) {
            return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
        }
        if (error.message.includes('Permission denied')) {
            return NextResponse.json({ message: 'Forbidden: Insufficient permissions' }, { status: 403 });
        }
    }
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
}
}

// PUT /api/taxes/rates/[id] - Update a tax rate
export async function PUT(request: NextRequest, { params }: Params) {
  try {
    const { id } = params;
    const body = await request.json();

    // Validate using zod schema
    const validation = partialTaxRateSchema.safeParse(body);

    if (!validation.success) {
      console.error(`PUT /api/taxes/rates/${id} Validation Error:`, validation.error.errors);
      return NextResponse.json({ errors: validation.error.flatten().fieldErrors }, { status: 400 });
    }

    // Get the merchant ID from the session
    const merchantId = await getMerchantId();
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
    }

    // Check permissions
    const permissionCheckPut = await requirePermission(request, merchantId, PermissionLevel.Staff);
    if (permissionCheckPut) {
      console.warn(`PUT /api/taxes/rates/${id} - Permission denied: User does not have Staff level access`);
      return permissionCheckPut;
    }

    // Try to update in the database
    try {
        const updatedTaxRate = await taxService.updateTaxRate(merchantId, id, data);
        console.log(`PUT /api/taxes/rates/${id} - Updated tax rate in database`);
        return NextResponse.json(updatedTaxRate);
    } catch (dbError) {
        console.error(`Database error updating tax rate ${id}:`, dbError);
        // Removed fallback to mock data
        if (dbError instanceof Error && dbError.message.includes('not found')) {
            return NextResponse.json({ message: 'Tax rate not found' }, { status: 404 });
        }
        return NextResponse.json({ message: 'Failed to update tax rate due to database error' }, { status: 500 });
    }
} catch (error) {
    console.error(`Failed to update tax rate ${id}:`, error);
    if (error instanceof z.ZodError) {
        return NextResponse.json({ errors: error.errors }, { status: 400 });
    }
    if (error instanceof Error) {
        if (error.message.includes('Merchant ID not found')) {
            return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
        }
        if (error.message.includes('Permission denied')) {
            return NextResponse.json({ message: 'Forbidden: Insufficient permissions' }, { status: 403 });
        }
    }
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
}
}

// DELETE /api/taxes/rates/[id] - Delete a tax rate
export async function DELETE(request: NextRequest, { params }: Params) {
  try {
    const { id } = params;

    // Get the merchant ID from the session
    const merchantId = await getMerchantId();
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
    }

    // Check permissions
    const permissionCheckDelete = await requirePermission(request, merchantId, PermissionLevel.Manager);
    if (permissionCheckDelete) {
      console.warn(`DELETE /api/taxes/rates/${id} - Permission denied: User does not have Manager level access`);
      return permissionCheckDelete;
    }

    // Try to delete from the database
    try {
        await taxService.deleteTaxRate(merchantId, id);
        console.log(`DELETE /api/taxes/rates/${id} - Deleted tax rate from database`);
        return NextResponse.json({ message: 'Tax rate deleted successfully' }, { status: 200 });
    } catch (dbError) {
        console.error(`Database error deleting tax rate ${id}:`, dbError);
        // Removed fallback to mock data
        if (dbError instanceof Error && dbError.message.includes('not found')) {
            return NextResponse.json({ message: 'Tax rate not found' }, { status: 404 });
        }
        return NextResponse.json({ message: 'Failed to delete tax rate due to database error' }, { status: 500 });
    }
} catch (error) {
    console.error(`Failed to delete tax rate ${id}:`, error);
    if (error instanceof Error) {
        if (error.message.includes('Merchant ID not found')) {
            return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
        }
        if (error.message.includes('Permission denied')) {
            return NextResponse.json({ message: 'Forbidden: Insufficient permissions' }, { status: 403 });
        }
    }
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
}
}