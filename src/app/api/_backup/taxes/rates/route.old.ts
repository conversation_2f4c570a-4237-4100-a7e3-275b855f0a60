import { NextRequest, NextResponse } from 'next/server';
import { TaxRate } from '@/lib/types';
import { taxRateSchema } from '@/lib/validators/taxRate';
import { z } from 'zod';
import { taxRateService } from '@/lib/services/taxRateService';
import { formatTaxRateResponse, formatTaxRatesResponse } from '@/lib/utils/apiFormatters';
import { getMerchantId } from '@/lib/utils/auth';
import { requirePermission } from '@/lib/utils/permissions';
import { PermissionLevel } from '@prisma/client';

// GET /api/taxes/rates - List all tax rates
export async function GET(request: NextRequest) {
  try {
    // Get URL parameters for filtering
    const { searchParams } = new URL(request.url);
    const isActive = searchParams.get('is_active') === 'true' ? true :
                    searchParams.get('is_active') === 'false' ? false : undefined;
    const search = searchParams.get('search');

    // Get the merchant ID from the session
    const merchantId = await getMerchantId();
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
    }

    // Check permissions
    const permissionCheckPost = await requirePermission(request, merchantId, PermissionLevel.Staff);
    if (permissionCheckPost) {
      console.warn(`POST /api/taxes/rates - Permission denied: User does not have Staff level access`);
      return permissionCheckPost;
    }

    // Check permissions
    const permissionCheckGet = await requirePermission(request, merchantId, PermissionLevel.ReadOnly);
    if (permissionCheckGet) {
      console.warn(`GET /api/taxes/rates - Permission denied: User does not have ReadOnly level access`);
      return permissionCheckGet;
    }

    // Try to fetch from the database
    try {
        const taxRates = await taxRateService.findAllByMerchant(merchantId, { isActive });
        console.log(`GET /api/taxes/rates - Fetched ${taxRates.length} tax rates from database`);
        return NextResponse.json(taxRates);
    } catch (dbError) {
        console.error('Database error fetching tax rates:', dbError);
        // Removed fallback to mock data
        return NextResponse.json({ message: 'Failed to fetch tax rates due to database error' }, { status: 500 });
    }
} catch (error) {
    console.error('Failed to fetch tax rates:', error);
    if (error instanceof Error) {
        if (error.message.includes('Merchant ID not found')) {
            return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
        }
        if (error.message.includes('Permission denied')) {
            return NextResponse.json({ message: 'Forbidden: Insufficient permissions' }, { status: 403 });
        }
    }
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
}
}

// POST /api/taxes/rates - Create a new tax rate
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate using zod schema
    const validation = taxRateSchema.safeParse(body);

    if (!validation.success) {
      console.error('POST /api/taxes/rates Validation Error:', validation.error.errors);
      return NextResponse.json({ errors: validation.error.flatten().fieldErrors }, { status: 400 });
    }

    const { name, rate, description } = validation.data;

    // Get the merchant ID from the session
    const merchantId = await getMerchantId();
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
    }

    // Check permissions
    const permissionCheckPost = await requirePermission(request, merchantId, PermissionLevel.Staff);
    if (permissionCheckPost) {
      console.warn(`POST /api/taxes/rates - Permission denied: User does not have Staff level access`);
      return permissionCheckPost;
    }

    // Check permissions
    const permissionCheckGet = await requirePermission(request, merchantId, PermissionLevel.ReadOnly);
    if (permissionCheckGet) {
      console.warn(`GET /api/taxes/rates - Permission denied: User does not have ReadOnly level access`);
      return permissionCheckGet;
    }

    // Try to create in the database
    try {
        const data = {
            name,
            rate,
            description
        };
        const newTaxRate = await taxRateService.createForMerchant(merchantId, data);
        console.log(`POST /api/taxes/rates - Created new tax rate in database`);
        return NextResponse.json(newTaxRate, { status: 201 });
    } catch (dbError) {
        console.error('Database error creating tax rate:', dbError);
        // Removed fallback to mock data
        if (dbError instanceof Error && dbError.message.includes('already exists')) {
            return NextResponse.json({ message: 'Tax rate with this name already exists' }, { status: 409 });
        }
        return NextResponse.json({ message: 'Failed to create tax rate due to database error' }, { status: 500 });
    }
} catch (error) {
    console.error('Failed to create tax rate:', error);
    if (error instanceof z.ZodError) {
        return NextResponse.json({ errors: error.errors }, { status: 400 });
    }
    if (error instanceof Error) {
        if (error.message.includes('Merchant ID not found')) {
            return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
        }
        if (error.message.includes('Permission denied')) {
            return NextResponse.json({ message: 'Forbidden: Insufficient permissions' }, { status: 403 });
        }
    }
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
}
}