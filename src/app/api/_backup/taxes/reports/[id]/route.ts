// src/app/api/taxes/reports/[id]/route.ts
import { NextResponse } from 'next/server';
import type { TaxReport } from '@/lib/types';
import { getMerchantId } from '@/lib/utils/auth';
import { requirePermission } from '@/lib/utils/permissions';
import { PermissionLevel } from '@prisma/client';
import { taxService } from '@/lib/services/taxService';

interface Params {
  params: { id: string };
}

// GET /api/taxes/reports/[id] - Get a specific tax report
export async function GET(request: Request, { params }: Params) {
  try {
    const { id } = params;

    // Get the merchant ID from the session
    const merchantId = await getMerchantId();
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
    }

    // Check permissions
    const permissionCheckGet = await requirePermission(request, merchantId, PermissionLevel.ReadOnly);
    if (permissionCheckGet) {
      console.warn(`GET /api/taxes/reports/${id} - Permission denied: User does not have ReadOnly level access`);
      return permissionCheckGet;
    }

    // Try to fetch from the database
    try {
        const taxReport = await taxService.findTaxReportByIdForMerchant(merchantId, id);
        if (!taxReport) {
            return NextResponse.json({ message: 'Tax report not found' }, { status: 404 });
        }

        console.log(`GET /api/taxes/reports/${id} - Fetched tax report from database`);

        // Format the response to match the API contract
        const formattedReport = {
            id: taxReport.id,
            restaurant_id: taxReport.merchantId,
            report_type: taxReport.reportType,
            start_date: taxReport.startDate.toISOString(),
            end_date: taxReport.endDate.toISOString(),
            total_taxable_amount: Number(taxReport.totalTaxableAmount),
            total_tax_amount: Number(taxReport.totalTaxAmount),
            tax_rate_breakdown: taxReport.taxRateBreakdowns.map(breakdown => ({
                tax_rate_id: breakdown.taxRateId,
                tax_rate_name: breakdown.taxRateName,
                taxable_amount: Number(breakdown.taxableAmount),
                tax_amount: Number(breakdown.taxAmount),
            })),
            generated_at: taxReport.generatedAt.toISOString(),
        };

        return NextResponse.json(formattedReport);
    } catch (dbError) {
        console.error(`Database error fetching tax report ${id}:`, dbError);
        return NextResponse.json({ message: 'Failed to fetch tax report due to database error' }, { status: 500 });
    }
} catch (error) {
    console.error(`Failed to fetch tax report ${id}:`, error);
    if (error instanceof Error) {
        if (error.message.includes('Merchant ID not found')) {
            return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
        }
        if (error.message.includes('Permission denied')) {
            return NextResponse.json({ message: 'Forbidden: Insufficient permissions' }, { status: 403 });
        }
    }
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
}
}

// PUT /api/taxes/reports/[id] - Update a tax report
export async function PUT(request: Request, { params }: Params) {
  try {
    const { id } = params;

    // Get the merchant ID from the session
    const merchantId = await getMerchantId();
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
    }

    // Check permissions
    const permissionCheckPut = await requirePermission(request, merchantId, PermissionLevel.Staff);
    if (permissionCheckPut) {
      console.warn(`PUT /api/taxes/reports/${id} - Permission denied: User does not have Staff level access`);
      return permissionCheckPut;
    }

    const body = await request.json();

    // Validate the request body
    if (!body) {
      return NextResponse.json({ message: 'Missing request body' }, { status: 400 });
    }

    // Parse dates if provided
    const startDate = body.start_date ? new Date(body.start_date) : undefined;
    const endDate = body.end_date ? new Date(body.end_date) : undefined;

    // Validate dates if provided
    if ((body.start_date && isNaN(startDate!.getTime())) ||
        (body.end_date && isNaN(endDate!.getTime()))) {
      return NextResponse.json({ message: 'Invalid date format' }, { status: 400 });
    }

    // Check if start date is before end date
    if (startDate && endDate && startDate > endDate) {
      return NextResponse.json({ message: 'Start date must be before end date' }, { status: 400 });
    }

    // Try to update in the database
    try {
      const updatedTaxReport = await taxService.updateTaxReportForMerchant(merchantId, id, {
        reportType: body.report_type,
        startDate: startDate,
        endDate: endDate,
        totalTaxableAmount: body.total_taxable_amount,
        totalTaxAmount: body.total_tax_amount,
        taxRateBreakdowns: body.tax_rate_breakdown?.map((breakdown: any) => ({
          id: breakdown.id,
          taxRateId: breakdown.tax_rate_id,
          taxRateName: breakdown.tax_rate_name,
          taxableAmount: breakdown.taxable_amount,
          taxAmount: breakdown.tax_amount,
        })),
      });

      console.log(`PUT /api/taxes/reports/${id} - Updated tax report in database`);

      // Format the response to match the API contract
      const formattedReport = {
        id: updatedTaxReport.id,
        restaurant_id: updatedTaxReport.merchantId,
        report_type: updatedTaxReport.reportType,
        start_date: updatedTaxReport.startDate.toISOString(),
        end_date: updatedTaxReport.endDate.toISOString(),
        total_taxable_amount: Number(updatedTaxReport.totalTaxableAmount),
        total_tax_amount: Number(updatedTaxReport.totalTaxAmount),
        tax_rate_breakdown: updatedTaxReport.taxRateBreakdowns.map(breakdown => ({
          tax_rate_id: breakdown.taxRateId,
          tax_rate_name: breakdown.taxRateName,
          taxable_amount: Number(breakdown.taxableAmount),
          tax_amount: Number(breakdown.taxAmount),
        })),
        generated_at: updatedTaxReport.generatedAt.toISOString(),
      };

      return NextResponse.json(formattedReport);
    } catch (dbError) {
      console.error(`Database error updating tax report ${id}:`, dbError);

      if (dbError instanceof Error && dbError.message.includes('not found')) {
        return NextResponse.json({ message: 'Tax report not found' }, { status: 404 });
      }

      return NextResponse.json({ message: 'Failed to update tax report due to database error' }, { status: 500 });
    }
} catch (error) {
    console.error(`Failed to update tax report ${id}:`, error);
    if (error instanceof Error) {
        if (error.message.includes('Merchant ID not found')) {
            return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
        }
        if (error.message.includes('Permission denied')) {
            return NextResponse.json({ message: 'Forbidden: Insufficient permissions' }, { status: 403 });
        }
    }
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
}
}

// DELETE /api/taxes/reports/[id] - Delete a tax report
export async function DELETE(request: Request, { params }: Params) {
  try {
    const { id } = params;

    // Get the merchant ID from the session
    const merchantId = await getMerchantId();
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
    }

    // Check permissions
    const permissionCheckDelete = await requirePermission(request, merchantId, PermissionLevel.Manager);
    if (permissionCheckDelete) {
      console.warn(`DELETE /api/taxes/reports/${id} - Permission denied: User does not have Manager level access`);
      return permissionCheckDelete;
    }

    // Try to delete from the database
    try {
      await taxService.deleteTaxReportForMerchant(merchantId, id);
      console.log(`DELETE /api/taxes/reports/${id} - Deleted tax report from database`);
      return NextResponse.json({ message: 'Tax report deleted successfully' }, { status: 200 });
    } catch (dbError) {
      console.error(`Database error deleting tax report ${id}:`, dbError);

      if (dbError instanceof Error && dbError.message.includes('not found')) {
        return NextResponse.json({ message: 'Tax report not found' }, { status: 404 });
      }

      return NextResponse.json({ message: 'Failed to delete tax report due to database error' }, { status: 500 });
    }
} catch (error) {
    console.error(`Failed to delete tax report ${id}:`, error);
    if (error instanceof Error) {
        if (error.message.includes('Merchant ID not found')) {
            return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
        }
        if (error.message.includes('Permission denied')) {
            return NextResponse.json({ message: 'Forbidden: Insufficient permissions' }, { status: 403 });
        }
    }
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
}
}
