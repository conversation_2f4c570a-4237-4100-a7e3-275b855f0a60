// src/app/api/taxes/reports/route.ts
import { NextResponse } from 'next/server';
import { TaxReportType } from '@/lib/types';
import type { TaxReport } from '@/lib/types';
import { taxService } from '@/lib/services/taxService';
import { getMerchantId } from '@/lib/utils/auth';
import { requirePermission } from '@/lib/utils/permissions';
import { PermissionLevel } from '@prisma/client';
import { z } from 'zod';

// GET /api/taxes/reports - List all tax reports
export async function GET(request: Request) {
  // Get the merchant ID from the session
  const merchantId = await getMerchantId();
  if (!merchantId) {
    return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
  }

  // Check permissions
  const permissionCheckGet = await requirePermission(request, merchantId, PermissionLevel.ReadOnly);
  if (permissionCheckGet) {
    console.warn(`GET /api/taxes/reports - Permission denied: User does not have ReadOnly level access`);
    return permissionCheckGet;
  }

  try {
    const { searchParams } = new URL(request.url);
    const reportType = searchParams.get('type') as TaxReportType | null;
    const startDate = searchParams.get('start_date');
    const endDate = searchParams.get('end_date');

    // Parse dates if provided
    const parsedStartDate = startDate ? new Date(startDate) : undefined;
    const parsedEndDate = endDate ? new Date(endDate) : undefined;

    // Validate dates if provided
    if ((startDate && isNaN(parsedStartDate!.getTime())) ||
        (endDate && isNaN(parsedEndDate!.getTime()))) {
      return NextResponse.json({ message: 'Invalid date format' }, { status: 400 });
    }

    // Try to fetch tax reports using the service
    try {
      const reports = await taxService.findAllTaxReportsByMerchant(merchantId, {
        reportType,
        startDate: parsedStartDate,
        endDate: parsedEndDate,
      });

      console.log(`GET /api/taxes/reports - Fetched ${reports.length} tax reports from database`);

      // Format the response to match the API contract
      const formattedReports = reports.map(report => ({
        id: report.id,
        restaurant_id: report.merchantId,
        report_type: report.reportType,
        start_date: report.startDate.toISOString(),
        end_date: report.endDate.toISOString(),
        total_taxable_amount: Number(report.totalTaxableAmount),
        total_tax_amount: Number(report.totalTaxAmount),
        generated_at: report.generatedAt.toISOString(),
      }));

      return NextResponse.json(formattedReports);
    } catch (dbError) {
      console.error('Database error fetching tax reports:', dbError);

      if (dbError instanceof Error) {
        // Add specific error handling if needed
        if (dbError.message.includes('Invalid date range')) {
          return NextResponse.json({ message: 'Invalid date range provided' }, { status: 400 });
        }
      }

      return NextResponse.json({ message: 'Failed to fetch tax reports due to database error' }, { status: 500 });
    }

} catch (error) {
    console.error('Failed to fetch tax reports:', error);
    if (error instanceof Error) {
        if (error.message.includes('Merchant ID not found')) {
            return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
        }
        if (error.message.includes('Permission denied')) {
            return NextResponse.json({ message: 'Forbidden: Insufficient permissions' }, { status: 403 });
        }
    }
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
}
}

// POST /api/taxes/reports - Generate a new tax report
export async function POST(request: Request) {
  // Get the merchant ID from the session
  const merchantId = await getMerchantId();
  if (!merchantId) {
    return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
  }

  // Check permissions
  const permissionCheckPost = await requirePermission(request, merchantId, PermissionLevel.Staff);
  if (permissionCheckPost) {
    console.warn(`POST /api/taxes/reports - Permission denied: User does not have Staff level access`);
    return permissionCheckPost;
  }

  try {
    const body = await request.json();

    // Basic validation
    if (!body.report_type || !body.start_date || !body.end_date) {
      return NextResponse.json({ message: 'Missing required tax report fields' }, { status: 400 });
    }

    // Validate report type
    if (!Object.values(TaxReportType).includes(body.report_type)) {
      return NextResponse.json({ message: 'Invalid tax report type' }, { status: 400 });
    }

    // Validate dates
    const startDate = new Date(body.start_date);
    const endDate = new Date(body.end_date);

    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return NextResponse.json({ message: 'Invalid date format' }, { status: 400 });
    }

    if (startDate > endDate) {
      return NextResponse.json({ message: 'Start date must be before end date' }, { status: 400 });
    }

    // Generate tax report data based on type
    let taxableAmount = 0;
    let taxAmount = 0;
    let taxRateBreakdowns = [];

    // In a real implementation, these values would be calculated from actual data
    // For now, we'll use placeholder values based on the report type
    if (body.report_type === TaxReportType.Sales) {
      taxableAmount = 100000; // Placeholder
      taxAmount = 8000; // Placeholder (8% rate)
      taxRateBreakdowns = [
        {
          taxRateId: 'sales-tax-rate-1',
          taxRateName: 'Standard Sales Tax',
          taxableAmount: 100000,
          taxAmount: 8000,
        }
      ];
    } else if (body.report_type === TaxReportType.Income) {
      taxableAmount = 250000; // Placeholder
      taxAmount = 62500; // Placeholder (25% rate)
      taxRateBreakdowns = [
        {
          taxRateId: 'income-tax-rate-1',
          taxRateName: 'Federal Income Tax',
          taxableAmount: 250000,
          taxAmount: 62500,
        }
      ];
    } else { // Payroll
      taxableAmount = 75000; // Placeholder
      taxAmount = 5737.50; // Placeholder (7.65% rate)
      taxRateBreakdowns = [
        {
          taxRateId: 'payroll-tax-rate-1',
          taxRateName: 'FICA',
          taxableAmount: 75000,
          taxAmount: 5737.50,
        }
      ];
    }

    try {
      // Create the tax report using the service
      const newTaxReport = await taxService.createTaxReportForMerchant(merchantId, {
        reportType: body.report_type,
        startDate: startDate,
        endDate: endDate,
        totalTaxableAmount: taxableAmount,
        totalTaxAmount: taxAmount,
        taxRateBreakdowns: taxRateBreakdowns,
      });

      console.log(`POST /api/taxes/reports - Created new tax report in database`);

      // Format the response to match the API contract
      const formattedReport = {
        id: newTaxReport.id,
        restaurant_id: newTaxReport.merchantId,
        report_type: newTaxReport.reportType,
        start_date: newTaxReport.startDate.toISOString(),
        end_date: newTaxReport.endDate.toISOString(),
        total_taxable_amount: Number(newTaxReport.totalTaxableAmount),
        total_tax_amount: Number(newTaxReport.totalTaxAmount),
        tax_rate_breakdown: newTaxReport.taxRateBreakdowns.map(breakdown => ({
          tax_rate_id: breakdown.taxRateId,
          tax_rate_name: breakdown.taxRateName,
          taxable_amount: Number(breakdown.taxableAmount),
          tax_amount: Number(breakdown.taxAmount),
        })),
        generated_at: newTaxReport.generatedAt.toISOString(),
      };

      return NextResponse.json(formattedReport, { status: 201 });
    } catch (dbError) {
      console.error('Database error creating tax report:', dbError);
      return NextResponse.json({ message: 'Failed to create tax report due to database error' }, { status: 500 });
    }
  } catch (error) {
    console.error('Failed to generate tax report:', error);
    return NextResponse.json({ message: 'Failed to generate tax report' }, { status: 500 });
  }
}


