import { NextRequest, NextResponse } from 'next/server';
import {
  mockChartOfAccounts,
  mockJournalEntries,
  mockJournalEntryLines,
} from '@/lib/mockData';
import {
  Account,
  JournalEntry,
  JournalEntryLine,
  TrialBalanceReport,
  TrialBalanceLine,
} from '@/lib/types';
import { reportService } from '@/lib/services/reportService'; // Assuming report service exists
import { getMerchantId } from '@/lib/utils/auth';
import { requirePermission } from '@/lib/utils/permissions';
import { PermissionLevel } from '@prisma/client';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const asOfDateParam = searchParams.get('asOfDate');
    const asOfDate = asOfDateParam ? new Date(asOfDateParam) : null;

    // Get the merchant ID from the session
    const merchantId = await getMerchantId();
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
    }

    // Check permissions
    const permissionCheckGet = await requirePermission(request, merchantId, PermissionLevel.ReadOnly);
    if (permissionCheckGet) {
      console.warn(`GET /api/reports/trial-balance - Permission denied: User does not have ReadOnly level access`);
      return permissionCheckGet;
    }

    // Try to generate the report from the database
    try {
      const report = await reportService.generateTrialBalanceReport(merchantId, {
        asOfDate: asOfDate || undefined,
      });

      return NextResponse.json(report);
    } catch (dbError) {
      console.error('Database error, falling back to mock data:', dbError);

      // Fall back to mock data if database fails
      // Filter journal entries up to the asOfDate
      const relevantJournalEntries = mockJournalEntries.filter((entry) => {
        if (!asOfDate) return true; // Include all if no date specified
        const entryDate = new Date(entry.entry_date);
        // Include entries on or before the asOfDate (adjust time to end of day)
        return entryDate <= new Date(asOfDate.setHours(23, 59, 59, 999));
      });

      const relevantJournalEntryIds = new Set(
        relevantJournalEntries.map((entry) => entry.id)
      );

      // Filter journal entry lines based on the filtered entries
      const relevantJournalEntryLines = mockJournalEntryLines.filter((line) =>
        relevantJournalEntryIds.has(line.journal_entry_id)
      );

      // Calculate balances
      const accountBalances = new Map<string, number>();

      // Initialize balances for all accounts from CoA
      mockChartOfAccounts.forEach((account) => {
        accountBalances.set(account.id, 0);
      });

      // Process journal entry lines to update balances
      relevantJournalEntryLines.forEach((line) => {
        const currentBalance = accountBalances.get(line.account_id) || 0;
        // For simplicity in mock, we assume standard debit/credit rules apply universally
        // Assets & Expenses: Debit increases, Credit decreases
        // Liabilities, Equity, Revenue: Debit decreases, Credit increases
        // A simpler mock approach: just track net change. Add for Debit, Subtract for Credit.
        // Let's use the simpler approach for this mock:
        const amountChange = line.type === 'Debit' ? line.amount : -line.amount;
        accountBalances.set(line.account_id, currentBalance + amountChange);
      });

      // Format report lines
      let totalDebit = 0;
      let totalCredit = 0;
      const reportLines: TrialBalanceLine[] = mockChartOfAccounts
        .map((account): TrialBalanceLine | null => {
          const balance = accountBalances.get(account.id) || 0;
          // Skip accounts with zero balance for a cleaner report
          if (Math.abs(balance) < 0.005) { // Use a small tolerance for floating point
              return null;
          }

          let debit = 0;
          let credit = 0;

          // Determine debit/credit based on account type and balance sign
          // Standard accounting:
          // Assets/Expenses: Positive balance = Debit
          // Liabilities/Equity/Revenue: Positive balance = Credit (represented as negative in simple calc)
          // Let's stick to the simple balance sign for mock: Positive = Debit, Negative = Credit
          if (balance > 0) {
            debit = balance;
            totalDebit += debit;
          } else {
            credit = Math.abs(balance); // Use absolute value for credit column
            totalCredit += credit;
          }

          return {
            accountCode: account.account_code,
            accountName: account.account_name,
            debit: parseFloat(debit.toFixed(2)), // Format to 2 decimal places
            credit: parseFloat(credit.toFixed(2)), // Format to 2 decimal places
          };
        })
        .filter((line): line is TrialBalanceLine => line !== null) // Remove null entries
        .sort((a, b) => a.accountCode.localeCompare(b.accountCode)); // Sort by account code

      const report: TrialBalanceReport = {
        asOfDate: asOfDateParam ?? undefined, // Store the original param string or undefined
        lines: reportLines,
        totalDebit: parseFloat(totalDebit.toFixed(2)),
        totalCredit: parseFloat(totalCredit.toFixed(2)),
        generatedAt: new Date().toISOString(),
      };

      return NextResponse.json(report);
    }
  } catch (error) {
    console.error('Error generating trial balance report:', error);
    return NextResponse.json({ message: 'Failed to generate trial balance report' }, { status: 500 });
  }
}