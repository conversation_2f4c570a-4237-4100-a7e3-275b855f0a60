import { NextRequest, NextResponse } from 'next/server';
import {
  mockChartOfAccounts,
  mockJournalEntries,
  mockJournalEntryLines,
} from '@/lib/mockData';
import {
  Account,
  JournalEntry,
  JournalEntryLine,
  GeneralLedgerReport,
  GeneralLedgerReportLine,
} from '@/lib/types';
import { reportService } from '@/lib/services/reportService';
import { getMerchantId } from '@/lib/utils/auth';
import { requirePermission } from '@/lib/utils/permissions';
import { PermissionLevel } from '@prisma/client';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const accountId = searchParams.get('accountId');
    const startDateParam = searchParams.get('startDate');
    const endDateParam = searchParams.get('endDate');

    if (!accountId) {
      return NextResponse.json(
        { error: 'accountId query parameter is required' },
        { status: 400 }
      );
    }

    // Parse dates carefully, handling nulls
    const startDate = startDateParam ? new Date(startDateParam) : null;
    const endDate = endDateParam ? new Date(endDateParam) : null;

    // Get the merchant ID from the session
    const merchantId = await getMerchantId();
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
    }

    // Check permissions
    const permissionCheckGet = await requirePermission(request, merchantId, PermissionLevel.ReadOnly);
    if (permissionCheckGet) {
      console.warn(`GET /api/reports/general-ledger - Permission denied: User does not have ReadOnly level access`);
      return permissionCheckGet;
    }

    // Try to generate the report from the database
    try {
      const report = await reportService.generateGeneralLedgerReport(merchantId, {
        accountId: accountId || undefined,
        startDate: startDate || undefined,
        endDate: endDate || undefined,
      });

      console.log(`GET /api/reports/general-ledger - Generated report for account ${accountId || 'all'} from ${startDate || 'beginning'} to ${endDate || 'end'} from database`);

      return NextResponse.json(report);
    } catch (dbError) {
      console.error('Database error generating general ledger report:', dbError);
      // Removed fallback to mock data
      return NextResponse.json({ message: 'Failed to generate general ledger report due to database error' }, { status: 500 });
    }
  } catch (error) {
    console.error("Failed to generate general ledger report:", error);
    // Handle specific errors like missing merchantId or permission denied
    if (error instanceof Error) {
      if (error.message.includes('Merchant ID not found')) {
        return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
      }
      if (error.message.includes('Permission denied')) {
        return NextResponse.json({ message: 'Forbidden: Insufficient permissions' }, { status: 403 });
      }
      if (error.message.includes('Invalid date format')) {
        return NextResponse.json({ message: 'Bad Request: Invalid date format' }, { status: 400 });
      }
    }
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}