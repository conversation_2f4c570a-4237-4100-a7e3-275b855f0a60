import { NextRequest, NextResponse } from 'next/server';
import {
  mockJournalEntries,
  mockJournalEntryLines,
  mockChartOfAccounts,
} from '@/lib/mockData';
import { BalanceSheetReport, JournalEntry, JournalEntryLine, Account } from '@/lib/types';
import { reportService } from '@/lib/services/reportService'; // Assuming report service exists
import { getMerchantId } from '@/lib/utils/auth';
import { requirePermission } from '@/lib/utils/permissions';
import { PermissionLevel } from '@prisma/client';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const asOfDateParam = searchParams.get('asOfDate');

    // Parse date carefully, handling null
    const asOfDate = asOfDateParam ? new Date(asOfDateParam) : null;

    // Get the merchant ID from the session
    const merchantId = await getMerchantId();
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
    }

    // Check permissions
    const permissionCheckGet = await requirePermission(request, merchantId, PermissionLevel.ReadOnly);
    if (permissionCheckGet) {
      console.warn(`GET /api/reports/balance-sheet - Permission denied: User does not have ReadOnly level access`);
      return permissionCheckGet;
    }

    // Try to generate the report from the database
    try {
      // Try to fetch from the database/service
      try {
          const balanceSheet = await reportService.getBalanceSheet(merchantId, asOfDate);
          console.log(`GET /api/reports/balance-sheet - Fetched balance sheet from service for date: ${asOfDate}`);
          return NextResponse.json(balanceSheet);
      } catch (dbError) {
          console.error('Service error fetching balance sheet:', dbError);
          // Removed fallback to mock data
          return NextResponse.json({ message: 'Failed to fetch balance sheet due to a service error' }, { status: 500 });
      }
    } catch (error) {
      console.error('Failed to fetch balance sheet:', error);
      if (error instanceof Error) {
          if (error.message.includes('Merchant ID not found')) {
              return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
          }
          if (error.message.includes('Permission denied')) {
              return NextResponse.json({ message: 'Forbidden: Insufficient permissions' }, { status: 403 });
          }
          // Handle invalid date format errors specifically if the service doesn't
          if (error.message.includes('Invalid date')) {
              return NextResponse.json({ message: 'Invalid date format provided for asOfDate' }, { status: 400 });
          }
      }
      return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
    }
  } catch (error) {
    console.error('Error generating balance sheet report:', error);
    return NextResponse.json({ message: 'Failed to generate balance sheet report' }, { status: 500 });
  }
}