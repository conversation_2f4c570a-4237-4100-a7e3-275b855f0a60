import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getMerchantId } from '@/lib/utils/auth';
import { requirePermission } from '@/lib/utils/permissions';
import { PermissionLevel } from '@prisma/client';
import { z } from 'zod';
import { handleApiError, handleZodError, requireAuth } from '@/lib/api-utils';
import { createCustomClient } from '@/utils/supabase/customClient';
import { checkAuth } from '@/utils/api-auth';

// Validation schema for custom report request
const customReportSchema = z.object({
  reportType: z.enum(['cashflow', 'expenses', 'income', 'budget']),
  startDate: z.string().datetime({ offset: true }),
  endDate: z.string().datetime({ offset: true }),
  groupBy: z.enum(['day', 'week', 'month', 'quarter', 'year']).optional(),
  categories: z.array(z.string()).optional(),
  includeDetails: z.boolean().optional(),
  compareWithPrevious: z.boolean().optional(),
  format: z.enum(['json', 'csv']).optional(),
});

// POST /api/reports/custom - Generate a custom report
export async function POST(request: NextRequest) {
  try {
    // Check authentication using our helper
    const { isAuthenticated, userId, errorResponse } = await checkAuth(request);

    if (!isAuthenticated) {
      console.error('POST /api/reports/custom - Authentication failed');
      return errorResponse;
    }

    console.log(`POST /api/reports/custom - Authenticated user: ${userId}`);

    // Get the merchant ID from the session
    const merchantId = await getMerchantId();
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
    }

    console.log(`POST /api/reports/custom - Using merchant ID: ${merchantId}`);

    // Create a new request with the user ID in the headers
    const requestWithUserId = new NextRequest(request.url, {
      headers: new Headers({
        'x-user-id': userId,
        ...Object.fromEntries(request.headers.entries())
      })
    });

    // Check permissions
    const permissionCheckPost = await requirePermission(requestWithUserId, merchantId, PermissionLevel.Staff);
    if (permissionCheckPost) {
      console.warn(`POST /api/reports/custom - Permission denied: User does not have Staff level access`);
      return permissionCheckPost;
    }

    const body = await request.json();

    // Validate request body
    try {
      customReportSchema.parse(body);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return handleZodError(error);
      }
      throw error;
    }

    // Get merchant
    const merchant = await prisma.merchant.findFirst();

    if (!merchant) {
      return NextResponse.json(
        { message: 'No merchant found' },
        { status: 404 }
      );
    }

    const { reportType, startDate, endDate, groupBy, categories, includeDetails, compareWithPrevious, format } = body;

    // Calculate date range for previous period if needed
    let previousStartDate, previousEndDate;
    if (compareWithPrevious) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      const duration = end.getTime() - start.getTime();

      previousEndDate = new Date(start);
      previousEndDate.setDate(previousEndDate.getDate() - 1);

      previousStartDate = new Date(previousEndDate);
      previousStartDate.setTime(previousStartDate.getTime() - duration);
    }

    // Generate report based on report type
    let reportData;
    let previousPeriodData;

    switch (reportType) {
      case 'cashflow':
        reportData = await generateCashFlowReport(
          merchant.id,
          new Date(startDate),
          new Date(endDate),
          groupBy || 'month',
          categories
        );

        if (compareWithPrevious) {
          previousPeriodData = await generateCashFlowReport(
            merchant.id,
            previousStartDate!,
            previousEndDate!,
            groupBy || 'month',
            categories
          );
        }
        break;

      case 'expenses':
        reportData = await generateExpensesReport(
          merchant.id,
          new Date(startDate),
          new Date(endDate),
          groupBy || 'month',
          categories
        );

        if (compareWithPrevious) {
          previousPeriodData = await generateExpensesReport(
            merchant.id,
            previousStartDate!,
            previousEndDate!,
            groupBy || 'month',
            categories
          );
        }
        break;

      case 'income':
        reportData = await generateIncomeReport(
          merchant.id,
          new Date(startDate),
          new Date(endDate),
          groupBy || 'month',
          categories
        );

        if (compareWithPrevious) {
          previousPeriodData = await generateIncomeReport(
            merchant.id,
            previousStartDate!,
            previousEndDate!,
            groupBy || 'month',
            categories
          );
        }
        break;

      case 'budget':
        reportData = await generateBudgetReport(
          merchant.id,
          new Date(startDate),
          new Date(endDate),
          groupBy || 'month',
          categories
        );

        if (compareWithPrevious) {
          previousPeriodData = await generateBudgetReport(
            merchant.id,
            previousStartDate!,
            previousEndDate!,
            groupBy || 'month',
            categories
          );
        }
        break;

      default:
        return NextResponse.json(
          { message: 'Invalid report type' },
          { status: 400 }
        );
    }

    // Prepare response
    const response: any = {
      reportType,
      startDate,
      endDate,
      groupBy: groupBy || 'month',
      data: reportData,
    };

    if (compareWithPrevious) {
      response.previousPeriod = {
        startDate: previousStartDate?.toISOString(),
        endDate: previousEndDate?.toISOString(),
        data: previousPeriodData,
      };
    }

    // Return response in requested format
    if (format === 'csv') {
      // Convert to CSV
      const csv = convertToCSV(response);
      return new NextResponse(csv, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="${reportType}_report_${new Date().toISOString().split('T')[0]}.csv"`,
        },
      });
    }

    return NextResponse.json(response);
  } catch (error) {
    return handleApiError(error);
  }
}

// Helper function to generate cash flow report
async function generateCashFlowReport(
  merchantId: string,
  startDate: Date,
  endDate: Date,
  groupBy: string,
  categories?: string[]
) {
  // Build query filters
  const where: any = {
    merchantId,
    expectedDate: {
      gte: startDate,
      lte: endDate,
    },
  };

  if (categories && categories.length > 0) {
    where.category = {
      in: categories,
    };
  }

  // Get cash flow items
  const cashFlowItems = await prisma.cashFlowItem.findMany({
    where,
    orderBy: {
      expectedDate: 'asc',
    },
  });

  // Group by specified interval
  const groupedData = groupDataByInterval(cashFlowItems, groupBy, 'expectedDate');

  // Calculate totals
  const summary = {
    totalIncome: cashFlowItems
      .filter(item => item.type === 'Income')
      .reduce((sum, item) => sum + Number(item.amount), 0),
    totalExpenses: cashFlowItems
      .filter(item => item.type === 'Expense')
      .reduce((sum, item) => sum + Number(item.amount), 0),
    netCashFlow: cashFlowItems.reduce((sum, item) => {
      return sum + (item.type === 'Income' ? Number(item.amount) : -Number(item.amount));
    }, 0),
  };

  return {
    summary,
    groupedData,
  };
}

// Helper function to generate expenses report
async function generateExpensesReport(
  merchantId: string,
  startDate: Date,
  endDate: Date,
  groupBy: string,
  categories?: string[]
) {
  // Build query filters
  const where: any = {
    merchantId,
    expectedDate: {
      gte: startDate,
      lte: endDate,
    },
    type: 'Expense',
  };

  if (categories && categories.length > 0) {
    where.category = {
      in: categories,
    };
  }

  // Get expense items
  const expenseItems = await prisma.cashFlowItem.findMany({
    where,
    orderBy: {
      expectedDate: 'asc',
    },
  });

  // Group by specified interval
  const groupedData = groupDataByInterval(expenseItems, groupBy, 'expectedDate');

  // Group by category
  const categoryData = expenseItems.reduce((acc, item) => {
    const category = item.category || 'Uncategorized';
    if (!acc[category]) {
      acc[category] = 0;
    }
    acc[category] += Number(item.amount);
    return acc;
  }, {} as Record<string, number>);

  // Calculate totals
  const summary = {
    totalExpenses: expenseItems.reduce((sum, item) => sum + Number(item.amount), 0),
    categoryBreakdown: Object.entries(categoryData).map(([category, amount]) => ({
      category,
      amount,
      percentage: (amount / expenseItems.reduce((sum, item) => sum + Number(item.amount), 0)) * 100,
    })),
  };

  return {
    summary,
    groupedData,
  };
}

// Helper function to generate income report
async function generateIncomeReport(
  merchantId: string,
  startDate: Date,
  endDate: Date,
  groupBy: string,
  categories?: string[]
) {
  // Build query filters
  const where: any = {
    merchantId,
    expectedDate: {
      gte: startDate,
      lte: endDate,
    },
    type: 'Income',
  };

  if (categories && categories.length > 0) {
    where.category = {
      in: categories,
    };
  }

  // Get income items
  const incomeItems = await prisma.cashFlowItem.findMany({
    where,
    orderBy: {
      expectedDate: 'asc',
    },
  });

  // Group by specified interval
  const groupedData = groupDataByInterval(incomeItems, groupBy, 'expectedDate');

  // Group by category
  const categoryData = incomeItems.reduce((acc, item) => {
    const category = item.category || 'Uncategorized';
    if (!acc[category]) {
      acc[category] = 0;
    }
    acc[category] += Number(item.amount);
    return acc;
  }, {} as Record<string, number>);

  // Calculate totals
  const summary = {
    totalIncome: incomeItems.reduce((sum, item) => sum + Number(item.amount), 0),
    categoryBreakdown: Object.entries(categoryData).map(([category, amount]) => ({
      category,
      amount,
      percentage: (amount / incomeItems.reduce((sum, item) => sum + Number(item.amount), 0)) * 100,
    })),
  };

  return {
    summary,
    groupedData,
  };
}

// Helper function to generate budget report
async function generateBudgetReport(
  merchantId: string,
  startDate: Date,
  endDate: Date,
  groupBy: string,
  categories?: string[]
) {
  // Get budget items
  const budgetItems = await prisma.budgetItem.findMany({
    where: {
      merchantId,
      // Filter by date range
      OR: [
        {
          startDate: {
            lte: endDate,
          },
          endDate: {
            gte: startDate,
          },
        },
        {
          startDate: {
            lte: endDate,
          },
          endDate: null,
        },
      ],
      // Filter by categories if provided
      ...(categories && categories.length > 0
        ? { category: { in: categories } }
        : {}),
    },
  });

  // Get actual cash flow items for comparison
  const cashFlowItems = await prisma.cashFlowItem.findMany({
    where: {
      merchantId,
      expectedDate: {
        gte: startDate,
        lte: endDate,
      },
      ...(categories && categories.length > 0
        ? { category: { in: categories } }
        : {}),
    },
  });

  // Calculate budget vs actual
  const budgetVsActual = calculateBudgetVsActual(
    budgetItems,
    cashFlowItems,
    startDate,
    endDate,
    groupBy
  );

  return {
    budgetVsActual,
  };
}

// Helper function to group data by time interval
function groupDataByInterval(
  data: any[],
  interval: string,
  dateField: string
) {
  const result: Record<string, any[]> = {};

  data.forEach(item => {
    const date = new Date(item[dateField]);
    let key: string;

    switch (interval) {
      case 'day':
        key = date.toISOString().split('T')[0]; // YYYY-MM-DD
        break;
      case 'week':
        // Get the first day of the week (Sunday)
        const firstDayOfWeek = new Date(date);
        const day = date.getDay();
        firstDayOfWeek.setDate(date.getDate() - day);
        key = `${firstDayOfWeek.toISOString().split('T')[0]}`;
        break;
      case 'month':
        key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
        break;
      case 'quarter':
        const quarter = Math.floor(date.getMonth() / 3) + 1;
        key = `${date.getFullYear()}-Q${quarter}`;
        break;
      case 'year':
        key = `${date.getFullYear()}`;
        break;
      default:
        key = date.toISOString().split('T')[0]; // Default to day
    }

    if (!result[key]) {
      result[key] = [];
    }

    result[key].push(item);
  });

  // Calculate totals for each group
  return Object.entries(result).map(([period, items]) => {
    const income = items
      .filter(item => item.type === 'Income')
      .reduce((sum, item) => sum + Number(item.amount), 0);

    const expenses = items
      .filter(item => item.type === 'Expense')
      .reduce((sum, item) => sum + Number(item.amount), 0);

    return {
      period,
      income,
      expenses,
      netCashFlow: income - expenses,
      count: items.length,
      items: items.map(item => ({
        id: item.id,
        description: item.description,
        amount: Number(item.amount),
        type: item.type,
        category: item.category,
        date: new Date(item[dateField]).toISOString(),
      })),
    };
  }).sort((a, b) => a.period.localeCompare(b.period));
}

// Helper function to calculate budget vs actual
function calculateBudgetVsActual(
  budgetItems: any[],
  cashFlowItems: any[],
  startDate: Date,
  endDate: Date,
  groupBy: string
) {
  // Group actual cash flow items by category and type
  const actualByCategory: Record<string, { income: number; expenses: number }> = {};

  cashFlowItems.forEach(item => {
    const category = item.category || 'Uncategorized';
    if (!actualByCategory[category]) {
      actualByCategory[category] = { income: 0, expenses: 0 };
    }

    if (item.type === 'Income') {
      actualByCategory[category].income += Number(item.amount);
    } else {
      actualByCategory[category].expenses += Number(item.amount);
    }
  });

  // Calculate budget amounts for the period
  const budgetByCategory: Record<string, { income: number; expenses: number }> = {};

  budgetItems.forEach(item => {
    const category = item.category || 'Uncategorized';
    if (!budgetByCategory[category]) {
      budgetByCategory[category] = { income: 0, expenses: 0 };
    }

    // Calculate prorated budget amount for the period
    let budgetAmount = Number(item.amount);

    // If budget item has a date range, prorate it
    if (item.startDate && item.endDate) {
      const budgetStartDate = new Date(item.startDate) < startDate ? startDate : new Date(item.startDate);
      const budgetEndDate = new Date(item.endDate) > endDate ? endDate : new Date(item.endDate);

      const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
      const budgetDays = Math.ceil((budgetEndDate.getTime() - budgetStartDate.getTime()) / (1000 * 60 * 60 * 24));

      budgetAmount = (budgetAmount / totalDays) * budgetDays;
    }

    if (item.type === 'Income') {
      budgetByCategory[category].income += budgetAmount;
    } else {
      budgetByCategory[category].expenses += budgetAmount;
    }
  });

  // Combine budget and actual data
  const allCategories = new Set([
    ...Object.keys(budgetByCategory),
    ...Object.keys(actualByCategory),
  ]);

  const result = Array.from(allCategories).map(category => {
    const budget = budgetByCategory[category] || { income: 0, expenses: 0 };
    const actual = actualByCategory[category] || { income: 0, expenses: 0 };

    return {
      category,
      budget: {
        income: budget.income,
        expenses: budget.expenses,
        net: budget.income - budget.expenses,
      },
      actual: {
        income: actual.income,
        expenses: actual.expenses,
        net: actual.income - actual.expenses,
      },
      variance: {
        income: actual.income - budget.income,
        expenses: actual.expenses - budget.expenses,
        net: (actual.income - actual.expenses) - (budget.income - budget.expenses),
      },
      percentageVariance: {
        income: budget.income ? ((actual.income - budget.income) / budget.income) * 100 : 0,
        expenses: budget.expenses ? ((actual.expenses - budget.expenses) / budget.expenses) * 100 : 0,
        net: (budget.income - budget.expenses)
          ? (((actual.income - actual.expenses) - (budget.income - budget.expenses)) / (budget.income - budget.expenses)) * 100
          : 0,
      },
    };
  });

  // Calculate totals
  const totals = result.reduce(
    (acc, item) => {
      acc.budget.income += item.budget.income;
      acc.budget.expenses += item.budget.expenses;
      acc.budget.net += item.budget.net;

      acc.actual.income += item.actual.income;
      acc.actual.expenses += item.actual.expenses;
      acc.actual.net += item.actual.net;

      acc.variance.income += item.variance.income;
      acc.variance.expenses += item.variance.expenses;
      acc.variance.net += item.variance.net;

      return acc;
    },
    {
      budget: { income: 0, expenses: 0, net: 0 },
      actual: { income: 0, expenses: 0, net: 0 },
      variance: { income: 0, expenses: 0, net: 0 },
    }
  );

  // Calculate total percentage variance
  const totalPercentageVariance = {
    income: totals.budget.income ? (totals.variance.income / totals.budget.income) * 100 : 0,
    expenses: totals.budget.expenses ? (totals.variance.expenses / totals.budget.expenses) * 100 : 0,
    net: totals.budget.net ? (totals.variance.net / totals.budget.net) * 100 : 0,
  };

  return {
    categories: result,
    totals: {
      ...totals,
      percentageVariance: totalPercentageVariance,
    },
  };
}

// Helper function to convert report data to CSV
function convertToCSV(reportData: any): string {
  let csv = '';

  // Add report metadata
  csv += `Report Type,${reportData.reportType}\n`;
  csv += `Start Date,${reportData.startDate}\n`;
  csv += `End Date,${reportData.endDate}\n`;
  csv += `Group By,${reportData.groupBy}\n\n`;

  // Add summary data
  if (reportData.data.summary) {
    csv += 'Summary\n';

    if (reportData.reportType === 'cashflow') {
      csv += `Total Income,${reportData.data.summary.totalIncome}\n`;
      csv += `Total Expenses,${reportData.data.summary.totalExpenses}\n`;
      csv += `Net Cash Flow,${reportData.data.summary.netCashFlow}\n\n`;
    } else if (reportData.reportType === 'expenses') {
      csv += `Total Expenses,${reportData.data.summary.totalExpenses}\n\n`;

      // Add category breakdown
      csv += 'Category Breakdown\n';
      csv += 'Category,Amount,Percentage\n';
      reportData.data.summary.categoryBreakdown.forEach((item: any) => {
        csv += `${item.category},${item.amount},${item.percentage.toFixed(2)}%\n`;
      });
      csv += '\n';
    } else if (reportData.reportType === 'income') {
      csv += `Total Income,${reportData.data.summary.totalIncome}\n\n`;

      // Add category breakdown
      csv += 'Category Breakdown\n';
      csv += 'Category,Amount,Percentage\n';
      reportData.data.summary.categoryBreakdown.forEach((item: any) => {
        csv += `${item.category},${item.amount},${item.percentage.toFixed(2)}%\n`;
      });
      csv += '\n';
    }
  }

  // Add grouped data
  if (reportData.data.groupedData) {
    csv += 'Grouped Data\n';
    csv += 'Period,Income,Expenses,Net Cash Flow,Count\n';

    reportData.data.groupedData.forEach((item: any) => {
      csv += `${item.period},${item.income},${item.expenses},${item.netCashFlow},${item.count}\n`;
    });
    csv += '\n';
  }

  // Add budget vs actual data
  if (reportData.data.budgetVsActual) {
    csv += 'Budget vs Actual\n';
    csv += 'Category,Budget Income,Budget Expenses,Budget Net,Actual Income,Actual Expenses,Actual Net,Variance Income,Variance Expenses,Variance Net,% Variance Income,% Variance Expenses,% Variance Net\n';

    reportData.data.budgetVsActual.categories.forEach((item: any) => {
      csv += `${item.category},${item.budget.income},${item.budget.expenses},${item.budget.net},${item.actual.income},${item.actual.expenses},${item.actual.net},${item.variance.income},${item.variance.expenses},${item.variance.net},${item.percentageVariance.income.toFixed(2)}%,${item.percentageVariance.expenses.toFixed(2)}%,${item.percentageVariance.net.toFixed(2)}%\n`;
    });

    // Add totals
    const totals = reportData.data.budgetVsActual.totals;
    csv += `Totals,${totals.budget.income},${totals.budget.expenses},${totals.budget.net},${totals.actual.income},${totals.actual.expenses},${totals.actual.net},${totals.variance.income},${totals.variance.expenses},${totals.variance.net},${totals.percentageVariance.income.toFixed(2)}%,${totals.percentageVariance.expenses.toFixed(2)}%,${totals.percentageVariance.net.toFixed(2)}%\n`;
    csv += '\n';
  }

  // Add previous period data if available
  if (reportData.previousPeriod) {
    csv += 'Previous Period\n';
    csv += `Start Date,${reportData.previousPeriod.startDate}\n`;
    csv += `End Date,${reportData.previousPeriod.endDate}\n\n`;

    // Add previous period summary
    if (reportData.previousPeriod.data.summary) {
      csv += 'Previous Period Summary\n';

      if (reportData.reportType === 'cashflow') {
        csv += `Total Income,${reportData.previousPeriod.data.summary.totalIncome}\n`;
        csv += `Total Expenses,${reportData.previousPeriod.data.summary.totalExpenses}\n`;
        csv += `Net Cash Flow,${reportData.previousPeriod.data.summary.netCashFlow}\n\n`;
      } else if (reportData.reportType === 'expenses') {
        csv += `Total Expenses,${reportData.previousPeriod.data.summary.totalExpenses}\n\n`;
      } else if (reportData.reportType === 'income') {
        csv += `Total Income,${reportData.previousPeriod.data.summary.totalIncome}\n\n`;
      }
    }
  }

  return csv;
}
