import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/lib/auth/session';
import { proxyGet } from '@/lib/utils/proxy';
import { z } from 'zod';

// GET /api/reports/profit-loss - Proxy to Rust backend
export async function GET(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    return await proxyGet('/reports/profit-loss', request);
  } catch (error) {
    console.error('GET /api/reports/profit-loss error:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}
