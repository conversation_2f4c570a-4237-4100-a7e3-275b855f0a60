import { NextResponse, NextRequest } from 'next/server';
import { getMerchantId } from '@/lib/utils/auth';
import { requirePermission } from '@/lib/utils/permissions';
import { PermissionLevel } from '@prisma/client';
import { PDFService } from '@/lib/services/pdfService';
import { z } from 'zod';

// Define the schema for AR Aging report request
const arAgingReportSchema = z.object({
  asOfDate: z.string().optional(), // ISO date string
  customerId: z.string().optional(),
});

// POST /api/reports/ar-aging/pdf - Generate a PDF for AR Aging report
export async function POST(request: NextRequest) {
  try {
    // Get merchant ID from session
    const merchantId = await getMerchantId();
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized - Merchant ID not found' }, { status: 401 });
    }

    // Check if the user has permission to view reports (ReadOnly level or higher)
    const permissionCheck = await requirePermission(request, merchantId, PermissionLevel.ReadOnly);
    if (permissionCheck) {
      console.warn('POST /api/reports/ar-aging/pdf - Permission denied: User does not have ReadOnly level access');
      return permissionCheck;
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = arAgingReportSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json({ errors: validationResult.error.errors }, { status: 400 });
    }
    
    const { asOfDate, customerId } = validationResult.data;

    try {
      // Get AR Aging report data
      // In a real implementation, this would fetch data from the database
      // For now, we'll use mock data
      const reportData = await getARAgingReportData(merchantId, asOfDate, customerId);

      try {
        // Generate the PDF
        const pdfBuffer = await PDFService.generateReportPDF('ar-aging', reportData);
        
        // Create a response with the PDF data
        const response = new NextResponse(pdfBuffer, {
          status: 200,
          headers: {
            'Content-Type': 'application/pdf',
            'Content-Disposition': `attachment; filename="ar-aging-report-${new Date().toISOString().split('T')[0]}.pdf"`,
          },
        });
        
        return response;
      } catch (pdfError) {
        console.error('Error generating PDF:', pdfError);
        return NextResponse.json({
          message: 'Failed to generate PDF',
          error: pdfError instanceof Error ? pdfError.message : 'Unknown PDF generation error'
        }, { status: 500 });
      }
    } catch (dataError) {
      console.error('Error fetching AR Aging report data:', dataError);
      return NextResponse.json({
        message: 'Failed to fetch AR Aging report data',
        error: dataError instanceof Error ? dataError.message : 'Unknown data error'
      }, { status: 500 });
    }
  } catch (error) {
    console.error('Error generating AR Aging report PDF:', error);
    if (error instanceof z.ZodError) {
      return NextResponse.json({ errors: error.errors }, { status: 400 });
    }
    if (error instanceof SyntaxError) {
      return NextResponse.json({ message: 'Invalid JSON body' }, { status: 400 });
    }
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}

// Mock function to get AR Aging report data
// In a real implementation, this would fetch data from the database
async function getARAgingReportData(merchantId: string, asOfDate?: string, customerId?: string) {
  // Use the provided asOfDate or default to today
  const reportDate = asOfDate ? new Date(asOfDate) : new Date();
  
  // Mock data for AR Aging report
  const mockData = {
    asOfDate: reportDate.toISOString(),
    merchantId,
    customerId,
    lines: [
      {
        customerId: '1',
        customerName: 'Acme Corporation',
        invoiceNumber: 'INV-001',
        invoiceDate: '2023-01-15',
        dueDate: '2023-02-15',
        buckets: {
          current: 0,
          days1_30: 0,
          days31_60: 1500,
          days61_90: 0,
          over90: 0
        },
        amountDue: 1500
      },
      {
        customerId: '2',
        customerName: 'Globex Industries',
        invoiceNumber: 'INV-002',
        invoiceDate: '2023-02-01',
        dueDate: '2023-03-01',
        buckets: {
          current: 0,
          days1_30: 2200,
          days31_60: 0,
          days61_90: 0,
          over90: 0
        },
        amountDue: 2200
      },
      {
        customerId: '3',
        customerName: 'Stark Enterprises',
        invoiceNumber: 'INV-003',
        invoiceDate: '2022-11-15',
        dueDate: '2022-12-15',
        buckets: {
          current: 0,
          days1_30: 0,
          days31_60: 0,
          days61_90: 0,
          over90: 3500
        },
        amountDue: 3500
      },
      {
        customerId: '4',
        customerName: 'Wayne Industries',
        invoiceNumber: 'INV-004',
        invoiceDate: '2023-03-01',
        dueDate: '2023-04-01',
        buckets: {
          current: 1800,
          days1_30: 0,
          days31_60: 0,
          days61_90: 0,
          over90: 0
        },
        amountDue: 1800
      },
      {
        customerId: '5',
        customerName: 'Oscorp',
        invoiceNumber: 'INV-005',
        invoiceDate: '2022-12-15',
        dueDate: '2023-01-15',
        buckets: {
          current: 0,
          days1_30: 0,
          days31_60: 0,
          days61_90: 2700,
          over90: 0
        },
        amountDue: 2700
      }
    ],
    totals: {
      current: 1800,
      days1_30: 2200,
      days31_60: 1500,
      days61_90: 2700,
      over90: 3500,
      totalDue: 11700
    }
  };
  
  // If customerId is provided, filter the data
  if (customerId) {
    const filteredLines = mockData.lines.filter(line => line.customerId === customerId);
    
    // Recalculate totals
    const totals = {
      current: 0,
      days1_30: 0,
      days31_60: 0,
      days61_90: 0,
      over90: 0,
      totalDue: 0
    };
    
    filteredLines.forEach(line => {
      totals.current += line.buckets.current;
      totals.days1_30 += line.buckets.days1_30;
      totals.days31_60 += line.buckets.days31_60;
      totals.days61_90 += line.buckets.days61_90;
      totals.over90 += line.buckets.over90;
      totals.totalDue += line.amountDue;
    });
    
    return {
      ...mockData,
      lines: filteredLines,
      totals
    };
  }
  
  return mockData;
}
