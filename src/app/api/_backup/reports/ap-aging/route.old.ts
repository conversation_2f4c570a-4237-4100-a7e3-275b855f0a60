import { NextRequest, NextResponse } from 'next/server';
import { mockBills, mockBillPayments, mockVendors } from '@/lib/mockData';
import { Bill, BillPayment, Vendor, BillStatus, APAgingReport, APAgingReportLine } from '@/lib/types';
import { reportService } from '@/lib/services/reportService';
import { getMerchantId } from '@/lib/utils/auth';
import { requirePermission } from '@/lib/utils/permissions';
import { PermissionLevel } from '@prisma/client';

// Helper function to calculate the difference in days between two dates
const daysBetween = (date1: Date, date2: Date): number => {
  const oneDay = 24 * 60 * 60 * 1000; // hours*minutes*seconds*milliseconds
  // Ensure we compare date parts only, ignoring time for aging calculation relative to due date
  const date1UTC = Date.UTC(date1.getUTCFullYear(), date1.getUTCMonth(), date1.getUTCDate());
  const date2UTC = Date.UTC(date2.getUTCFullYear(), date2.getUTCMonth(), date2.getUTCDate());
  return Math.floor((date1UTC - date2UTC) / oneDay);
};


export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const asOfDateParam = searchParams.get('asOfDate');

    // Use provided date or default to today
    const asOfDate = asOfDateParam ? new Date(asOfDateParam) : new Date();

    // Get the merchant ID from the session
    const merchantId = await getMerchantId();
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
    }

    // Check permissions
    const permissionCheckGet = await requirePermission(request, merchantId, PermissionLevel.ReadOnly);
    if (permissionCheckGet) {
      console.warn(`GET /api/reports/ap-aging - Permission denied: User does not have ReadOnly level access`);
      return permissionCheckGet;
    }

    // Try to generate the report from the database
    try {
      const report = await reportService.generateAPAgingReport(merchantId, {
        asOfDate: asOfDate || undefined,
      });

      console.log(`GET /api/reports/ap-aging - Generated report as of ${asOfDate} from database`);

      return NextResponse.json(report);
    } catch (dbError) {
      console.error('Database error generating AP aging report:', dbError);
      // Removed fallback to mock data
      return NextResponse.json({ message: 'Failed to generate AP aging report due to database error' }, { status: 500 });
    }
  } catch (error) {
    console.error("Failed to generate AP aging report:", error);
    // Handle specific errors like missing merchantId or permission denied
    if (error instanceof Error) {
      if (error.message.includes('Merchant ID not found')) {
        return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
      }
      if (error.message.includes('Permission denied')) {
        return NextResponse.json({ message: 'Forbidden: Insufficient permissions' }, { status: 403 });
      }
      if (error.message.includes('Invalid date format')) {
        return NextResponse.json({ message: 'Bad Request: Invalid date format' }, { status: 400 });
      }
    }
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}