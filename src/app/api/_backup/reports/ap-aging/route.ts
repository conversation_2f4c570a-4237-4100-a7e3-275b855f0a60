import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/lib/auth/session';
import { proxyGet } from '@/lib/utils/proxy';
import { z } from 'zod';

// GET /api/reports/ap-aging - Proxy to Rust backend
export async function GET(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    return await proxyGet('/reports/ap-aging', request);
  } catch (error) {
    console.error('GET /api/reports/ap-aging error:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}
