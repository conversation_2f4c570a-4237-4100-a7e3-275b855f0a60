import { NextRequest, NextResponse } from 'next/server';
import { ARAgingReport, ARAgingReportLine, Invoice } from '@/lib/types';
import { mockInvoices } from '@/lib/invoicesMockData';
import { mockCustomers } from '@/lib/customersMockData';
import { reportService } from '@/lib/services/reportService';
import { getMerchantId } from '@/lib/utils/auth';
import { requirePermission } from '@/lib/utils/permissions';
import { PermissionLevel } from '@prisma/client';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const asOfDateParam = searchParams.get('asOfDate');

    // Parse date or use current date
    const asOfDate = asOfDateParam ? new Date(asOfDateParam) : new Date();

    // Get the merchant ID from the session
    const merchantId = await getMerchantId();
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
    }

    // Check permissions
    const permissionCheckGet = await requirePermission(request, merchantId, PermissionLevel.ReadOnly);
    if (permissionCheckGet) {
      console.warn(`GET /api/reports/accounts-receivable-aging - Permission denied: User does not have ReadOnly level access`);
      return permissionCheckGet;
    }

    // Try to generate the report from the database
    try {
      const report = await reportService.generateARAgingReport(merchantId, {
        asOfDate: asOfDate || undefined,
      });

      console.log(`GET /api/reports/accounts-receivable-aging - Generated report as of ${asOfDate} from database`);

      return NextResponse.json(report);
    } catch (dbError) {
      console.error('Database error generating AR aging report:', dbError);
      // Removed fallback to mock data
      return NextResponse.json({ message: 'Failed to generate AR aging report due to database error' }, { status: 500 });
    }
  } catch (error) {
    console.error("Failed to generate AR aging report:", error);
    // Handle specific errors like missing merchantId or permission denied
    if (error instanceof Error) {
      if (error.message.includes('Merchant ID not found')) {
        return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
      }
      if (error.message.includes('Permission denied')) {
        return NextResponse.json({ message: 'Forbidden: Insufficient permissions' }, { status: 403 });
      }
      if (error.message.includes('Invalid date format')) {
        return NextResponse.json({ message: 'Bad Request: Invalid date format' }, { status: 400 });
      }
    }
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}
