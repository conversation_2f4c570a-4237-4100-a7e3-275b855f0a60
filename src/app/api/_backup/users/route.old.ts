import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getAuthSession } from '@/lib/auth';
import { z } from 'zod';
import { handleApiError, handleZodError, requireAuth, requireRole } from '@/lib/api-utils';
import bcrypt from 'bcrypt';
import { getMerchantId } from '@/lib/utils/auth';
import { requirePermission } from '@/lib/utils/permissions';
import { PermissionLevel } from '@prisma/client';
import { SubscriptionPlan, SubscriptionStatus } from '@prisma/client';
import { createCustomClient } from '@/utils/supabase/customClient';
import { checkAuth } from '@/utils/api-auth';

// Validation schema for user creation/update
const userSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Invalid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters').optional(),
  role: z.enum(['admin', 'finance', 'staff']).optional(),
  isActive: z.boolean().default(true).optional(),
  image: z.string().optional(),
  merchantIds: z.array(z.string()).optional(),
  defaultMerchantId: z.string().optional(),
});

// GET /api/users - Get all users
export async function GET(request: NextRequest) {
  try {
    // Check authentication using our helper
    const { isAuthenticated, userId, errorResponse } = await checkAuth(request);

    if (!isAuthenticated) {
      console.error('GET /api/users - Authentication failed');
      return errorResponse;
    }

    console.log(`GET /api/users - Authenticated user: ${userId}`);

    // Create a new request with the user ID in the headers
    const requestWithUserId = new NextRequest(request.url, {
      headers: new Headers({
        'x-user-id': userId,
        ...Object.fromEntries(request.headers.entries())
      })
    });

    // Require Admin permission to list all users
    // Note: We don't pass a specific merchantId here, assuming Admin check is global for listing all users.
    // If non-admins need to list users within their merchants, this logic would need refinement.
    const permissionCheckGet = await requirePermission(requestWithUserId, null, PermissionLevel.Admin);
    if (permissionCheckGet) {
      console.warn(`GET /api/users - Permission denied: User does not have Admin level access`);
      return permissionCheckGet;
    }

    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const role = searchParams.get('role');
    const isActive = searchParams.get('is_active');
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const sortBy = searchParams.get('sort_by') || 'name';
    const sortOrder = searchParams.get('sort_order') || 'asc';

    // Build query filters
    const where: any = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (role) {
      where.role = role;
    }

    if (isActive !== null) {
      where.isActive = isActive === 'true';
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Determine sort order
    const orderBy: any = {};
    orderBy[sortBy] = sortOrder;

    // Get total count for pagination
    const totalCount = await prisma.user.count({ where });

    // Query database with pagination and include merchant information
    const users = await prisma.user.findMany({
      where,
      orderBy,
      skip,
      take: limit,
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        isActive: true,
        image: true,
        createdAt: true,
        updatedAt: true,
        merchantPermissions: {
          select: {
            merchantId: true,
            permissionLevel: true,
            merchant: {
              select: {
                id: true,
                name: true,
                logoUrl: true,
              },
            },
          },
        },
        preferences: {
          select: {
            defaultMerchantId: true,
          },
        },
      },
    });

    // Transform the data to include merchant information in a more usable format
    const transformedUsers = users.map(user => {
      const merchants = user.merchantPermissions?.map(permission => ({
        id: permission.merchantId,
        name: permission.merchant.name,
        logoUrl: permission.merchant.logoUrl,
        permissionLevel: permission.permissionLevel,
      })) || [];

      return {
        ...user,
        merchants,
        defaultMerchantId: user.preferences?.defaultMerchantId || null,
        // Remove the raw merchantPermissions and preferences from the response
        merchantPermissions: undefined,
        preferences: undefined,
      };
    });

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPreviousPage = page > 1;

    return NextResponse.json({
      users: transformedUsers,
      pagination: {
        total: totalCount,
        page,
        limit,
        totalPages,
        hasNextPage,
        hasPreviousPage,
      },
    });
  } catch (error) {
    return handleApiError(error);
  }
}

// POST /api/users - Create a new user
export async function POST(request: NextRequest) {
  try {
    // Check authentication using our helper
    const { isAuthenticated, userId, errorResponse } = await checkAuth(request);

    if (!isAuthenticated) {
      console.error('POST /api/users - Authentication failed');
      return errorResponse;
    }

    console.log(`POST /api/users - Authenticated user: ${userId}`);

    // Create a new request with the user ID in the headers
    const requestWithUserId = new NextRequest(request.url, {
      headers: new Headers({
        'x-user-id': userId,
        ...Object.fromEntries(request.headers.entries())
      })
    });

    // Require Admin permission to create users
    // Note: Similar to GET, assuming Admin check is global for creating users.
    const permissionCheckPost = await requirePermission(requestWithUserId, null, PermissionLevel.Admin);
    if (permissionCheckPost) {
      console.warn(`POST /api/users - Permission denied: User does not have Admin level access`);
      return permissionCheckPost;
    }

    const body = await request.json();

    // Validate request body
    try {
      // For new users, password is required
      const createUserSchema = userSchema.extend({
        password: z.string().min(6, 'Password must be at least 6 characters'),
      });
      createUserSchema.parse(body);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return handleZodError(error);
      }
      throw error;
    }

    // Check if user with this email already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: body.email },
    });

    if (existingUser) {
      return NextResponse.json(
        { message: 'User with this email already exists' },
        { status: 409 }
      );
    }

    // Hash password
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(body.password, saltRounds);

    // Create new user in database with transaction to handle merchant associations
    const newUser = await prisma.$transaction(async (tx) => {
      // Create the user
      const user = await tx.user.create({
        data: {
          name: body.name,
          email: body.email,
          passwordHash: hashedPassword,
          role: body.role || 'staff',
          isActive: body.isActive !== undefined ? body.isActive : true,
          image: body.image,
        },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          isActive: true,
          image: true,
          createdAt: true,
          updatedAt: true,
          // Don't include password hash
        },
      });

      // If merchantIds are provided, create merchant permissions
      if (body.merchantIds && body.merchantIds.length > 0) {
        await Promise.all(
          body.merchantIds.map(async (merchantId) => {
            // Create user-merchant permission
            await tx.userMerchantPermission.create({
              data: {
                userId: user.id,
                merchantId,
                permissionLevel: 'Staff', // Default permission level
              },
            });

            // Check if the merchant already has a subscription
            const existingSubscription = await tx.subscription.findUnique({
              where: { merchantId },
            });

            // If no subscription exists, create a free subscription
            if (!existingSubscription) {
              console.log(`Creating free subscription for merchant ${merchantId}`);
              const now = new Date();
              const oneYearFromNow = new Date(now);
              oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);

              await tx.subscription.create({
                data: {
                  merchantId,
                  plan: SubscriptionPlan.Free,
                  status: SubscriptionStatus.Active,
                  currentPeriodStart: now,
                  currentPeriodEnd: oneYearFromNow,
                  cancelAtPeriodEnd: false,
                }
              });
            }
          })
        );
      }

      // If defaultMerchantId is provided, create user preferences
      if (body.defaultMerchantId) {
        await tx.userPreferences.upsert({
          where: { userId: user.id },
          update: { defaultMerchantId: body.defaultMerchantId },
          create: {
            userId: user.id,
            defaultMerchantId: body.defaultMerchantId,
          },
        });
      }

      return user;
    });

    return NextResponse.json(newUser, { status: 201 });
  } catch (error) {
    return handleApiError(error);
  }
}

// PUT /api/users - Update a user
export async function PUT(request: NextRequest) {
  try {
    // Check authentication using our helper
    const { isAuthenticated, userId, errorResponse } = await checkAuth(request);

    if (!isAuthenticated) {
      console.error('PUT /api/users - Authentication failed');
      return errorResponse;
    }

    console.log(`PUT /api/users - Authenticated user: ${userId}`);

    // Only admins can update users (if role exists)
    // if (session.user.role && session.user.role !== 'admin') {
    //   return NextResponse.json({ message: 'Forbidden' }, { status: 403 });
    // }

    const body = await request.json();

    // Validate request body
    try {
      // For updates, password is optional
      userSchema.parse(body);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return handleZodError(error);
      }
      throw error;
    }

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id: body.id },
    });

    if (!existingUser) {
      return NextResponse.json(
        { message: 'User not found' },
        { status: 404 }
      );
    }

    // Prepare update data
    const updateData: any = {
      name: body.name,
      email: body.email,
    };

    if (body.role) updateData.role = body.role;
    if (body.isActive !== undefined) updateData.isActive = body.isActive;
    if (body.image) updateData.image = body.image;

    // Only update password if provided
    if (body.password) {
      const saltRounds = 10;
      updateData.passwordHash = await bcrypt.hash(body.password, saltRounds);
    }

    // Update user in database with transaction to handle merchant associations
    const updatedUser = await prisma.$transaction(async (tx) => {
      // Update the user
      const user = await tx.user.update({
        where: { id: body.id },
        data: updateData,
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          isActive: true,
          image: true,
          createdAt: true,
          updatedAt: true,
          // Don't include password hash
        },
      });

      // If merchantIds are provided, update merchant permissions
      if (body.merchantIds) {
        // First, get current permissions
        const currentPermissions = await tx.userMerchantPermission.findMany({
          where: { userId: body.id },
          select: { merchantId: true },
        });

        const currentMerchantIds = currentPermissions.map(p => p.merchantId);
        const newMerchantIds = body.merchantIds;

        // Remove permissions for merchants not in the new list
        const merchantIdsToRemove = currentMerchantIds.filter(id => !newMerchantIds.includes(id));
        if (merchantIdsToRemove.length > 0) {
          await tx.userMerchantPermission.deleteMany({
            where: {
              userId: body.id,
              merchantId: { in: merchantIdsToRemove },
            },
          });
        }

        // Add permissions for new merchants
        const merchantIdsToAdd = newMerchantIds.filter(id => !currentMerchantIds.includes(id));
        await Promise.all(
          merchantIdsToAdd.map(async (merchantId) => {
            await tx.userMerchantPermission.create({
              data: {
                userId: body.id,
                merchantId,
                permissionLevel: 'Staff', // Default permission level
              },
            });
          })
        );
      }

      // If defaultMerchantId is provided, update user preferences
      if (body.defaultMerchantId) {
        await tx.userPreferences.upsert({
          where: { userId: body.id },
          update: { defaultMerchantId: body.defaultMerchantId },
          create: {
            userId: body.id,
            defaultMerchantId: body.defaultMerchantId,
          },
        });
      }

      return user;
    });

    return NextResponse.json(updatedUser);
  } catch (error) {
    return handleApiError(error);
  }
}

// DELETE /api/users - Delete a user
export async function DELETE(request: NextRequest) {
  try {
    // Check authentication using our helper
    const { isAuthenticated, userId, errorResponse } = await checkAuth(request);

    if (!isAuthenticated) {
      console.error('DELETE /api/users - Authentication failed');
      return errorResponse;
    }

    console.log(`DELETE /api/users - Authenticated user: ${userId}`);

    // Only admins can delete users (if role exists)
    // if (session.user.role && session.user.role !== 'admin') {
    //   return NextResponse.json({ message: 'Forbidden' }, { status: 403 });
    // }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { message: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id },
    });

    if (!existingUser) {
      return NextResponse.json(
        { message: 'User not found' },
        { status: 404 }
      );
    }

    // Prevent deleting the last admin user
    if (existingUser.role === 'admin') {
      const adminCount = await prisma.user.count({
        where: { role: 'admin' },
      });

      if (adminCount <= 1) {
        return NextResponse.json(
          { message: 'Cannot delete the last admin user' },
          { status: 400 }
        );
      }
    }

    // Delete user from database
    await prisma.user.delete({
      where: { id },
    });

    return NextResponse.json({ message: 'User deleted' });
  } catch (error) {
    return handleApiError(error);
  }
}