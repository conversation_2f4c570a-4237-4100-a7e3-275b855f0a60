import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/lib/auth/session';
import { proxyGet, proxyPost, proxyPut, proxyDelete } from '@/lib/utils/proxy';
import { requireBranchPermission, requireOrganizationPermission } from '@/lib/utils/branchPermissions';
import { PermissionLevel } from '@prisma/client';
import { z } from 'zod';

// Zod schema for creating/updating a user
const userSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Invalid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters').optional(),
  role: z.enum(['Admin', 'Finance', 'Staff', 'ReadOnly']).optional(),
  isActive: z.boolean().optional().default(true),
  image: z.string().url('Invalid image URL').optional().nullable(),
  // Legacy merchant support (for backward compatibility)
  merchantIds: z.array(z.string().uuid('Invalid merchant ID format')).optional(),
  defaultMerchantId: z.string().uuid('Invalid merchant ID format').optional().nullable(),
  // New organization and branch support
  organizationIds: z.array(z.string().cuid('Invalid organization ID format')).optional(),
  branchIds: z.array(z.string().cuid('Invalid branch ID format')).optional(),
  defaultOrganizationId: z.string().cuid('Invalid organization ID format').optional().nullable(),
  defaultBranchId: z.string().cuid('Invalid branch ID format').optional().nullable(),
});

// GET /api/users - Get all users
export async function GET(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    console.log(`GET /api/users - User: ${session.user.email}, checking branch/organization permissions`);

    // Get branch or organization context from query parameters
    const { searchParams } = new URL(request.url);
    const branchId = searchParams.get('branchId');
    const organizationId = searchParams.get('organizationId');

    // Check permissions based on context
    if (branchId) {
      // Check branch-level permission (Admin level required to manage users)
      const permissionCheck = await requireBranchPermission(
        request,
        branchId,
        PermissionLevel.Admin
      );
      if (permissionCheck) return permissionCheck;

      console.log(`GET /api/users - User has Admin permission for branch ${branchId}`);
    } else if (organizationId) {
      // Check organization-level permission (Admin level required to manage users)
      const permissionCheck = await requireOrganizationPermission(
        request,
        organizationId,
        PermissionLevel.Admin
      );
      if (permissionCheck) return permissionCheck;

      console.log(`GET /api/users - User has Admin permission for organization ${organizationId}`);
    } else {
      // Fallback to role-based check for backward compatibility
      if (session.user.role !== 'Admin' && session.user.role !== 'Owner') {
        return NextResponse.json(
          { message: 'Forbidden: Admin permission required to view users. Please specify branchId or organizationId.' },
          { status: 403 }
        );
      }
    }

    return await proxyGet('/users', request);
  } catch (error) {
    console.error('GET /api/users error:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}

// POST /api/users - Create a new user
export async function POST(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    console.log(`POST /api/users - User: ${session.user.email}, checking branch/organization permissions`);

    // Parse the request body to get context
    const body = await request.json();
    const branchId = body.branchId || body.defaultBranchId;
    const organizationId = body.organizationId || body.defaultOrganizationId;

    // Check permissions based on context
    if (branchId) {
      // Check branch-level permission (Admin level required to create users)
      const permissionCheck = await requireBranchPermission(
        request,
        branchId,
        PermissionLevel.Admin
      );
      if (permissionCheck) return permissionCheck;

      console.log(`POST /api/users - User has Admin permission for branch ${branchId}`);
    } else if (organizationId) {
      // Check organization-level permission (Admin level required to create users)
      const permissionCheck = await requireOrganizationPermission(
        request,
        organizationId,
        PermissionLevel.Admin
      );
      if (permissionCheck) return permissionCheck;

      console.log(`POST /api/users - User has Admin permission for organization ${organizationId}`);
    } else {
      // Fallback to role-based check for backward compatibility
      if (session.user.role !== 'Admin' && session.user.role !== 'Owner') {
        return NextResponse.json(
          { message: 'Forbidden: Admin permission required to create users. Please specify branchId or organizationId.' },
          { status: 403 }
        );
      }
    }

    // For new users, password is required
    const createUserSchema = userSchema.extend({
      password: z.string().min(6, 'Password must be at least 6 characters'),
    });

    const validationResult = createUserSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { errors: validationResult.error.errors },
        { status: 400 }
      );
    }

    const validatedData = validationResult.data;

    // Transform the validated data to match the Rust backend's expected format
    const rustRequestBody = {
      name: validatedData.name,
      email: validatedData.email,
      password: validatedData.password,
      role: validatedData.role,
      is_active: validatedData.isActive,
      image: validatedData.image,
      // Legacy merchant support
      merchant_ids: validatedData.merchantIds,
      default_merchant_id: validatedData.defaultMerchantId,
      // New organization and branch support
      organization_ids: validatedData.organizationIds,
      branch_ids: validatedData.branchIds,
      default_organization_id: validatedData.defaultOrganizationId,
      default_branch_id: validatedData.defaultBranchId,
    };

    // Proxy to Rust backend
    return await proxyPost('/users', request, rustRequestBody);
  } catch (error) {
    console.error('POST /api/users error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { errors: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof SyntaxError) {
      return NextResponse.json(
        { message: 'Invalid JSON body' },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

// PUT /api/users/:id - Update a user
export async function PUT(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    // Extract the user ID from the URL
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const userId = pathParts[pathParts.length - 1];

    if (!userId) {
      return NextResponse.json({ message: 'User ID is required' }, { status: 400 });
    }

    console.log(`PUT /api/users/${userId} - User: ${session.user.email}, checking permissions`);

    // Parse the request body to get context
    const body = await request.json();
    const branchId = body.branchId || body.defaultBranchId;
    const organizationId = body.organizationId || body.defaultOrganizationId;

    // Allow users to update their own profile with basic permissions
    const isUpdatingSelf = session.user.id === userId;

    if (!isUpdatingSelf) {
      // Check permissions for updating other users
      if (branchId) {
        // Check branch-level permission (Admin level required to update other users)
        const permissionCheck = await requireBranchPermission(
          request,
          branchId,
          PermissionLevel.Admin
        );
        if (permissionCheck) return permissionCheck;

        console.log(`PUT /api/users/${userId} - User has Admin permission for branch ${branchId}`);
      } else if (organizationId) {
        // Check organization-level permission (Admin level required to update other users)
        const permissionCheck = await requireOrganizationPermission(
          request,
          organizationId,
          PermissionLevel.Admin
        );
        if (permissionCheck) return permissionCheck;

        console.log(`PUT /api/users/${userId} - User has Admin permission for organization ${organizationId}`);
      } else {
        // Fallback to role-based check for backward compatibility
        if (session.user.role !== 'Admin' && session.user.role !== 'Owner') {
          return NextResponse.json(
            { message: 'Forbidden: Admin permission required to update other users. Please specify branchId or organizationId.' },
            { status: 403 }
          );
        }
      }
    } else {
      console.log(`PUT /api/users/${userId} - User is updating their own profile`);
    }

    // Validate the request body (already parsed above)
    const validationResult = userSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { errors: validationResult.error.errors },
        { status: 400 }
      );
    }

    const validatedData = validationResult.data;

    // Transform the validated data to match the Rust backend's expected format
    const rustRequestBody = {
      name: validatedData.name,
      email: validatedData.email,
      password: validatedData.password,
      role: validatedData.role,
      is_active: validatedData.isActive,
      image: validatedData.image,
      // Legacy merchant support
      merchant_ids: validatedData.merchantIds,
      default_merchant_id: validatedData.defaultMerchantId,
      // New organization and branch support
      organization_ids: validatedData.organizationIds,
      branch_ids: validatedData.branchIds,
      default_organization_id: validatedData.defaultOrganizationId,
      default_branch_id: validatedData.defaultBranchId,
    };

    return await proxyPut(`/users/${userId}`, request, rustRequestBody);
  } catch (error) {
    console.error('PUT /api/users/:id error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

// DELETE /api/users/:id - Delete a user
export async function DELETE(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    // Extract the user ID from the URL
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const userId = pathParts[pathParts.length - 1];

    if (!userId) {
      return NextResponse.json({ message: 'User ID is required' }, { status: 400 });
    }

    // Prevent deleting yourself
    if (session.user.id === userId) {
      return NextResponse.json(
        { message: 'You cannot delete your own account' },
        { status: 400 }
      );
    }

    console.log(`DELETE /api/users/${userId} - User: ${session.user.email}, checking permissions`);

    // Get branch or organization context from query parameters
    const { searchParams } = new URL(request.url);
    const branchId = searchParams.get('branchId');
    const organizationId = searchParams.get('organizationId');

    // Check permissions based on context
    if (branchId) {
      // Check branch-level permission (Admin level required to delete users)
      const permissionCheck = await requireBranchPermission(
        request,
        branchId,
        PermissionLevel.Admin
      );
      if (permissionCheck) return permissionCheck;

      console.log(`DELETE /api/users/${userId} - User has Admin permission for branch ${branchId}`);
    } else if (organizationId) {
      // Check organization-level permission (Admin level required to delete users)
      const permissionCheck = await requireOrganizationPermission(
        request,
        organizationId,
        PermissionLevel.Admin
      );
      if (permissionCheck) return permissionCheck;

      console.log(`DELETE /api/users/${userId} - User has Admin permission for organization ${organizationId}`);
    } else {
      // Fallback to role-based check for backward compatibility
      if (session.user.role !== 'Admin' && session.user.role !== 'Owner') {
        return NextResponse.json(
          { message: 'Forbidden: Admin permission required to delete users. Please specify branchId or organizationId.' },
          { status: 403 }
        );
      }
    }

    return await proxyDelete(`/users/${userId}`, request);
  } catch (error) {
    console.error('DELETE /api/users/:id error:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}
