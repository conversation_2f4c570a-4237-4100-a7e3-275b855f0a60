import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getAuthSession } from '@/lib/auth';
import { requirePermission } from '@/lib/utils/permissions';
import { PermissionLevel } from '@prisma/client';
import { z } from 'zod';
import { handleApiError, handleZodError } from '@/lib/api-utils';

interface Params {
  params: { id: string };
}

interface UserUpdateData {
  name?: string;
  role?: 'admin' | 'finance' | 'staff'; // Align with Zod schema
  isActive?: boolean;
}

// Zod schema for PUT request body
const updateUserSchema = z.object({
  name: z.string().min(1, 'Name is required').optional(),
  role: z.enum(['admin', 'finance', 'staff']).optional(),
  isActive: z.boolean().optional(),
}).refine(data => Object.keys(data).length > 0, {
  message: "At least one field must be provided for update",
});

// PUT /api/users/[id] - Update user details (e.g., name, role, isActive)
export async function PUT(request: NextRequest, { params }: Params) {
  const session = await getAuthSession();
  const { id: targetUserId } = params;

  // Basic auth check
  if (!session || !session.user?.id) {
    return new NextResponse(JSON.stringify({ message: 'Unauthorized' }), { status: 401 });
  }

  const currentUserId = session.user.id;
  const isUpdatingSelf = currentUserId === targetUserId;

  try {
    const body = await request.json();
    // Validate request body using Zod
    const validationResult = updateUserSchema.safeParse(body);
    if (!validationResult.success) {
      return handleZodError(validationResult.error);
    }
    const { name, role, isActive } = validationResult.data;

     // Prepare update data
     const updateData: UserUpdateData = {};

     // Validate and add name if provided
     if (name !== undefined) {
      updateData.name = name; // Already validated by Zod
     }

     // Handle role and isActive updates (requires Admin or specific logic)
    if (role !== undefined || isActive !== undefined) {
      if (isUpdatingSelf) {
        // Users cannot change their own role or active status via this endpoint
        return new NextResponse(JSON.stringify({ message: 'Forbidden: Cannot update own role or active status' }), { status: 403 });
      } else {
        // Require Admin permission to update other users' role or active status
        // @ts-ignore session.user.role should be available
        if (session.user.role !== "Admin") { // Compare with string value
          console.warn(`PUT /api/users/${targetUserId} - Permission denied: User ${session.user.id} with role ${session.user.role} does not have Admin level access to change role/status`);
          return new NextResponse(JSON.stringify({ message: 'Forbidden: Insufficient permissions' }), { status: 403 });
        }
        // Add role/isActive to updateData only if user is Admin
        if (role !== undefined) updateData.role = role;
        if (isActive !== undefined) updateData.isActive = isActive;
      }
    } else if (!isUpdatingSelf && name === undefined) {
        // If not updating self and only name is provided, still need Admin check
        // @ts-ignore session.user.role should be available
        if (session.user.role !== "Admin") { // Compare with string value
          console.warn(`PUT /api/users/${targetUserId} - Permission denied: User ${session.user.id} with role ${session.user.role} does not have Admin level access to update other users`);
          return new NextResponse(JSON.stringify({ message: 'Forbidden: Insufficient permissions' }), { status: 403 });
        }
    }
    // If only updating own name, no specific permission check needed beyond auth

    // Ensure there's something to update
    // Ensure there's something to update (redundant due to Zod refine, but safe)
    if (Object.keys(updateData).length === 0) {
        return new NextResponse(JSON.stringify({ message: 'No valid fields provided for update' }), { status: 400 });
    }

     // const updatedUser = await prisma.user.update({
     //   where: { id: targetUserId },
     //   data: updateData,
     // });
    // TODO: Add select clause back if this works, or manually construct response
    // return NextResponse.json(updatedUser);
    return NextResponse.json({ message: "User update temporarily disabled pending prisma generate" }); // Placeholder
  } catch (error: any) {
    console.error(`Failed to update user ${targetUserId}:`, error);
    if (error instanceof z.ZodError) { // Catch potential Zod errors from manual checks if any remain
        return handleZodError(error);
    }
     if (error.code === 'P2025') { // Prisma code for record not found
       return new NextResponse(JSON.stringify({ message: 'User not found' }), { status: 404 });
     }
    return handleApiError(error); // Use centralized error handler
   }
 }

// DELETE /api/users/[id] - Delete a user
export async function DELETE(request: NextRequest, { params }: Params) {
  const session = await getAuthSession();
  const { id: targetUserId } = params;

  // Basic auth check
  if (!session || !session.user?.id) {
    return new NextResponse(JSON.stringify({ message: 'Unauthorized' }), { status: 401 });
  }

  const currentUserId = session.user.id;

  // Prevent users from deleting themselves
  if (currentUserId === targetUserId) {
    return new NextResponse(JSON.stringify({ message: 'Cannot delete your own account' }), { status: 403 });
  }

  // Require Admin permission to delete users
  // @ts-ignore session.user.role should be available
  if (session.user.role !== "Admin") { // Compare with string value
    console.warn(`DELETE /api/users/${targetUserId} - Permission denied: User ${session.user.id} with role ${session.user.role} does not have Admin level access`);
    return new NextResponse(JSON.stringify({ message: 'Forbidden: Insufficient permissions' }), { status: 403 });
  }

  try {
    // await prisma.user.delete({
    //   where: { id: targetUserId },
    // });
    // return new NextResponse(null, { status: 204 }); // No Content
    return NextResponse.json({ message: "User delete temporarily disabled pending prisma generate" }); // Placeholder
  } catch (error: any) {
    console.error(`Failed to delete user ${targetUserId}:`, error);
    if (error.code === 'P2025') { // Prisma code for record not found
      return new NextResponse(JSON.stringify({ message: 'User not found' }), { status: 404 });
    }
    return handleApiError(error); // Use centralized error handler
   }
 }