// src/app/api/merchants/[id]/users/route.ts
import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { z } from 'zod';
import { PermissionLevel } from '@prisma/client';
import { requirePermission } from '@/lib/utils/permissions';
import userMerchantPermissionService from '@/lib/services/userMerchantPermissionService';
import { createCustomClient } from '@/utils/supabase/customClient';
import { checkAuth } from '@/utils/api-auth';

// Validation schema for adding/updating user permissions
const userPermissionSchema = z.object({
  userId: z.string().min(1, 'User ID is required'),
  permissionLevel: z.enum([
    PermissionLevel.Owner,
    PermissionLevel.Admin,
    PermissionLevel.Manager,
    PermissionLevel.Staff,
    PermissionLevel.ReadOnly,
  ]),
  customPermissions: z.record(z.boolean()).optional(),
});

// Interface for route params
interface Params {
  params: {
    id: string;
  };
}

// GET /api/merchants/[id]/users - Get all users for a specific merchant
export async function GET(
  request: NextRequest,
  context: { params: { id: string } }
) {
  try {
    const id = context.params.id;

    // Check authentication using our helper
    const { isAuthenticated, userId, errorResponse } = await checkAuth(request);

    if (!isAuthenticated) {
      console.error(`GET /api/merchants/${id}/users - Authentication failed`);
      return errorResponse;
    }

    console.log(`GET /api/merchants/${id}/users - Authenticated user: ${userId}`);

    // Create a new request with the user ID in the headers
    const requestWithUserId = new NextRequest(request.url, {
      headers: new Headers({
        'x-user-id': userId,
        ...Object.fromEntries(request.headers.entries())
      })
    });

    // Check if the user has permission to access this merchant
    const permissionCheck = await requirePermission(requestWithUserId, id, PermissionLevel.ReadOnly);
    if (permissionCheck) return permissionCheck;

    // Get all users with permissions for this merchant
    const merchantUsers = await userMerchantPermissionService.getMerchantUsers(id);

    // Format the response
    const users = merchantUsers.map(permission => ({
      id: permission.user.id,
      name: permission.user.name,
      email: permission.user.email,
      image: permission.user.image,
      role: permission.user.role,
      isActive: permission.user.isActive,
      permissionLevel: permission.permissionLevel,
      customPermissions: permission.customPermissions,
    }));

    return NextResponse.json({ users });
  } catch (error) {
    console.error('Error fetching merchant users:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}

// POST /api/merchants/[id]/users - Add a user to a merchant
export async function POST(
  request: NextRequest,
  context: { params: { id: string } }
) {
  try {
    const id = context.params.id;

    // Check authentication using our helper
    const { isAuthenticated, userId, errorResponse } = await checkAuth(request);

    if (!isAuthenticated) {
      console.error(`POST /api/merchants/${id}/users - Authentication failed`);
      return errorResponse;
    }

    console.log(`POST /api/merchants/${id}/users - Authenticated user: ${userId}`);

    // Create a new request with the user ID in the headers
    const requestWithUserId = new NextRequest(request.url, {
      headers: new Headers({
        'x-user-id': userId,
        ...Object.fromEntries(request.headers.entries())
      })
    });

    // Check if the user has permission to manage users for this merchant
    const permissionCheck = await requirePermission(requestWithUserId, id, PermissionLevel.Admin);
    if (permissionCheck) return permissionCheck;

    // Parse and validate request body
    const body = await request.json();
    const validation = userPermissionSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { message: 'Validation failed', errors: validation.error.format() },
        { status: 400 }
      );
    }

    // Check if the user exists
    const user = await prisma.user.findUnique({
      where: { id: validation.data.userId },
    });

    if (!user) {
      return NextResponse.json({ message: 'User not found' }, { status: 404 });
    }

    // Set the user's permission for this merchant
    const permission = await userMerchantPermissionService.setPermission({
      userId: validation.data.userId,
      merchantId: id,
      permissionLevel: validation.data.permissionLevel,
      customPermissions: validation.data.customPermissions,
    });

    return NextResponse.json(permission, { status: 201 });
  } catch (error) {
    console.error('Error adding user to merchant:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}
