// src/app/api/merchants/[id]/users/[userId]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { z } from 'zod';
import { PermissionLevel } from '@prisma/client';
import { requirePermission } from '@/lib/utils/permissions';
import userMerchantPermissionService from '@/lib/services/userMerchantPermissionService';
import { createCustomClient } from '@/utils/supabase/customClient';
import { checkAuth } from '@/utils/api-auth';

// Validation schema for updating user permissions
const updatePermissionSchema = z.object({
  permissionLevel: z.enum([
    PermissionLevel.Owner,
    PermissionLevel.Admin,
    PermissionLevel.Manager,
    PermissionLevel.Staff,
    PermissionLevel.ReadOnly,
  ]),
  customPermissions: z.record(z.boolean()).optional(),
});

// Interface for route params
interface Params {
  params: {
    id: string;
    userId: string;
  };
}

// GET /api/merchants/[id]/users/[userId] - Get a specific user's permissions for a merchant
export async function GET(request: NextRequest, { params }: Params) {
  try {
    const { id, userId } = params;

    // Check authentication using our helper
    const { isAuthenticated, userId: authenticatedUserId, errorResponse } = await checkAuth(request);

    if (!isAuthenticated) {
      console.error(`GET /api/merchants/${id}/users/${userId} - Authentication failed`);
      return errorResponse;
    }

    console.log(`GET /api/merchants/${id}/users/${userId} - Authenticated user: ${authenticatedUserId}`);

    // Create a new request with the user ID in the headers
    const requestWithUserId = new NextRequest(request.url, {
      headers: new Headers({
        'x-user-id': authenticatedUserId,
        ...Object.fromEntries(request.headers.entries())
      })
    });

    // Check if the user has permission to access this merchant
    const permissionCheck = await requirePermission(requestWithUserId, id, PermissionLevel.ReadOnly);
    if (permissionCheck) return permissionCheck;

    // Get the user's permission for this merchant
    const permission = await userMerchantPermissionService.getPermission(userId, id);

    if (!permission) {
      return NextResponse.json({ message: 'Permission not found' }, { status: 404 });
    }

    // Get the user details
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        name: true,
        email: true,
        image: true,
        role: true,
        isActive: true,
      },
    });

    return NextResponse.json({
      ...permission,
      user,
    });
  } catch (error) {
    console.error('Error fetching user permission:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}

// PUT /api/merchants/[id]/users/[userId] - Update a user's permissions for a merchant
export async function PUT(request: NextRequest, { params }: Params) {
  try {
    const { id, userId } = params;

    // Check authentication using our helper
    const { isAuthenticated, userId: authenticatedUserId, errorResponse } = await checkAuth(request);

    if (!isAuthenticated) {
      console.error(`PUT /api/merchants/${id}/users/${userId} - Authentication failed`);
      return errorResponse;
    }

    console.log(`PUT /api/merchants/${id}/users/${userId} - Authenticated user: ${authenticatedUserId}`);

    // Create a new request with the user ID in the headers
    const requestWithUserId = new NextRequest(request.url, {
      headers: new Headers({
        'x-user-id': authenticatedUserId,
        ...Object.fromEntries(request.headers.entries())
      })
    });

    // Check if the user has permission to manage users for this merchant
    const permissionCheck = await requirePermission(requestWithUserId, id, PermissionLevel.Admin);
    if (permissionCheck) return permissionCheck;

    // Prevent users from changing their own permission level
    if (authenticatedUserId === userId) {
      return NextResponse.json(
        { message: 'You cannot change your own permission level' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validation = updatePermissionSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { message: 'Validation failed', errors: validation.error.format() },
        { status: 400 }
      );
    }

    // Check if the user exists
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return NextResponse.json({ message: 'User not found' }, { status: 404 });
    }

    // Update the user's permission for this merchant
    const permission = await userMerchantPermissionService.setPermission({
      userId,
      merchantId: id,
      permissionLevel: validation.data.permissionLevel,
      customPermissions: validation.data.customPermissions,
    });

    return NextResponse.json(permission);
  } catch (error) {
    console.error('Error updating user permission:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}

// DELETE /api/merchants/[id]/users/[userId] - Remove a user from a merchant
export async function DELETE(request: NextRequest, { params }: Params) {
  try {
    const { id, userId } = params;

    // Check authentication using our helper
    const { isAuthenticated, userId: authenticatedUserId, errorResponse } = await checkAuth(request);

    if (!isAuthenticated) {
      console.error(`DELETE /api/merchants/${id}/users/${userId} - Authentication failed`);
      return errorResponse;
    }

    console.log(`DELETE /api/merchants/${id}/users/${userId} - Authenticated user: ${authenticatedUserId}`);

    // Create a new request with the user ID in the headers
    const requestWithUserId = new NextRequest(request.url, {
      headers: new Headers({
        'x-user-id': authenticatedUserId,
        ...Object.fromEntries(request.headers.entries())
      })
    });

    // Check if the user has permission to manage users for this merchant
    const permissionCheck = await requirePermission(requestWithUserId, id, PermissionLevel.Admin);
    if (permissionCheck) return permissionCheck;

    // Prevent users from removing themselves
    if (authenticatedUserId === userId) {
      return NextResponse.json(
        { message: 'You cannot remove yourself from the merchant' },
        { status: 403 }
      );
    }

    // Check if the permission exists
    const permission = await userMerchantPermissionService.getPermission(userId, id);

    if (!permission) {
      return NextResponse.json({ message: 'Permission not found' }, { status: 404 });
    }

    // Remove the user's permission for this merchant
    await userMerchantPermissionService.removePermission(userId, id);

    return NextResponse.json({ message: 'User removed from merchant successfully' });
  } catch (error) {
    console.error('Error removing user from merchant:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}
