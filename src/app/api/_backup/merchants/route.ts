import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/lib/auth/session';
import { proxyGet, proxyPost, proxyPut, proxyDelete } from '@/lib/utils/proxy';
import { z } from 'zod';
import prisma from '@/lib/prisma';

// Zod schema for creating/updating a merchant
const merchantSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  address: z.string().optional().nullable(),
  phone: z.string().optional().nullable(),
  primaryContactEmail: z.string().email('Invalid email address').optional().nullable(),
  website: z.string().url('Invalid website URL').optional().nullable(),
  taxId: z.string().optional().nullable(),
  fiscalYearStart: z.string().optional().nullable(),
  currency: z.string().optional().nullable(),
  logoUrl: z.string().optional().nullable(),
  legalName: z.string().optional().nullable(),
  isActive: z.boolean().optional().default(true),
});

// GET /api/merchants - Get all merchants the user has access to
export async function GET(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    // For non-super admins, return merchants they have access to from the database
    if (!session.user.isSuperAdmin) {
      console.log(`GET /api/merchants - Non-super admin user: ${session.user.email}, returning accessible merchants`);

      try {
        // Get merchants the user has permissions for
        const userPermissions = await prisma.userMerchantPermission.findMany({
          where: {
            userId: session.user.id,
          },
          include: {
            merchant: true,
          },
        });

        const merchants = userPermissions.map(permission => ({
          id: permission.merchant.id,
          name: permission.merchant.name,
          description: permission.merchant.description,
          isActive: permission.merchant.isActive,
          createdAt: permission.merchant.createdAt,
          updatedAt: permission.merchant.updatedAt,
          // Add permission level for context
          userPermissionLevel: permission.permissionLevel,
        }));

        console.log(`Found ${merchants.length} accessible merchants for user ${session.user.email}`);
        return NextResponse.json({ merchants });
      } catch (dbError) {
        console.error('Database error when fetching user merchants:', dbError);

        // Fallback: return default merchant if user has one
        if (session.user.defaultMerchantId) {
          const defaultMerchant = await prisma.merchant.findUnique({
            where: { id: session.user.defaultMerchantId },
          });

          if (defaultMerchant) {
            return NextResponse.json({
              merchants: [{
                id: defaultMerchant.id,
                name: defaultMerchant.name,
                description: defaultMerchant.description,
                isActive: defaultMerchant.isActive,
                createdAt: defaultMerchant.createdAt,
                updatedAt: defaultMerchant.updatedAt,
                userPermissionLevel: 'ReadOnly', // Default permission
              }]
            });
          }
        }

        // If all else fails, return empty array
        return NextResponse.json({ merchants: [] });
      }
    }

    // For super admins, proxy to Rust backend
    return await proxyGet('/merchants', request);
  } catch (error) {
    console.error('GET /api/merchants error:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}

// POST /api/merchants - Create a new merchant
export async function POST(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    // Allow users to create merchants if they don't have any, or if they're super admins
    if (!session.user.isSuperAdmin) {
      console.log(`POST /api/merchants - Non-super admin user: ${session.user.email}, checking if they can create a merchant`);

      // Check if user has any existing merchants
      const existingPermissions = await prisma.userMerchantPermission.findMany({
        where: {
          userId: session.user.id,
        },
      });

      if (existingPermissions.length > 0) {
        return NextResponse.json(
          { message: 'Forbidden: You already have access to merchants. Only super admins can create additional merchants.' },
          { status: 403 }
        );
      }

      console.log(`User ${session.user.email} has no existing merchants, allowing creation of first merchant`);
    }

    // Parse and validate the request body
    const body = await request.json();
    const validationResult = merchantSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { errors: validationResult.error.errors },
        { status: 400 }
      );
    }

    const validatedData = validationResult.data;

    // For super admins, proxy to Rust backend
    if (session.user.isSuperAdmin) {
      // Transform the validated data to match the Rust backend's expected format
      const rustRequestBody = {
        name: validatedData.name,
        address: validatedData.address,
        phone: validatedData.phone,
        primary_contact_email: validatedData.primaryContactEmail,
        website: validatedData.website,
        tax_id: validatedData.taxId,
        fiscal_year_start: validatedData.fiscalYearStart,
        currency: validatedData.currency,
        logo_url: validatedData.logoUrl,
        legal_name: validatedData.legalName,
        is_active: validatedData.isActive,
      };

      return await proxyPost('/merchants', request, rustRequestBody);
    }

    // For regular users creating their first merchant, create directly in database
    console.log(`Creating first merchant for user ${session.user.email}`);

    const merchant = await prisma.merchant.create({
      data: {
        name: validatedData.name,
        address: validatedData.address,
        phone: validatedData.phone,
        primaryContactEmail: validatedData.primaryContactEmail,
        website: validatedData.website,
        taxId: validatedData.taxId,
        fiscalYearStart: validatedData.fiscalYearStart,
        currency: validatedData.currency || 'USD',
        logoUrl: validatedData.logoUrl,
        legalName: validatedData.legalName,
        isActive: validatedData.isActive ?? true,
      },
    });

    console.log(`Created merchant ${merchant.id} for user ${session.user.email}`);

    // Create Owner permission for the user
    const permission = await prisma.userMerchantPermission.create({
      data: {
        userId: session.user.id,
        merchantId: merchant.id,
        permissionLevel: 'Owner',
      },
    });

    console.log(`Created Owner permission for user ${session.user.email} on merchant ${merchant.id}`);

    // Update user's default merchant ID if not set
    if (!session.user.defaultMerchantId) {
      await prisma.user.update({
        where: { id: session.user.id },
        data: { defaultMerchantId: merchant.id },
      });
      console.log(`Set default merchant ID to ${merchant.id} for user ${session.user.email}`);
    }

    return NextResponse.json({
      id: merchant.id,
      name: merchant.name,
      description: merchant.description,
      isActive: merchant.isActive,
      createdAt: merchant.createdAt,
      updatedAt: merchant.updatedAt,
      userPermissionLevel: permission.permissionLevel,
    }, { status: 201 });
  } catch (error) {
    console.error('POST /api/merchants error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { errors: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof SyntaxError) {
      return NextResponse.json(
        { message: 'Invalid JSON body' },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

// PUT /api/merchants/:id - Update a merchant
export async function PUT(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    // Extract the merchant ID from the URL
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const merchantId = pathParts[pathParts.length - 1];

    if (!merchantId) {
      return NextResponse.json({ message: 'Merchant ID is required' }, { status: 400 });
    }

    // Check if user has access to this merchant
    if (!session.user.isSuperAdmin && session.user.defaultMerchantId !== merchantId) {
      return NextResponse.json(
        { message: 'Forbidden: You do not have access to this merchant' },
        { status: 403 }
      );
    }

    // Parse and validate the request body
    const body = await request.json();
    const validationResult = merchantSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { errors: validationResult.error.errors },
        { status: 400 }
      );
    }

    const validatedData = validationResult.data;

    // Transform the validated data to match the Rust backend's expected format
    const rustRequestBody = {
      name: validatedData.name,
      address: validatedData.address,
      phone: validatedData.phone,
      primary_contact_email: validatedData.primaryContactEmail,
      website: validatedData.website,
      tax_id: validatedData.taxId,
      fiscal_year_start: validatedData.fiscalYearStart,
      currency: validatedData.currency,
      logo_url: validatedData.logoUrl,
      legal_name: validatedData.legalName,
      is_active: validatedData.isActive,
    };

    return await proxyPut(`/merchants/${merchantId}`, request, rustRequestBody);
  } catch (error) {
    console.error('PUT /api/merchants/:id error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

// DELETE /api/merchants/:id - Delete a merchant
export async function DELETE(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    // Only super admins can delete merchants
    if (!session.user.isSuperAdmin) {
      return NextResponse.json(
        { message: 'Forbidden: Only super admins can delete merchants' },
        { status: 403 }
      );
    }

    // Extract the merchant ID from the URL
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const merchantId = pathParts[pathParts.length - 1];

    if (!merchantId) {
      return NextResponse.json({ message: 'Merchant ID is required' }, { status: 400 });
    }

    return await proxyDelete(`/merchants/${merchantId}`, request);
  } catch (error) {
    console.error('DELETE /api/merchants/:id error:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}
