// src/app/api/merchants/route.ts
import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { z } from 'zod';
import { PermissionLevel, SubscriptionPlan, SubscriptionStatus } from '@prisma/client';
import { requirePermission } from '@/lib/utils/permissions';
import userMerchantPermissionService from '@/lib/services/userMerchantPermissionService';
import { createCustomClient } from '@/utils/supabase/customClient';
import { checkAuth } from '@/utils/api-auth';

// Validation schema for merchant creation/update
const merchantSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  address: z.string().optional().nullable(),
  phone: z.string().optional().nullable(),
  primaryContactEmail: z.string().email('Invalid email address').optional().nullable(),
  website: z.string().url('Invalid website URL').optional().nullable(),
  taxId: z.string().optional().nullable(),
  fiscalYearStart: z.string().optional().nullable(),
  currency: z.string().optional().nullable(),
  logoUrl: z.string().optional().nullable(),
  legalName: z.string().optional().nullable(),
});

// GET /api/merchants - Get all merchants the user has access to
export async function GET(request: NextRequest) {
  try {
    // Check authentication using our helper
    const { isAuthenticated, userId, errorResponse } = await checkAuth(request);

    if (!isAuthenticated) {
      console.error('GET /api/merchants - Authentication failed');
      return errorResponse;
    }

    console.log(`GET /api/merchants - Authenticated user: ${userId}`);

    // Get all merchants the user has access to
    const userPermissions = await userMerchantPermissionService.getUserPermissions(userId);

    // If no permissions found, return empty array
    if (!userPermissions || !userPermissions.length) {
      return NextResponse.json({ merchants: [] });
    }

    // Format the response
    const merchants = userPermissions.map(permission => ({
      id: permission.merchant?.id || '',
      name: permission.merchant?.name || 'Unknown Merchant',
      logoUrl: permission.merchant?.logoUrl || null,
      permissionLevel: permission.permissionLevel,
    }));

    return NextResponse.json({ merchants });
  } catch (error) {
    console.error('Error fetching merchants:', error);
    return NextResponse.json({ merchants: [] });
  }
}

// POST /api/merchants - Create a new merchant
export async function POST(request: NextRequest) {
  // Declare variables at the function scope so they're available in the catch block
  let userId: string | null = null;
  let userEmail: string | null = null;
  let userName: string | null = null;

  try {
    // Check authentication using our helper
    const { isAuthenticated, userId: authUserId, userEmail: authUserEmail, userName: authUserName, errorResponse } = await checkAuth(request);
    userId = authUserId;
    userEmail = authUserEmail || null;
    userName = authUserName || null;

    if (!isAuthenticated) {
      console.error('POST /api/merchants - Authentication failed');
      return errorResponse;
    }

    console.log(`POST /api/merchants - Authenticated user: ${userId}`);
    console.log(`Creating merchant for user ID: ${userId}`);

    // Parse and validate request body
    const body = await request.json();
    const validation = merchantSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { message: 'Validation failed', errors: validation.error.format() },
        { status: 400 }
      );
    }

    console.log(`Creating merchant with data:`, validation.data);

    // First, check if the user exists in the database
    const userExists = await prisma.user.findUnique({
      where: { id: userId },
      select: { id: true }
    });

    // If user doesn't exist in the database, create it with the information we have
    if (!userExists) {
      console.log(`User ${userId} not found in database, creating user record...`);

      // Use the email from the checkAuth response
      if (!userEmail) {
        console.error('No email available for user creation');
        return NextResponse.json({
          message: 'Failed to create user: No email available',
        }, { status: 400 });
      }

      console.log(`Creating user with ID: ${userId} and email: ${userEmail}`);

      // Create the user in the database with minimal information
      try {
        const newUser = await prisma.user.create({
          data: {
            id: userId,
            email: userEmail,
            name: userName,
            // We don't have these values, but they're optional
            emailVerified: null,
            image: null,
          }
        });
        console.log(`Created user record in database for ${userId}:`, newUser);
      } catch (userCreateError) {
        console.error('Error creating user record:', userCreateError);

        // Check if it's a unique constraint violation (user might already exist)
        if (userCreateError.code === 'P2002') {
          console.log('User already exists in database (unique constraint violation)');
          // Continue with merchant creation since the user exists
        } else {
          return NextResponse.json({
            message: 'Failed to create user record in database',
            error: userCreateError.message,
            code: userCreateError.code
          }, { status: 500 });
        }
      }
    } else {
      console.log(`User ${userId} found in database, proceeding with merchant creation`);
    }

    // Use a transaction to ensure all operations succeed or fail together
    const result = await prisma.$transaction(async (tx) => {
      // Step 1: Create the merchant
      const merchant = await tx.merchant.create({
        data: validation.data,
      });

      console.log(`Created merchant with ID: ${merchant.id}`);

      // Step 2: Create the permission
      const permission = await tx.userMerchantPermission.create({
        data: {
          userId: userId,
          merchantId: merchant.id,
          permissionLevel: PermissionLevel.Owner,
        },
      });

      console.log(`Created permission with ID: ${permission.id}`);

      // Step 3: Create a free subscription for the merchant
      const now = new Date();
      const oneYearFromNow = new Date(now);
      oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);

      const subscription = await tx.subscription.create({
        data: {
          merchantId: merchant.id,
          plan: SubscriptionPlan.Free,
          status: SubscriptionStatus.Active,
          currentPeriodStart: now,
          currentPeriodEnd: oneYearFromNow,
          cancelAtPeriodEnd: false,
        }
      });

      console.log(`Created free subscription for merchant: ${subscription.id}`);

      return { merchant, permission, subscription };
    });

    const merchant = result.merchant;

    console.log(`Merchant creation completed successfully`);
    return NextResponse.json({
      ...merchant,
      subscription: {
        plan: result.subscription.plan,
        status: result.subscription.status,
        currentPeriodEnd: result.subscription.currentPeriodEnd
      }
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating merchant:', error);

    // Check for specific error types
    if (error.code === 'P2003') {
      if (error.meta?.field_name?.includes('userId')) {
        return NextResponse.json(
          { message: 'Failed to create merchant: User ID not found in database. Please log out and log back in.' },
          { status: 400 }
        );
      }

      // Try to get information about the user to provide better error messages
      try {
        const userExists = await prisma.user.findUnique({
          where: { id: userId },
          select: { id: true, name: true, email: true }
        });

        if (!userExists) {
          return NextResponse.json({
            message: `User with ID ${userId} not found in database. Please log out and log back in.`,
          }, { status: 400 });
        } else {
          return NextResponse.json({
            message: `Database constraint error: ${error.message}`,
          }, { status: 400 });
        }
      } catch (userCheckError) {
        console.error('Error checking user existence:', userCheckError);
        return NextResponse.json({
          message: 'Failed to verify user in database',
          error: error.message
        }, { status: 500 });
      }
    }

    return NextResponse.json({
      message: 'Internal Server Error',
      error: error.message || 'Unknown error'
    }, { status: 500 });
  }
}
