import { NextRequest, NextResponse } from 'next/server';
import { getAuthSession } from '@/lib/auth';
import { UserPreferencesSchema } from '@/lib/validators/preferences';
import { userSettingsService } from '@/lib/services/userSettingsService';
import prisma from '@/lib/prisma';

// Mock data for fallback
const mockUserPreferences = {
  id: 'pref-123',
  user_id: 'user-123',
  theme: 'system',
  date_format: 'MM/DD/YYYY',
  time_format: '12h',
  number_format: 'en-US',
  currency_format: 'USD',
  language: 'en',
  start_page: '/dashboard',
  notifications: {
    email: true,
    browser: true,
    mobile: false,
  },
  display_density: 'comfortable',
  table_page_size: 10
};

/**
 * Format user preferences from database model to API response format
 */
function formatUserPreferencesResponse(preferences: any) {
  return {
    id: preferences.id,
    user_id: preferences.userId,
    theme: preferences.theme,
    date_format: preferences.dateFormat,
    time_format: preferences.timeFormat,
    number_format: preferences.numberFormat,
    currency_format: preferences.currencyFormat,
    language: preferences.language,
    start_page: preferences.startPage,
    notifications: preferences.notifications,
    display_density: preferences.displayDensity,
    table_page_size: preferences.tablePageSize,
  };
}

export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  // Get the authenticated session
  const session = await getAuthSession();

  // Get userId from params
  const { userId } = await Promise.resolve(params);

  // In production, we should check if the user is authorized to access these preferences
  // Only allow access to your own preferences unless you're an admin
  if (process.env.NODE_ENV !== 'development') {
    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    // Only allow users to access their own preferences (unless they're an admin)
    if (session.user.id !== userId && session.user.role !== 'Admin') {
      return NextResponse.json({ message: 'Forbidden' }, { status: 403 });
    }
  }

  try {
    // Try to fetch from the database using the service
    const userPreferences = await userSettingsService.findByUserId(userId);

    if (userPreferences) {
      // Transform to match the expected format
      return NextResponse.json(formatUserPreferencesResponse(userPreferences));
    }

    // If in development mode and no preferences found, return mock data
    if (process.env.NODE_ENV === 'development') {
      console.log(`No preferences found for user ${userId}, returning mock data`);
      return NextResponse.json(mockUserPreferences);
    }

    // In production, return a 404 if no preferences found
    return NextResponse.json({ message: 'Preferences not found' }, { status: 404 });
  } catch (error) {
    console.error('Error fetching user preferences:', error);

    // Fall back to mock data only in development
    if (process.env.NODE_ENV === 'development') {
      return NextResponse.json(mockUserPreferences);
    }

    // In production, return a proper error
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  // Get the authenticated session
  const session = await getAuthSession();

  // Get userId from params
  const { userId } = await Promise.resolve(params);

  // In production, we should check if the user is authorized to update these preferences
  // Only allow updating your own preferences unless you're an admin
  if (process.env.NODE_ENV !== 'development') {
    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    // Only allow users to update their own preferences (unless they're an admin)
    if (session.user.id !== userId && session.user.role !== 'Admin') {
      return NextResponse.json({ message: 'Forbidden' }, { status: 403 });
    }
  }

  try {
    const body = await request.json();
    const validation = UserPreferencesSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json({ message: 'Invalid input', errors: validation.error.format() }, { status: 400 });
    }

    // Prepare data for database
    const {
      theme,
      dateFormat,
      timeFormat,
      numberFormat,
      currencyFormat,
      language,
      startPage,
      notifications,
      displayDensity,
      tablePageSize
    } = validation.data;

    // Try to update or create preferences using the service
    const userPreferences = await userSettingsService.upsertPreferences(userId, {
      theme,
      dateFormat,
      timeFormat,
      numberFormat,
      currencyFormat,
      language,
      startPage,
      notifications,
      displayDensity,
      tablePageSize,
    });

    // Transform to match the expected format
    return NextResponse.json(formatUserPreferencesResponse(userPreferences));
  } catch (error) {
    console.error('Error updating user preferences:', error);

    // Fall back to mock data only in development
    if (process.env.NODE_ENV === 'development') {
      return NextResponse.json(mockUserPreferences, { status: 500 });
    }

    // In production, return a proper error
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}
