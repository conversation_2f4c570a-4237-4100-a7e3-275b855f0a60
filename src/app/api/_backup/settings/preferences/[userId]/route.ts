import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/lib/auth/session';
import { proxyGet, proxyPut } from '@/lib/utils/proxy';
import { z } from 'zod';
import { UserPreferencesSchema } from '@/lib/validators/preferences';

// GET /api/settings/preferences/[userId] - Proxy to Rust backend
export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    // Only allow users to access their own preferences (unless they're an admin)
    if (session.user.id !== params.userId && session.user.role !== 'Admin') {
      return NextResponse.json({ message: 'Forbidden' }, { status: 403 });
    }

    return await proxyGet(`/settings/preferences/${params.userId}`, request);
  } catch (error) {
    console.error(`GET /api/settings/preferences/${params.userId} error:`, error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}

// PUT /api/settings/preferences/[userId] - Validate and proxy to Rust backend
export async function PUT(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    // Only allow users to update their own preferences (unless they're an admin)
    if (session.user.id !== params.userId && session.user.role !== 'Admin') {
      return NextResponse.json({ message: 'Forbidden' }, { status: 403 });
    }

    // Parse and validate the request body
    const body = await request.json();
    const validationResult = UserPreferencesSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { errors: validationResult.error.errors },
        { status: 400 }
      );
    }

    const validatedData = validationResult.data;
    
    // Transform the validated data to match the Rust backend's expected format
    const rustRequestBody = {
      theme: validatedData.theme,
      date_format: validatedData.dateFormat,
      time_format: validatedData.timeFormat,
      number_format: validatedData.numberFormat,
      currency_format: validatedData.currencyFormat,
      language: validatedData.language,
      start_page: validatedData.startPage,
      notifications: validatedData.notifications,
      display_density: validatedData.displayDensity,
      table_page_size: validatedData.tablePageSize,
    };

    // Proxy to Rust backend
    return await proxyPut(`/settings/preferences/${params.userId}`, request, rustRequestBody);
  } catch (error) {
    console.error(`PUT /api/settings/preferences/${params.userId} error:`, error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { errors: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { message: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
