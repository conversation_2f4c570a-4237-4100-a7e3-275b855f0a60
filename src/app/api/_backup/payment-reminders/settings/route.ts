import { NextRequest, NextResponse } from 'next/server';
import { getMerchantId } from '@/lib/utils/auth';
import { requirePermission } from '@/lib/utils/permissions';
import { PermissionLevel } from '@prisma/client';
import prisma from '@/lib/prisma';

// GET /api/payment-reminders/settings - Get payment reminder settings
export async function GET(request: NextRequest) {
  try {
    // Get merchant ID from session
    const merchantId = await getMerchantId(request);
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized - Merchant ID not found' }, { status: 401 });
    }

    // Check if the user has permission to view settings (ReadOnly level or higher)
    const permissionCheckGet = await requirePermission(request, merchantId, PermissionLevel.ReadOnly);
    if (permissionCheckGet) {
      console.warn('GET /api/payment-reminders/settings - Permission denied: User does not have ReadOnly level access');
      return permissionCheckGet;
    }

    // Try to fetch from the database
    try {
      // Get the merchant's payment reminder settings
      const settings = await prisma.paymentReminderSettings.findUnique({
        where: {
          merchantId,
        },
      });

      // If settings don't exist, return default settings
      if (!settings) {
        return NextResponse.json({
          enabled: true,
          auto_send_reminders: false,
          default_email_template_id: null,
          upcoming_reminder_days: 3,
          overdue_reminder_days: [1, 7, 14, 30],
        });
      }

      // Format the response
      return NextResponse.json({
        enabled: settings.enabled,
        auto_send_reminders: settings.autoSendReminders,
        default_email_template_id: settings.defaultEmailTemplateId,
        upcoming_reminder_days: settings.upcomingReminderDays,
        overdue_reminder_days: settings.overdueReminderDays,
      });
    } catch (dbError) {
      console.error('Database error, falling back to mock data:', dbError);

      // Fall back to mock data if database fails
      return NextResponse.json({
        enabled: true,
        auto_send_reminders: false,
        default_email_template_id: null,
        upcoming_reminder_days: 3,
        overdue_reminder_days: [1, 7, 14, 30],
      });
    }
  } catch (error) {
    console.error('Failed to fetch payment reminder settings:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}

// PUT /api/payment-reminders/settings - Update payment reminder settings
export async function PUT(request: NextRequest) {
  try {
    // Get merchant ID from session
    const merchantId = await getMerchantId(request);
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized - Merchant ID not found' }, { status: 401 });
    }

    // Check if the user has permission to update settings (Admin level or higher)
    const permissionCheckPut = await requirePermission(request, merchantId, PermissionLevel.Admin);
    if (permissionCheckPut) {
      console.warn('PUT /api/payment-reminders/settings - Permission denied: User does not have Admin level access');
      return permissionCheckPut;
    }

    const body = await request.json();

    // Validate the request body
    if (body.enabled === undefined && 
        body.auto_send_reminders === undefined && 
        body.default_email_template_id === undefined &&
        body.upcoming_reminder_days === undefined &&
        body.overdue_reminder_days === undefined) {
      return NextResponse.json({ 
        message: 'No valid update fields provided' 
      }, { status: 400 });
    }

    // Validate overdue_reminder_days if provided
    if (body.overdue_reminder_days !== undefined) {
      if (!Array.isArray(body.overdue_reminder_days)) {
        return NextResponse.json({ 
          message: 'overdue_reminder_days must be an array of numbers' 
        }, { status: 400 });
      }

      // Ensure all values are numbers
      for (const days of body.overdue_reminder_days) {
        if (typeof days !== 'number' || days < 0) {
          return NextResponse.json({ 
            message: 'overdue_reminder_days must contain positive numbers' 
          }, { status: 400 });
        }
      }
    }

    // Validate upcoming_reminder_days if provided
    if (body.upcoming_reminder_days !== undefined) {
      if (typeof body.upcoming_reminder_days !== 'number' || body.upcoming_reminder_days < 0) {
        return NextResponse.json({ 
          message: 'upcoming_reminder_days must be a positive number' 
        }, { status: 400 });
      }
    }

    // Try to update in the database
    try {
      // Check if email template exists if provided
      if (body.default_email_template_id) {
        const emailTemplate = await prisma.emailTemplate.findFirst({
          where: {
            id: body.default_email_template_id,
            merchantId,
          },
        });

        if (!emailTemplate) {
          return NextResponse.json({ message: 'Email template not found' }, { status: 404 });
        }
      }

      // Prepare the update data
      const updateData: any = {};
      
      if (body.enabled !== undefined) updateData.enabled = body.enabled;
      if (body.auto_send_reminders !== undefined) updateData.autoSendReminders = body.auto_send_reminders;
      if (body.default_email_template_id !== undefined) updateData.defaultEmailTemplateId = body.default_email_template_id;
      if (body.upcoming_reminder_days !== undefined) updateData.upcomingReminderDays = body.upcoming_reminder_days;
      if (body.overdue_reminder_days !== undefined) updateData.overdueReminderDays = body.overdue_reminder_days;

      // Update or create the settings
      const settings = await prisma.paymentReminderSettings.upsert({
        where: {
          merchantId,
        },
        update: updateData,
        create: {
          merchantId,
          enabled: body.enabled !== undefined ? body.enabled : true,
          autoSendReminders: body.auto_send_reminders !== undefined ? body.auto_send_reminders : false,
          defaultEmailTemplateId: body.default_email_template_id !== undefined ? body.default_email_template_id : null,
          upcomingReminderDays: body.upcoming_reminder_days !== undefined ? body.upcoming_reminder_days : 3,
          overdueReminderDays: body.overdue_reminder_days !== undefined ? body.overdue_reminder_days : [1, 7, 14, 30],
        },
      });

      // Format the response
      return NextResponse.json({
        enabled: settings.enabled,
        auto_send_reminders: settings.autoSendReminders,
        default_email_template_id: settings.defaultEmailTemplateId,
        upcoming_reminder_days: settings.upcomingReminderDays,
        overdue_reminder_days: settings.overdueReminderDays,
      });
    } catch (dbError) {
      console.error('Database error, falling back to mock data:', dbError);

      // Fall back to mock data if database fails
      return NextResponse.json({
        enabled: body.enabled !== undefined ? body.enabled : true,
        auto_send_reminders: body.auto_send_reminders !== undefined ? body.auto_send_reminders : false,
        default_email_template_id: body.default_email_template_id !== undefined ? body.default_email_template_id : null,
        upcoming_reminder_days: body.upcoming_reminder_days !== undefined ? body.upcoming_reminder_days : 3,
        overdue_reminder_days: body.overdue_reminder_days !== undefined ? body.overdue_reminder_days : [1, 7, 14, 30],
      });
    }
  } catch (error) {
    console.error('Failed to update payment reminder settings:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}
