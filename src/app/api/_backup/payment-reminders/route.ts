import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/lib/auth/session';
import { proxyGet, proxyPost, proxyPut, proxyDelete } from '@/lib/utils/proxy';
import { z } from 'zod';
import { PaymentReminderStatus } from '@/lib/types';

// Zod schema for creating a payment reminder
const createPaymentReminderSchema = z.object({
  invoiceId: z.string().uuid('Invalid invoice ID format'),
  reminderDate: z.string().refine(val => {
    try {
      return new Date(val).toISOString();
    } catch (e) {
      return false;
    }
  }, { message: "Invalid reminder date format" }),
  emailTemplateId: z.string().uuid('Invalid email template ID format').optional(),
  notes: z.string().optional(),
  status: z.nativeEnum(PaymentReminderStatus).optional().default(PaymentReminderStatus.Scheduled),
});

// GET /api/payment-reminders - Proxy to Rust backend
export async function GET(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    return await proxyGet('/payment-reminders', request);
  } catch (error) {
    console.error('GET /api/payment-reminders error:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}

// POST /api/payment-reminders - Validate and proxy to Rust backend
export async function POST(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    // Parse and validate the request body
    const body = await request.json();
    const validationResult = createPaymentReminderSchema.safeParse({
      invoiceId: body.invoice_id,
      reminderDate: body.reminder_date,
      emailTemplateId: body.email_template_id,
      notes: body.notes,
      status: body.status,
    });
    
    if (!validationResult.success) {
      return NextResponse.json(
        { errors: validationResult.error.errors },
        { status: 400 }
      );
    }

    const validatedData = validationResult.data;
    
    // Transform the validated data to match the Rust backend's expected format
    const rustRequestBody = {
      invoice_id: validatedData.invoiceId,
      reminder_date: validatedData.reminderDate,
      email_template_id: validatedData.emailTemplateId,
      notes: validatedData.notes,
      status: validatedData.status,
    };

    // Proxy to Rust backend
    return await proxyPost('/payment-reminders', request, rustRequestBody);
  } catch (error) {
    console.error('POST /api/payment-reminders error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { errors: error.errors },
        { status: 400 }
      );
    }
    
    if (error instanceof SyntaxError) {
      return NextResponse.json(
        { message: 'Invalid JSON body' },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { message: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

// PUT /api/payment-reminders/:id - Update a payment reminder
export async function PUT(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    // Extract the reminder ID from the URL
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const reminderId = pathParts[pathParts.length - 1];

    if (!reminderId) {
      return NextResponse.json({ message: 'Reminder ID is required' }, { status: 400 });
    }

    return await proxyPut(`/payment-reminders/${reminderId}`, request);
  } catch (error) {
    console.error('PUT /api/payment-reminders/:id error:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}

// DELETE /api/payment-reminders/:id - Delete a payment reminder
export async function DELETE(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    // Extract the reminder ID from the URL
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const reminderId = pathParts[pathParts.length - 1];

    if (!reminderId) {
      return NextResponse.json({ message: 'Reminder ID is required' }, { status: 400 });
    }

    return await proxyDelete(`/payment-reminders/${reminderId}`, request);
  } catch (error) {
    console.error('DELETE /api/payment-reminders/:id error:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}
