import { NextRequest, NextResponse } from 'next/server';
import { PaymentReminderStatus } from '@/lib/types';
import { paymentReminderService } from '@/lib/services/paymentReminderService';
import { emailService } from '@/lib/services/emailService'; // Assuming email service exists
import { getMerchantId } from '@/lib/utils/auth';
import { requirePermission } from '@/lib/utils/permissions';
import { PermissionLevel } from '@prisma/client';
import { emailLogService } from '@/lib/services/emailLogService';
import {
  // Keep mock data imports for fallback
  findMockPaymentReminderById,
  updateMockPaymentReminder,
  findUpcomingMockPaymentReminders
} from '@/lib/accountsReceivableMockData';
import { findMockInvoiceById } from '@/lib/invoicesMockData';
import { findMockCustomerById } from '@/lib/customersMockData';
import {
  findMockEmailTemplateById,
  findDefaultMockEmailTemplate,
  sendMockEmail
} from '@/lib/emailMockData';
import { z } from 'zod';

// POST /api/payment-reminders/batch - Process multiple payment reminders
export async function POST(request: NextRequest) {
  try {
    // Get merchant ID from session
    const merchantId = await getMerchantId(request);
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized - Merchant ID not found' }, { status: 401 });
    }

    // Check if the user has permission to process payment reminders (Staff level or higher)
    const permissionCheck = await requirePermission(request, merchantId, PermissionLevel.Staff);
    if (permissionCheck) {
      console.warn('POST /api/payment-reminders/batch - Permission denied: User does not have Staff level access');
      return permissionCheck;
    }

    const body = await request.json();

    // Get parameters
    const reminderIds = body.reminder_ids || [];
    const dryRun = body.dry_run === true;
    const processAll = body.process_all === true;

    // Validate parameters
    if (!processAll && (!reminderIds || reminderIds.length === 0)) {
      return NextResponse.json({
        message: 'Missing required field: reminder_ids (array) or process_all (boolean)'
      }, { status: 400 });
    }

    // Try to process reminders using the database
    // Try to send batch reminders using the service
    try {
        const results = await paymentReminderService.sendBatchReminders(merchantId, reminderIds);
        console.log(`POST /api/payment-reminders/batch - Attempted to send ${reminderIds.length} reminders`);

        // Filter results to separate successes and failures
        const successfulSends = results.filter(r => r.success);
        const failedSends = results.filter(r => !r.success);

        if (failedSends.length === 0) {
          return NextResponse.json({
            message: `Successfully sent ${successfulSends.length} reminders.`,
            results: successfulSends
          }, { status: 200 });
        } else {
          // Return a mixed status if some failed
          return NextResponse.json({
            message: `Sent ${successfulSends.length} reminders successfully, but failed to send ${failedSends.length}.`,
            successful_sends: successfulSends,
            failed_sends: failedSends
          }, { status: 207 }); // Multi-Status
        }
      } catch (dbError) {
        console.error('Service error sending batch payment reminders:', dbError);
        // Removed fallback to mock data
        if (dbError instanceof Error) {
          if (dbError.message.includes('not found')) {
            return NextResponse.json({ message: 'One or more reminders, invoices, customers, or templates not found.' }, { status: 404 });
          }
          // Add other specific service errors if needed
        }
        return NextResponse.json({ message: 'Failed to send batch payment reminders due to a service error' }, { status: 500 });
      }
  } catch (error) {
      console.error('Failed to send batch payment reminders:', error);
      if (error instanceof z.ZodError) {
        return NextResponse.json({ errors: error.errors }, { status: 400 });
      }
      if (error instanceof Error) {
        if (error.message.includes('Merchant ID not found')) {
          return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
        }
        if (error.message.includes('Permission denied')) {
          return NextResponse.json({ message: 'Forbidden: Insufficient permissions' }, { status: 403 });
        }
      }
      // Handle JSON parsing errors or other unexpected errors
      // Note: SyntaxError for request.json() would ideally be caught before this main handler if it's a common case.
      if (error instanceof SyntaxError) {
        return NextResponse.json({ message: 'Invalid JSON body' }, { status: 400 });
      }
      return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}

// Process all scheduled reminders for a merchant
async function processAllReminders(merchantId: string, dryRun: boolean) {
  // Get all scheduled reminders
  const remindersResult = await paymentReminderService.findAllByMerchant(merchantId, {
    status: PaymentReminderStatus.Scheduled,
  });

  const reminders = remindersResult.reminders;

  if (reminders.length === 0) {
    return {
      message: 'No scheduled reminders found',
      processed: 0,
      sent: 0,
      skipped: 0,
      details: [],
    };
  }

  // Process each reminder
  const results = [];
  let totalProcessed = 0;
  let totalSent = 0;
  let totalSkipped = 0;

  for (const reminder of reminders) {
    try {
      // Get the invoice and customer
      const invoice = reminder.invoice;
      const customer = reminder.invoice.customer;

      // Skip if customer has no email
      if (!customer.email) {
        results.push({
          reminder_id: reminder.id,
          invoice_id: reminder.invoiceId,
          customer_id: customer.id,
          status: 'skipped',
          reason: 'Customer does not have an email address',
        });
        totalSkipped++;
        continue;
      }

      // Skip if dry run
      if (dryRun) {
        results.push({
          reminder_id: reminder.id,
          invoice_id: reminder.invoiceId,
          customer_id: customer.id,
          status: 'would_send',
          reason: 'Dry run',
        });
        totalProcessed++;
        totalSent++;
        continue;
      }

      // Get the email template
      let emailTemplate;
      if (reminder.emailTemplateId) {
        emailTemplate = reminder.emailTemplate;
        if (!emailTemplate) {
          results.push({
            reminder_id: reminder.id,
            invoice_id: reminder.invoiceId,
            customer_id: customer.id,
            status: 'skipped',
            reason: 'Email template not found',
          });
          totalSkipped++;
          continue;
        }
      } else {
        // Use default template if none specified
        // In a real implementation, we would fetch the default template from the database
        // For now, we'll use the mock data
        emailTemplate = findDefaultMockEmailTemplate();
        if (!emailTemplate) {
          results.push({
            reminder_id: reminder.id,
            invoice_id: reminder.invoiceId,
            customer_id: customer.id,
            status: 'skipped',
            reason: 'No default email template found',
          });
          totalSkipped++;
          continue;
        }
      }

      // Prepare template variables
      const variables = {
        invoice_number: invoice.invoiceNumber,
        company_name: 'Your Company', // This would come from settings in a real app
        customer_name: customer.name,
        total_amount: new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(Number(invoice.totalAmount)),
        due_date: new Date(invoice.dueDate).toLocaleDateString(),
        invoice_link: `https://yourapp.com/invoices/${invoice.id}`, // This would be a real link in production
        amount_due: new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(Number(invoice.amountDue)),
        issue_date: new Date(invoice.issueDate).toLocaleDateString(),
      };

      // Replace variables in template
      const subject = emailTemplate.subject.replace(/\{([^}]+)\}/g, (_, key) => variables[key] || '');
      const body_content = emailTemplate.body.replace(/\{([^}]+)\}/g, (_, key) => variables[key] || '');

      // Send the email
      const emailLog = await emailLogService.sendEmail(merchantId, {
        recipientEmail: customer.email,
        subject,
        body: body_content,
        invoiceId: invoice.id,
        customerId: customer.id,
      });

      // Update reminder status
      await paymentReminderService.markAsSent(merchantId, reminder.id);

      results.push({
        reminder_id: reminder.id,
        invoice_id: reminder.invoiceId,
        customer_id: customer.id,
        status: 'sent',
        email_log_id: emailLog.id,
      });

      totalProcessed++;
      totalSent++;
    } catch (error) {
      results.push({
        reminder_id: reminder.id,
        invoice_id: reminder.invoiceId,
        customer_id: reminder.invoice.customerId,
        status: 'error',
        reason: (error as Error).message,
      });
      totalSkipped++;
    }
  }

  return {
    message: `Successfully processed ${totalProcessed} reminders, sent ${totalSent}, skipped ${totalSkipped}`,
    processed: totalProcessed,
    sent: totalSent,
    skipped: totalSkipped,
    details: results,
  };
}

// Process specific reminders for a merchant
async function processSpecificReminders(merchantId: string, reminderIds: string[], dryRun: boolean) {
  if (reminderIds.length === 0) {
    return {
      message: 'No reminder IDs provided',
      processed: 0,
      sent: 0,
      skipped: 0,
      details: [],
    };
  }

  // Process each reminder
  const results = [];
  let totalProcessed = 0;
  let totalSent = 0;
  let totalSkipped = 0;

  for (const reminderId of reminderIds) {
    try {
      // Get the reminder
      const reminder = await paymentReminderService.findById(merchantId, reminderId);

      if (!reminder) {
        results.push({
          reminder_id: reminderId,
          status: 'skipped',
          reason: 'Payment reminder not found',
        });
        totalSkipped++;
        continue;
      }

      // Check if reminder is in a state that allows sending
      if (reminder.status !== PaymentReminderStatus.Scheduled) {
        results.push({
          reminder_id: reminderId,
          invoice_id: reminder.invoiceId,
          customer_id: reminder.invoice.customerId,
          status: 'skipped',
          reason: 'Only scheduled reminders can be sent',
        });
        totalSkipped++;
        continue;
      }

      // Get the invoice and customer
      const invoice = reminder.invoice;
      const customer = reminder.invoice.customer;

      // Skip if customer has no email
      if (!customer.email) {
        results.push({
          reminder_id: reminderId,
          invoice_id: reminder.invoiceId,
          customer_id: customer.id,
          status: 'skipped',
          reason: 'Customer does not have an email address',
        });
        totalSkipped++;
        continue;
      }

      // Skip if dry run
      if (dryRun) {
        results.push({
          reminder_id: reminderId,
          invoice_id: reminder.invoiceId,
          customer_id: customer.id,
          status: 'would_send',
          reason: 'Dry run',
        });
        totalProcessed++;
        totalSent++;
        continue;
      }

      // Get the email template
      let emailTemplate;
      if (reminder.emailTemplateId) {
        emailTemplate = reminder.emailTemplate;
        if (!emailTemplate) {
          results.push({
            reminder_id: reminderId,
            invoice_id: reminder.invoiceId,
            customer_id: customer.id,
            status: 'skipped',
            reason: 'Email template not found',
          });
          totalSkipped++;
          continue;
        }
      } else {
        // Use default template if none specified
        // In a real implementation, we would fetch the default template from the database
        // For now, we'll use the mock data
        emailTemplate = findDefaultMockEmailTemplate();
        if (!emailTemplate) {
          results.push({
            reminder_id: reminderId,
            invoice_id: reminder.invoiceId,
            customer_id: customer.id,
            status: 'skipped',
            reason: 'No default email template found',
          });
          totalSkipped++;
          continue;
        }
      }

      // Prepare template variables
      const variables = {
        invoice_number: invoice.invoiceNumber,
        company_name: 'Your Company', // This would come from settings in a real app
        customer_name: customer.name,
        total_amount: new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(Number(invoice.totalAmount)),
        due_date: new Date(invoice.dueDate).toLocaleDateString(),
        invoice_link: `https://yourapp.com/invoices/${invoice.id}`, // This would be a real link in production
        amount_due: new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(Number(invoice.amountDue)),
        issue_date: new Date(invoice.issueDate).toLocaleDateString(),
      };

      // Replace variables in template
      const subject = emailTemplate.subject.replace(/\{([^}]+)\}/g, (_, key) => variables[key] || '');
      const body_content = emailTemplate.body.replace(/\{([^}]+)\}/g, (_, key) => variables[key] || '');

      // Send the email
      const emailLog = await emailLogService.sendEmail(merchantId, {
        recipientEmail: customer.email,
        subject,
        body: body_content,
        invoiceId: invoice.id,
        customerId: customer.id,
      });

      // Update reminder status
      await paymentReminderService.markAsSent(merchantId, reminderId);

      results.push({
        reminder_id: reminderId,
        invoice_id: reminder.invoiceId,
        customer_id: customer.id,
        status: 'sent',
        email_log_id: emailLog.id,
      });

      totalProcessed++;
      totalSent++;
    } catch (error) {
      results.push({
        reminder_id: reminderId,
        status: 'error',
        reason: (error as Error).message,
      });
      totalSkipped++;
    }
  }

  return {
    message: `Successfully processed ${totalProcessed} reminders, sent ${totalSent}, skipped ${totalSkipped}`,
    processed: totalProcessed,
    sent: totalSent,
    skipped: totalSkipped,
    details: results,
  };
}

// Process all scheduled reminders using mock data
async function processMockAllReminders(dryRun: boolean) {
  // Get all scheduled reminders
  const reminders = findUpcomingMockPaymentReminders();

  if (reminders.length === 0) {
    return {
      message: 'No scheduled reminders found',
      processed: 0,
      sent: 0,
      skipped: 0,
      details: [],
    };
  }

  // Process each reminder
  const results = [];
  let totalProcessed = 0;
  let totalSent = 0;
  let totalSkipped = 0;

  for (const reminder of reminders) {
    try {
      // Get the invoice
      const invoice = findMockInvoiceById(reminder.invoice_id);
      if (!invoice) {
        results.push({
          reminder_id: reminder.id,
          invoice_id: reminder.invoice_id,
          status: 'skipped',
          reason: 'Invoice not found',
        });
        totalSkipped++;
        continue;
      }

      // Get the customer
      const customer = findMockCustomerById(reminder.customer_id);
      if (!customer) {
        results.push({
          reminder_id: reminder.id,
          invoice_id: reminder.invoice_id,
          status: 'skipped',
          reason: 'Customer not found',
        });
        totalSkipped++;
        continue;
      }

      // Skip if customer has no email
      if (!customer.email) {
        results.push({
          reminder_id: reminder.id,
          invoice_id: reminder.invoice_id,
          customer_id: customer.id,
          status: 'skipped',
          reason: 'Customer does not have an email address',
        });
        totalSkipped++;
        continue;
      }

      // Skip if dry run
      if (dryRun) {
        results.push({
          reminder_id: reminder.id,
          invoice_id: reminder.invoice_id,
          customer_id: customer.id,
          status: 'would_send',
          reason: 'Dry run',
        });
        totalProcessed++;
        totalSent++;
        continue;
      }

      // Get the email template
      let emailTemplate;
      if (reminder.email_template_id) {
        emailTemplate = findMockEmailTemplateById(reminder.email_template_id);
        if (!emailTemplate) {
          results.push({
            reminder_id: reminder.id,
            invoice_id: reminder.invoice_id,
            customer_id: customer.id,
            status: 'skipped',
            reason: 'Email template not found',
          });
          totalSkipped++;
          continue;
        }
      } else {
        // Use default template if none specified
        emailTemplate = findDefaultMockEmailTemplate();
        if (!emailTemplate) {
          results.push({
            reminder_id: reminder.id,
            invoice_id: reminder.invoice_id,
            customer_id: customer.id,
            status: 'skipped',
            reason: 'No default email template found',
          });
          totalSkipped++;
          continue;
        }
      }

      // Prepare template variables
      const variables = {
        invoice_number: invoice.invoice_number,
        company_name: 'Your Company', // This would come from settings in a real app
        customer_name: customer.name,
        total_amount: new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(invoice.total_amount),
        due_date: new Date(invoice.due_date).toLocaleDateString(),
        invoice_link: `https://yourapp.com/invoices/${invoice.id}`, // This would be a real link in production
        amount_due: new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(invoice.amount_due),
        issue_date: new Date(invoice.issue_date).toLocaleDateString(),
      };

      // Replace variables in template
      const subject = emailTemplate.subject.replace(/\{([^}]+)\}/g, (_, key) => variables[key] || '');
      const body_content = emailTemplate.body.replace(/\{([^}]+)\}/g, (_, key) => variables[key] || '');

      // Send the email
      const emailLog = sendMockEmail(
        invoice.id,
        customer.id,
        customer.email,
        subject,
        body_content
      );

      // Update reminder status
      updateMockPaymentReminder(reminder.id, {
        status: PaymentReminderStatus.Sent,
        updated_at: new Date().toISOString(),
      });

      results.push({
        reminder_id: reminder.id,
        invoice_id: reminder.invoice_id,
        customer_id: customer.id,
        status: 'sent',
        email_log_id: emailLog.id,
      });

      totalProcessed++;
      totalSent++;
    } catch (error) {
      results.push({
        reminder_id: reminder.id,
        status: 'error',
        reason: (error as Error).message,
      });
      totalSkipped++;
    }
  }

  return {
    message: `Successfully processed ${totalProcessed} reminders, sent ${totalSent}, skipped ${totalSkipped}`,
    processed: totalProcessed,
    sent: totalSent,
    skipped: totalSkipped,
    details: results,
  };
}

// Process specific reminders using mock data
async function processMockSpecificReminders(reminderIds: string[], dryRun: boolean) {
  if (reminderIds.length === 0) {
    return {
      message: 'No reminder IDs provided',
      processed: 0,
      sent: 0,
      skipped: 0,
      details: [],
    };
  }

  // Process each reminder
  const results = [];
  let totalProcessed = 0;
  let totalSent = 0;
  let totalSkipped = 0;

  for (const reminderId of reminderIds) {
    try {
      // Get the reminder
      const reminder = findMockPaymentReminderById(reminderId);

      if (!reminder) {
        results.push({
          reminder_id: reminderId,
          status: 'skipped',
          reason: 'Payment reminder not found',
        });
        totalSkipped++;
        continue;
      }

      // Check if reminder is in a state that allows sending
      if (reminder.status !== PaymentReminderStatus.Scheduled) {
        results.push({
          reminder_id: reminderId,
          invoice_id: reminder.invoice_id,
          customer_id: reminder.customer_id,
          status: 'skipped',
          reason: 'Only scheduled reminders can be sent',
        });
        totalSkipped++;
        continue;
      }

      // Get the invoice
      const invoice = findMockInvoiceById(reminder.invoice_id);
      if (!invoice) {
        results.push({
          reminder_id: reminderId,
          invoice_id: reminder.invoice_id,
          status: 'skipped',
          reason: 'Invoice not found',
        });
        totalSkipped++;
        continue;
      }

      // Get the customer
      const customer = findMockCustomerById(reminder.customer_id);
      if (!customer) {
        results.push({
          reminder_id: reminderId,
          invoice_id: reminder.invoice_id,
          status: 'skipped',
          reason: 'Customer not found',
        });
        totalSkipped++;
        continue;
      }

      // Skip if customer has no email
      if (!customer.email) {
        results.push({
          reminder_id: reminderId,
          invoice_id: reminder.invoice_id,
          customer_id: customer.id,
          status: 'skipped',
          reason: 'Customer does not have an email address',
        });
        totalSkipped++;
        continue;
      }

      // Skip if dry run
      if (dryRun) {
        results.push({
          reminder_id: reminderId,
          invoice_id: reminder.invoice_id,
          customer_id: customer.id,
          status: 'would_send',
          reason: 'Dry run',
        });
        totalProcessed++;
        totalSent++;
        continue;
      }

      // Get the email template
      let emailTemplate;
      if (reminder.email_template_id) {
        emailTemplate = findMockEmailTemplateById(reminder.email_template_id);
        if (!emailTemplate) {
          results.push({
            reminder_id: reminderId,
            invoice_id: reminder.invoice_id,
            customer_id: customer.id,
            status: 'skipped',
            reason: 'Email template not found',
          });
          totalSkipped++;
          continue;
        }
      } else {
        // Use default template if none specified
        emailTemplate = findDefaultMockEmailTemplate();
        if (!emailTemplate) {
          results.push({
            reminder_id: reminderId,
            invoice_id: reminder.invoice_id,
            customer_id: customer.id,
            status: 'skipped',
            reason: 'No default email template found',
          });
          totalSkipped++;
          continue;
        }
      }

      // Prepare template variables
      const variables = {
        invoice_number: invoice.invoice_number,
        company_name: 'Your Company', // This would come from settings in a real app
        customer_name: customer.name,
        total_amount: new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(invoice.total_amount),
        due_date: new Date(invoice.due_date).toLocaleDateString(),
        invoice_link: `https://yourapp.com/invoices/${invoice.id}`, // This would be a real link in production
        amount_due: new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(invoice.amount_due),
        issue_date: new Date(invoice.issue_date).toLocaleDateString(),
      };

      // Replace variables in template
      const subject = emailTemplate.subject.replace(/\{([^}]+)\}/g, (_, key) => variables[key] || '');
      const body_content = emailTemplate.body.replace(/\{([^}]+)\}/g, (_, key) => variables[key] || '');

      // Send the email
      const emailLog = sendMockEmail(
        invoice.id,
        customer.id,
        customer.email,
        subject,
        body_content
      );

      // Update reminder status
      updateMockPaymentReminder(reminderId, {
        status: PaymentReminderStatus.Sent,
        updated_at: new Date().toISOString(),
      });

      results.push({
        reminder_id: reminderId,
        invoice_id: reminder.invoice_id,
        customer_id: customer.id,
        status: 'sent',
        email_log_id: emailLog.id,
      });

      totalProcessed++;
      totalSent++;
    } catch (error) {
      results.push({
        reminder_id: reminderId,
        status: 'error',
        reason: (error as Error).message,
      });
      totalSkipped++;
    }
  }

  return {
    message: `Successfully processed ${totalProcessed} reminders, sent ${totalSent}, skipped ${totalSkipped}`,
    processed: totalProcessed,
    sent: totalSent,
    skipped: totalSkipped,
    details: results,
  };
}

