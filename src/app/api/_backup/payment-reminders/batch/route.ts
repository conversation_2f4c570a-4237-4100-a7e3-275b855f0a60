import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/lib/auth/session';
import { proxyGet, proxyPost } from '@/lib/utils/proxy';
import { z } from 'zod';

// Zod schema for batch processing payment reminders
const batchProcessRemindersSchema = z.object({
  reminderIds: z.array(z.string()).optional(),
  processAll: z.boolean().optional().default(false),
  dryRun: z.boolean().optional().default(false),
});

// GET /api/payment-reminders/batch - Get batch payment reminders
export async function GET(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    return await proxyGet('/payment-reminders/batch', request);
  } catch (error) {
    console.error('GET /api/payment-reminders/batch error:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}

// POST /api/payment-reminders/batch - Process multiple payment reminders
export async function POST(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    // Parse and validate the request body
    const body = await request.json();
    const validationResult = batchProcessRemindersSchema.safeParse({
      reminderIds: body.reminder_ids,
      processAll: body.process_all,
      dryRun: body.dry_run,
    });
    
    if (!validationResult.success) {
      return NextResponse.json(
        { errors: validationResult.error.errors },
        { status: 400 }
      );
    }

    const validatedData = validationResult.data;
    
    // Validate that either reminderIds or processAll is provided
    if (!validatedData.processAll && (!validatedData.reminderIds || validatedData.reminderIds.length === 0)) {
      return NextResponse.json(
        { message: 'Either reminder_ids or process_all must be provided' },
        { status: 400 }
      );
    }
    
    // Transform the validated data to match the Rust backend's expected format
    const rustRequestBody = {
      reminder_ids: validatedData.reminderIds,
      process_all: validatedData.processAll,
      dry_run: validatedData.dryRun,
    };

    // Proxy to Rust backend
    return await proxyPost('/payment-reminders/batch', request, rustRequestBody);
  } catch (error) {
    console.error('POST /api/payment-reminders/batch error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { errors: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { message: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
