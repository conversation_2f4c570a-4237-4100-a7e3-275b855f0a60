import { NextRequest, NextResponse } from 'next/server';
import { paymentReminderService } from '@/lib/services/paymentReminderService';
import { getMerchantId } from '@/lib/utils/auth';
import { requirePermission } from '@/lib/utils/permissions';
import { PermissionLevel } from '@prisma/client';
import { mockPaymentReminders } from '@/lib/accountsReceivableMockData'; // Keep for fallback

// DELETE /api/payment-reminders/batch/delete - Delete multiple payment reminders at once
export async function DELETE(request: NextRequest) {
  try {
    // Get merchant ID from session
    const merchantId = await getMerchantId(request);
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized - Merchant ID not found' }, { status: 401 });
    }

    // Check if the user has permission to delete payment reminders (Manager level or higher)
    const permissionCheck = await requirePermission(request, merchantId, PermissionLevel.Manager);
    if (permissionCheck) {
      console.warn('DELETE /api/payment-reminders/batch/delete - Permission denied: User does not have Manager level access');
      return permissionCheck;
    }

    const body = await request.json();
    const { ids } = body;

    // Validate request
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json({ message: 'Invalid request: ids array is required' }, { status: 400 });
    }

    // Try to delete from the database
    try {
      const result = await paymentReminderService.deleteMany(merchantId, ids);
      const deletedCount = result.count;

      return NextResponse.json({ 
        message: `Successfully deleted ${deletedCount} payment reminders`,
        deleted_count: deletedCount
      });
    } catch (dbError) {
      console.error('Database error, falling back to mock data:', dbError);

      // Fall back to mock data if database fails
      // Track original length to calculate how many were deleted
      const originalLength = mockPaymentReminders.length;

      // Filter out the payment reminders to be deleted
      const updatedPaymentReminders = mockPaymentReminders.filter(reminder => !ids.includes(reminder.id));
      
      // Calculate how many were deleted
      const deletedCount = originalLength - updatedPaymentReminders.length;

      // Update the mock database
      mockPaymentReminders.length = 0;
      mockPaymentReminders.push(...updatedPaymentReminders);

      return NextResponse.json({ 
        message: `Successfully deleted ${deletedCount} payment reminders (mock)`, // Indicate mock data was used
        deleted_count: deletedCount
      });
    }


  } catch (error) {
    console.error('Failed to batch delete payment reminders:', error);
    return NextResponse.json({ message: 'Failed to batch delete payment reminders' }, { status: 500 });
  }
}
