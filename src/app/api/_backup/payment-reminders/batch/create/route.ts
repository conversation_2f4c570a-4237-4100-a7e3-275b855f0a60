import { NextRequest, NextResponse } from 'next/server';
import { paymentReminderService } from '@/lib/services/paymentReminderService';
import { getMerchantId } from '@/lib/utils/auth';
import { requirePermission } from '@/lib/utils/permissions';
import { PermissionLevel } from '@prisma/client';
import { mockInvoices } from '@/lib/invoicesMockData';
import { addMockPaymentReminder, generateMockPaymentReminderId } from '@/lib/accountsReceivableMockData';
import { PaymentReminderStatus } from '@/lib/types';
import prisma from '@/lib/prisma';

// POST /api/payment-reminders/batch/create - Create payment reminders for multiple invoices at once
export async function POST(request: NextRequest) {
  try {
    // Get merchant ID from session
    const merchantId = await getMerchantId(request);
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized - Merchant ID not found' }, { status: 401 });
    }

    // Check if the user has permission to create payment reminders (Staff level or higher)
    const permissionCheck = await requirePermission(request, merchantId, PermissionLevel.Staff);
    if (permissionCheck) {
      console.warn('POST /api/payment-reminders/batch/create - Permission denied: User does not have Staff level access');
      return permissionCheck;
    }

    const body = await request.json();
    const { invoice_ids, reminder_date, email_template_id, notes } = body;

    // Validate request
    if (!invoice_ids || !Array.isArray(invoice_ids) || invoice_ids.length === 0) {
      return NextResponse.json({ message: 'Invalid request: invoice_ids array is required' }, { status: 400 });
    }

    if (!reminder_date) {
      return NextResponse.json({ message: 'reminder_date is required' }, { status: 400 });
    }

    // Validate reminder_date
    let reminderDate: Date;
    try {
      reminderDate = new Date(reminder_date);
    } catch (error) {
      return NextResponse.json({ message: 'Invalid reminder_date format' }, { status: 400 });
    }

    // Try to use the database
    try {
      // Track how many reminders were created and their IDs
      let createdCount = 0;
      const reminderIds: string[] = [];

      // Process each invoice
      for (const invoiceId of invoice_ids) {
        try {
          // Check if invoice exists and belongs to the merchant
          const invoice = await prisma.invoice.findFirst({
            where: {
              id: invoiceId,
              merchantId,
            },
          });

          if (!invoice) {
            console.warn(`Invoice ${invoiceId} not found or does not belong to merchant ${merchantId}`);
            continue; // Skip if invoice not found
          }

          // Create a payment reminder
          const reminder = await paymentReminderService.create(merchantId, {
            invoiceId: invoice.id,
            emailTemplateId: email_template_id || undefined,
            scheduledDate: reminderDate,
            message: notes || '',
            status: PaymentReminderStatus.Scheduled,
          });

          // Track successful creations
          createdCount++;
          reminderIds.push(reminder.id);
        } catch (error) {
          console.error(`Error creating payment reminder for invoice ${invoiceId}:`, error);
          // Continue with the next invoice
        }
      }

      return NextResponse.json({
        message: `Successfully created ${createdCount} payment reminders`,
        created_count: createdCount,
        reminder_ids: reminderIds
      });
    } catch (dbError) {
      console.error('Database error, falling back to mock data:', dbError);

      // Fall back to mock data if database fails
      // Track how many reminders were created and their IDs
      let createdCount = 0;
      const reminderIds: string[] = [];

      // Process each invoice
      for (const invoiceId of invoice_ids) {
        // Find the invoice
        const invoice = mockInvoices.find(inv => inv.id === invoiceId);

        if (!invoice) {
          continue; // Skip if invoice not found
        }

        // Create a payment reminder
        const reminderId = generateMockPaymentReminderId();

        const paymentReminder = {
          id: reminderId,
          invoice_id: invoice.id,
          customer_id: invoice.customer_id,
          reminder_date: reminder_date,
          status: PaymentReminderStatus.Scheduled,
          email_template_id: email_template_id || null,
          notes: notes || '',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        };

        // Add to mock database
        addMockPaymentReminder(paymentReminder);

        // Track successful creations
        createdCount++;
        reminderIds.push(reminderId);
      }

      return NextResponse.json({
        message: `Successfully created ${createdCount} payment reminders`,
        created_count: createdCount,
        reminder_ids: reminderIds
      });
    }
  } catch (error) {
    console.error('Failed to batch create payment reminders:', error);
    return NextResponse.json({ message: 'Failed to batch create payment reminders' }, { status: 500 });
  }
}
