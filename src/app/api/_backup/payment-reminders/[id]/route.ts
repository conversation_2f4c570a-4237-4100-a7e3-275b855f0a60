import { NextRequest, NextResponse } from 'next/server';
import { PaymentReminderStatus } from '@/lib/types';
import { paymentReminderService } from '@/lib/services/paymentReminderService';
import { getMerchantId } from '@/lib/utils/auth';
import { requirePermission } from '@/lib/utils/permissions';
import { PermissionLevel } from '@prisma/client';
import { z } from 'zod'; // Added Zod import

// Removed mock data imports and helper function with fallback
// import { findPaymentReminderById } from '../route';
// import {
//   updateMockPaymentReminder,
//   deleteMockPaymentReminder
// } from '@/lib/accountsReceivableMockData';
// import { findMockEmailTemplateById } from '@/lib/emailMockData';

interface Params {
  params: { id: string };
}

// Zod schema for PUT request body
const updatePaymentReminderSchema = z.object({
  reminder_date: z.string().datetime({ message: "Invalid reminder date format" }).optional(),
  status: z.nativeEnum(PaymentReminderStatus).optional(),
  notes: z.string().optional(),
  email_template_id: z.string().nullable().optional(), // Allow null or string
}).refine(data => Object.keys(data).length > 0, {
  message: "At least one field must be provided for update",
});

// GET /api/payment-reminders/[id] - Get a specific payment reminder
export async function GET(request: NextRequest, { params }: Params) {
  const { id } = params;

  try {
    // Get merchant ID from session
    const merchantId = await getMerchantId();
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized - Merchant ID not found' }, { status: 401 });
    }

    // Check if the user has permission to view payment reminders (ReadOnly level or higher)
    const permissionCheck = await requirePermission(request, merchantId, PermissionLevel.ReadOnly);
    if (permissionCheck) {
      console.warn(`GET /api/payment-reminders/${id} - Permission denied: User does not have ReadOnly level access`);
      return permissionCheck;
    }

    // Try to fetch from the database
    try {
       const reminder = await paymentReminderService.findById(merchantId, id);

       if (!reminder) {
        return NextResponse.json({ message: 'Payment reminder not found' }, { status: 404 });
      }

      // Format the response to match the expected format in the frontend
      const formattedReminder = {
        id: reminder.id,
        invoice_id: reminder.invoiceId,
        customer_id: reminder.invoice.customerId,
        reminder_date: reminder.scheduledDate.toISOString(),
        status: reminder.status,
        email_template_id: reminder.emailTemplateId || undefined,
        notes: reminder.message || undefined,
        sent_at: reminder.sentAt ? reminder.sentAt.toISOString() : undefined,
        created_at: reminder.createdAt.toISOString(),
        updated_at: reminder.updatedAt.toISOString(),
      };

      return NextResponse.json(formattedReminder, { status: 200 });
    } catch (dbError) {
      console.error(`Database error fetching payment reminder ${id}:`, dbError);
      // Propagate the error without falling back to mock data
      // Handle specific Prisma errors if needed, e.g., P2025 for not found is handled above
      return NextResponse.json({ message: 'Failed to fetch payment reminder due to database error' }, { status: 500 });
     }
   } catch (error) {
     console.error(`Error fetching payment reminder ${id}:`, error);
     if (error instanceof Error) {
        if (error.message.includes('Unauthorized')) {
            return NextResponse.json({ message: error.message }, { status: 401 });
        }
        if (error.message.includes('Forbidden')) {
            return NextResponse.json({ message: error.message }, { status: 403 });
        }
     }
     return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
   }
 }

// PUT /api/payment-reminders/[id] - Update a specific payment reminder
export async function PUT(request: NextRequest, { params }: Params) {
  const { id } = params;

  try {
    // Get merchant ID from session
    const merchantId = await getMerchantId();
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized - Merchant ID not found' }, { status: 401 });
    }

    // Check if the user has permission to update payment reminders (Staff level or higher)
    const permissionCheck = await requirePermission(request, merchantId, PermissionLevel.Staff);
    if (permissionCheck) {
      console.warn(`PUT /api/payment-reminders/${id} - Permission denied: User does not have Staff level access`);
      return permissionCheck;
    }

    const body = await request.json();

    // Validate request body using Zod
    const validationResult = updatePaymentReminderSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json({ errors: validationResult.error.errors }, { status: 400 });
    }
    const validatedData = validationResult.data;
     // Try to update in the database
     try {
       // Get the current payment reminder
      const currentReminder = await paymentReminderService.findById(merchantId, id);

      if (!currentReminder) {
        return NextResponse.json({ message: 'Payment reminder not found' }, { status: 404 });
      }

      // Prevent updating sent reminders
      if (currentReminder.status === PaymentReminderStatus.Sent) {
        return NextResponse.json({
          message: 'Cannot update a reminder that has already been sent'
        }, { status: 400 });
      }

      // Prepare updates object
      const updates: Record<string, any> = {};

      // Only include fields that are provided in the request body
      if (body.reminder_date !== undefined) updates.scheduledDate = new Date(body.reminder_date);
      if (body.status !== undefined) updates.status = body.status;
      if (body.notes !== undefined) updates.message = body.notes;
      if (body.email_template_id !== undefined) updates.emailTemplateId = body.email_template_id || null;

      if (Object.keys(updates).length === 0) {
        console.warn(`PUT /api/payment-reminders/${id} - No valid update fields provided.`);

        // Format the response to match the expected format in the frontend
        const formattedReminder = {
          id: currentReminder.id,
          invoice_id: currentReminder.invoiceId,
          customer_id: currentReminder.invoice.customerId,
          reminder_date: currentReminder.scheduledDate.toISOString(),
          status: currentReminder.status,
          email_template_id: currentReminder.emailTemplateId || undefined,
          notes: currentReminder.message || undefined,
          sent_at: currentReminder.sentAt ? currentReminder.sentAt.toISOString() : undefined,
          created_at: currentReminder.createdAt.toISOString(),
          updated_at: currentReminder.updatedAt.toISOString(),
        };

        return NextResponse.json(formattedReminder); // Return current data if no updates
      }

      // Map validated data to Prisma update structure
      if (validatedData.reminder_date !== undefined) updates.scheduledDate = new Date(validatedData.reminder_date);
      if (validatedData.status !== undefined) updates.status = validatedData.status;
      if (validatedData.notes !== undefined) updates.message = validatedData.notes;
      // Handle nullable email_template_id
      if ('email_template_id' in validatedData) {
          updates.emailTemplateId = validatedData.email_template_id;
      }

       // Update the payment reminder
       const updatedReminder = await paymentReminderService.update(merchantId, id, updates);

      // Format the response to match the expected format in the frontend
      const formattedReminder = {
        id: updatedReminder.id,
        invoice_id: updatedReminder.invoiceId,
        customer_id: updatedReminder.invoice.customerId,
        reminder_date: updatedReminder.scheduledDate.toISOString(),
        status: updatedReminder.status,
        email_template_id: updatedReminder.emailTemplateId || undefined,
        notes: updatedReminder.message || undefined,
        sent_at: updatedReminder.sentAt ? updatedReminder.sentAt.toISOString() : undefined,
        created_at: updatedReminder.createdAt.toISOString(),
        updated_at: updatedReminder.updatedAt.toISOString(),
      };

      return NextResponse.json(formattedReminder, { status: 200 });
    } catch (error) {
      console.error(`Database error updating payment reminder ${id}:`, error);
      // Handle specific Prisma errors
      const dbError = error as any;
      if (dbError.code === 'P2025') { // Record to update not found
        return NextResponse.json({ message: 'Payment reminder not found' }, { status: 404 });
      }
      if (dbError.code === 'P2003') { // Foreign key constraint failed
          if (dbError.meta?.field_name?.includes('emailTemplateId')) {
              return NextResponse.json({ message: 'Email template not found' }, { status: 404 });
          }
      }
      // Propagate other errors
      return NextResponse.json({ message: 'Failed to update payment reminder due to database error' }, { status: 500 });
     }
   } catch (error) {
     console.error(`Error updating payment reminder ${id}:`, error);
    if (error instanceof z.ZodError) {
        return NextResponse.json({ errors: error.errors }, { status: 400 });
    }
    if (error instanceof Error) {
        if (error.message.includes('Unauthorized')) {
            return NextResponse.json({ message: error.message }, { status: 401 });
        }
        if (error.message.includes('Forbidden')) {
            return NextResponse.json({ message: error.message }, { status: 403 });
        }
        if (error.message.includes('Cannot update a reminder that has already been sent')) {
            return NextResponse.json({ message: error.message }, { status: 400 });
        }
    }
     return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
   }
 }

// DELETE /api/payment-reminders/[id] - Delete a specific payment reminder
export async function DELETE(request: NextRequest, { params }: Params) {
  const { id } = params;

  try {
    // Get merchant ID from session
    const merchantId = await getMerchantId();
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized - Merchant ID not found' }, { status: 401 });
    }

    // Check if the user has permission to delete payment reminders (Manager level or higher)
    const permissionCheck = await requirePermission(request, merchantId, PermissionLevel.Manager);
    if (permissionCheck) {
      console.warn(`DELETE /api/payment-reminders/${id} - Permission denied: User does not have Manager level access`);
      return permissionCheck;
    }

    // Try to delete from the database
    try {
      // Check if reminder exists and belongs to the merchant before deleting
      const reminder = await paymentReminderService.findById(merchantId, id);
      if (!reminder) {
        return NextResponse.json({ message: 'Payment reminder not found' }, { status: 404 });
      }
       await paymentReminderService.delete(merchantId, id);

       return NextResponse.json({ message: 'Payment reminder deleted successfully' }, { status: 200 });
     } catch (error) {
      console.error(`Database error deleting payment reminder ${id}:`, error);
      // Handle specific Prisma errors
      const dbError = error as any;
      if (dbError.code === 'P2025') { // Record to delete not found
        return NextResponse.json({ message: 'Payment reminder not found' }, { status: 404 });
      }
      // Propagate other errors
      return NextResponse.json({ message: 'Failed to delete payment reminder due to database error' }, { status: 500 });
     }
   } catch (error) {
     console.error(`Error deleting payment reminder ${id}:`, error);
    if (error instanceof Error) {
        if (error.message.includes('Unauthorized')) {
            return NextResponse.json({ message: error.message }, { status: 401 });
        }
        if (error.message.includes('Forbidden')) {
            return NextResponse.json({ message: error.message }, { status: 403 });
        }
    }
     return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
   }
 }
