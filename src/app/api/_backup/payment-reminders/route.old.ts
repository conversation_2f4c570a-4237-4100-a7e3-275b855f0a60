import { NextRequest, NextResponse } from 'next/server';
import { PaymentReminderStatus } from '@/lib/types';
import { paymentReminderService } from '@/lib/services/paymentReminderService';
import { getMerchantId } from '@/lib/utils/auth';
import { requirePermission } from '@/lib/utils/permissions';
import { PermissionLevel } from '@prisma/client';
import { z } from 'zod'; // Added Zod import

// Removed mock data imports
// import {
//   mockPaymentReminders,
//   addMockPaymentReminder,
//   findMockPaymentReminderById,
//   findMockPaymentRemindersByInvoiceId,
//   findMockPaymentRemindersByCustomerId,
//   findUpcomingMockPaymentReminders
// } from '@/lib/accountsReceivableMockData';
// import { findMockInvoiceById } from '@/lib/invoicesMockData';
// import { findMockEmailTemplateById } from '@/lib/emailMockData';

// Zod schema for GET query parameters
const getPaymentRemindersSchema = z.object({
  invoice_id: z.string().optional(),
  customer_id: z.string().optional(),
  status: z.nativeEnum(PaymentReminderStatus).optional(),
  upcoming: z.preprocess((val) => val === 'true', z.boolean()).optional(),
});

// Zod schema for POST request body
const createPaymentReminderSchema = z.object({
  invoice_id: z.string(),
  reminder_date: z.string().datetime({ message: "Invalid reminder date format" }),
  email_template_id: z.string().optional(),
  notes: z.string().optional(),
});

// GET /api/payment-reminders - List all payment reminders
export async function GET(request: NextRequest) {
  try {
    // Get merchant ID from session
    const merchantId = await getMerchantId();
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized - Merchant ID not found' }, { status: 401 });
    }

    // Check if the user has permission to view payment reminders (ReadOnly level or higher)
    const permissionCheck = await requirePermission(request, merchantId, PermissionLevel.ReadOnly);
    if (permissionCheck) {
      console.warn('GET /api/payment-reminders - Permission denied: User does not have ReadOnly level access');
      return permissionCheck;
    }

    // Get URL parameters for filtering
    const { searchParams } = new URL(request.url);
    const invoiceId = searchParams.get('invoice_id');
    const customerId = searchParams.get('customer_id');
    const status = searchParams.get('status');
    const upcoming = searchParams.get('upcoming');

    // Validate query parameters using Zod
    const queryParams = {
      invoice_id: invoiceId,
      customer_id: customerId,
      status: status,
      upcoming: upcoming,
    };
    const validationResult = getPaymentRemindersSchema.safeParse(queryParams);

    if (!validationResult.success) {
      return NextResponse.json({ errors: validationResult.error.errors }, { status: 400 });
    }
    const validatedParams = validationResult.data;
    // Try to fetch from the database
    try {
       const reminders = await paymentReminderService.findAllByMerchant(merchantId, {
        invoiceId: validatedParams.invoice_id,
        customerId: validatedParams.customer_id,
        status: validatedParams.status,
        upcoming: validatedParams.upcoming,
       });

       // Format the response to match the expected format in the frontend
      const formattedReminders = reminders.map(reminder => ({
        id: reminder.id,
        invoice_id: reminder.invoiceId,
        customer_id: reminder.invoice.customerId,
        reminder_date: reminder.scheduledDate.toISOString(),
        status: reminder.status,
        email_template_id: reminder.emailTemplateId || undefined,
        notes: reminder.message || undefined,
        sent_at: reminder.sentAt ? reminder.sentAt.toISOString() : undefined,
        created_at: reminder.createdAt.toISOString(),
        updated_at: reminder.updatedAt.toISOString(),
      }));

      return NextResponse.json(formattedReminders);
    } catch (dbError) {
      console.error('Database error fetching payment reminders:', dbError);
      // Propagate the error without falling back to mock data
      return NextResponse.json({ message: 'Failed to fetch payment reminders due to database error' }, { status: 500 });
     }
   } catch (error) {
     console.error('Failed to fetch payment reminders:', error);
    if (error instanceof z.ZodError) {
        return NextResponse.json({ errors: error.errors }, { status: 400 });
    }
    if (error instanceof Error) {
        if (error.message.includes('Unauthorized')) {
            return NextResponse.json({ message: error.message }, { status: 401 });
        }
        if (error.message.includes('Forbidden')) {
            return NextResponse.json({ message: error.message }, { status: 403 });
        }
    }
     return NextResponse.json({ message: 'Failed to fetch payment reminders' }, { status: 500 });
   }
 }

// POST /api/payment-reminders - Create a new payment reminder
export async function POST(request: NextRequest) {
  try {
    // Get merchant ID from session
    const merchantId = await getMerchantId();
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized - Merchant ID not found' }, { status: 401 });
    }

    // Check if the user has permission to create payment reminders (Staff level or higher)
    const permissionCheck = await requirePermission(request, merchantId, PermissionLevel.Staff);
    if (permissionCheck) {
      console.warn('POST /api/payment-reminders - Permission denied: User does not have Staff level access');
      return permissionCheck;
    }

    const body = await request.json();

   // Validate request body using Zod
   const validationResult = createPaymentReminderSchema.safeParse(body);

   if (!validationResult.success) {
     return NextResponse.json({ errors: validationResult.error.errors }, { status: 400 });
   }
   const validatedData = validationResult.data;

     // Try to create in the database
     try {
       const reminder = await paymentReminderService.create(merchantId, {
        invoiceId: validatedData.invoice_id,
        emailTemplateId: validatedData.email_template_id,
        scheduledDate: new Date(validatedData.reminder_date),
        message: validatedData.notes || '',
        status: PaymentReminderStatus.Scheduled,
       });

      // Format the response to match the expected format in the frontend
      const formattedReminder = {
        id: reminder.id,
        invoice_id: reminder.invoiceId,
        customer_id: reminder.invoice.customerId,
        reminder_date: reminder.scheduledDate.toISOString(),
        status: reminder.status,
        email_template_id: reminder.emailTemplateId || undefined,
        notes: reminder.message || undefined,
        sent_at: reminder.sentAt ? reminder.sentAt.toISOString() : undefined,
        created_at: reminder.createdAt.toISOString(),
        updated_at: reminder.updatedAt.toISOString(),
      };

      return NextResponse.json(formattedReminder, { status: 201 });
    } catch (error) {
      console.error('Database error creating payment reminder:', error);
      // Handle specific database errors (e.g., foreign key constraint)
      const dbError = error as any;
      if (dbError.code === 'P2003') { // Foreign key constraint failed
          if (dbError.meta?.field_name?.includes('invoiceId')) {
              return NextResponse.json({ message: 'Invoice not found' }, { status: 404 });
          }
          if (dbError.meta?.field_name?.includes('emailTemplateId')) {
              return NextResponse.json({ message: 'Email template not found' }, { status: 404 });
          }
      }
      // Propagate the error without falling back to mock data
      return NextResponse.json({ message: 'Failed to create payment reminder due to database error' }, { status: 500 });
     }
   } catch (error) {
     console.error('Failed to create payment reminder:', error);
    if (error instanceof z.ZodError) {
        return NextResponse.json({ errors: error.errors }, { status: 400 });
    }
    if (error instanceof Error) {
        if (error.message.includes('Unauthorized')) {
            return NextResponse.json({ message: error.message }, { status: 401 });
        }
        if (error.message.includes('Forbidden')) {
            return NextResponse.json({ message: error.message }, { status: 403 });
        }
    }
     return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
   }
 }

 // --- Helper functions for [id] route ---


