import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/lib/auth/session';
import { proxyGet, proxyPost } from '@/lib/utils/proxy';
import { requireBranchPermission, requireOrganizationPermission } from '@/lib/utils/branchPermissions';
import { PermissionLevel } from '@prisma/client';

// GET /api/vendors - Proxy to Rust backend with branch permission checking
export async function GET(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    console.log(`GET /api/vendors - User: ${session.user.email}, checking branch/organization permissions`);

    // Get branch or organization context from query parameters
    const { searchParams } = new URL(request.url);
    const branchId = searchParams.get('branchId');
    const organizationId = searchParams.get('organizationId');

    // Check permissions based on context
    if (branchId) {
      // Check branch-level permission (Staff level required to view vendors)
      const permissionCheck = await requireBranchPermission(
        request,
        branchId,
        PermissionLevel.Staff
      );
      if (permissionCheck) return permissionCheck;

      console.log(`GET /api/vendors - User has Staff permission for branch ${branchId}`);
    } else if (organizationId) {
      // Check organization-level permission (Staff level required to view vendors)
      const permissionCheck = await requireOrganizationPermission(
        request,
        organizationId,
        PermissionLevel.Staff
      );
      if (permissionCheck) return permissionCheck;

      console.log(`GET /api/vendors - User has Staff permission for organization ${organizationId}`);
    } else {
      // For backward compatibility, allow access without context but log a warning
      console.warn(`GET /api/vendors - No branch or organization context provided for user ${session.user.email}`);
    }

    // Proxy to Rust backend
    return await proxyGet('/vendors', request);
  } catch (error) {
    console.error('GET /api/vendors error:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}

// POST /api/vendors - Proxy to Rust backend with branch permission checking
export async function POST(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    console.log(`POST /api/vendors - User: ${session.user.email}, checking branch/organization permissions`);

    // Parse the request body to get context
    const body = await request.json();
    const branchId = body.branchId;
    const organizationId = body.organizationId;

    // Check permissions based on context
    if (branchId) {
      // Check branch-level permission (Manager level required to create vendors)
      const permissionCheck = await requireBranchPermission(
        request,
        branchId,
        PermissionLevel.Manager
      );
      if (permissionCheck) return permissionCheck;

      console.log(`POST /api/vendors - User has Manager permission for branch ${branchId}`);
    } else if (organizationId) {
      // Check organization-level permission (Manager level required to create vendors)
      const permissionCheck = await requireOrganizationPermission(
        request,
        organizationId,
        PermissionLevel.Manager
      );
      if (permissionCheck) return permissionCheck;

      console.log(`POST /api/vendors - User has Manager permission for organization ${organizationId}`);
    } else {
      // For backward compatibility, allow access without context but log a warning
      console.warn(`POST /api/vendors - No branch or organization context provided for user ${session.user.email}`);
    }

    // Proxy to Rust backend (the proxyPost function will handle the body)
    // We need to create a new request since we already consumed the body
    const clonedRequest = new NextRequest(request.url, {
      method: 'POST',
      headers: request.headers,
      body: JSON.stringify(body),
    });

    return await proxyPost('/vendors', clonedRequest);
  } catch (error) {
    console.error('POST /api/vendors error:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}
