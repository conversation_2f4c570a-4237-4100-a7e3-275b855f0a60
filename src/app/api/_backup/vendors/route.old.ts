import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { createVendorSchema } from '@/lib/validators/vendor';
import { vendorService } from '@/lib/services/vendorService';
import { getMerchantId } from '@/lib/utils/auth';
import { formatVendorResponse, formatVendorsResponse } from '@/lib/utils/apiFormatters';
import { requirePermission } from '@/lib/utils/permissions';
import { PermissionLevel } from '@prisma/client';
import { createCustomClient } from '@/utils/supabase/customClient';
import { checkAuth } from '@/utils/api-auth';

// Placeholder for role-based authorization
const checkUserRole = async (request: NextRequest, allowedRoles: string[]): Promise<boolean> => {
    // TODO: Replace with actual role checking logic based on session
    // Example: const session = await getServerSession(authOptions); return allowedRoles.includes(session?.user?.role);
    console.log(`Placeholder: Checking if user has one of roles: ${allowedRoles.join(', ')}`);
    return true; // Assume authorized for now
};

// GET /api/vendors - List vendors for the merchant with pagination and filtering
export async function GET(request: NextRequest) {
    try {
        // Check if the user is authenticated using our helper
        const { isAuthenticated, userId, errorResponse } = await checkAuth(request);

        if (!isAuthenticated) {
            console.error('GET /api/vendors - Authentication failed');
            return errorResponse;
        }

        console.log(`GET /api/vendors - Authenticated user: ${userId}`);

        // Get URL parameters for filtering and pagination
        const { searchParams } = new URL(request.url);

        // Filtering parameters
        const search = searchParams.get('search') || undefined;
        const isActive = searchParams.get('isActive') === 'true' ? true :
                        searchParams.get('isActive') === 'false' ? false : undefined;

        // Pagination parameters
        const page = parseInt(searchParams.get('page') || '1', 10);
        const limit = parseInt(searchParams.get('limit') || '10', 10);

        // Sorting parameters
        const sortBy = searchParams.get('sortBy') || 'name';
        const sortOrder = (searchParams.get('sortOrder') || 'asc') as 'asc' | 'desc';

        // Calculate pagination values
        const skip = (page - 1) * limit;

        // Get the merchant ID from the session
        const merchantId = await getMerchantId();
        if (!merchantId) {
            console.error('GET /api/vendors - No merchant ID found');
            return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
        }

        console.log(`GET /api/vendors - Using merchant ID: ${merchantId}`);

        // Check if the user has permission to view vendors (ReadOnly level or higher)
        // Create a new request with the user ID in the headers
        const requestWithUserId = new NextRequest(request.url, {
            headers: new Headers({
                'x-user-id': userId,
                ...Object.fromEntries(request.headers.entries())
            })
        });

        const permissionCheck = await requirePermission(requestWithUserId, merchantId, PermissionLevel.ReadOnly);
        if (permissionCheck) {
            console.warn('GET /api/vendors - Permission denied: User does not have ReadOnly level access');
            return permissionCheck;
        }

        // Try to fetch from the database
        // Get total count for pagination
        const totalCount = await vendorService.countByMerchant(merchantId, {
            search,
            isActive,
        });

        // Get vendors with pagination
        const vendors = await vendorService.findAllByMerchant(merchantId, {
            search,
            isActive,
            orderBy: { [sortBy]: sortOrder },
            skip,
            take: limit,
        });

        // Format the response using the utility function
        const formattedVendors = formatVendorsResponse(vendors);

        // Calculate pagination metadata
        const totalPages = Math.ceil(totalCount / limit);
        const hasNextPage = page < totalPages;
        const hasPreviousPage = page > 1;

        console.log(`GET /api/vendors - Returning ${formattedVendors.length} vendors from database (page ${page} of ${totalPages})`);

        return NextResponse.json({
            vendors: formattedVendors,
            pagination: {
                total: totalCount,
                page,
                limit,
                totalPages,
                hasNextPage,
                hasPreviousPage,
            },
        });
    } catch (error) {
        console.error("Failed to fetch vendors:", error);
        return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
    }
}

// POST /api/vendors - Create a new vendor
export async function POST(request: NextRequest) {
    try {
        // Check if the user is authenticated using our helper
        const { isAuthenticated, userId, errorResponse } = await checkAuth(request);

        if (!isAuthenticated) {
            console.error('POST /api/vendors - Authentication failed');
            return errorResponse;
        }

        console.log(`POST /api/vendors - Authenticated user: ${userId}`);

        // Get the merchant ID from the session
        const merchantId = await getMerchantId();
        if (!merchantId) {
            console.error('POST /api/vendors - No merchant ID found');
            return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
        }

        console.log(`POST /api/vendors - Using merchant ID: ${merchantId}`);

        // Check if the user has permission to create vendors (Staff level or higher)
        // Create a new request with the user ID in the headers
        const requestWithUserId = new NextRequest(request.url, {
            headers: new Headers({
                'x-user-id': userId,
                ...Object.fromEntries(request.headers.entries())
            })
        });

        const permissionCheck = await requirePermission(requestWithUserId, merchantId, PermissionLevel.Staff);
        if (permissionCheck) {
            console.warn('POST /api/vendors - Permission denied: User does not have Staff level access');
            return permissionCheck;
        }

        const body = await request.json();

        // Validate request body
        const validationResult = createVendorSchema.safeParse(body);
        if (!validationResult.success) {
            return NextResponse.json({ message: 'Validation failed', errors: validationResult.error.flatten().fieldErrors }, { status: 400 });
        }

        const validatedData = validationResult.data;

        // Map the validated data to the database model
        const vendorData = {
            name: validatedData.name,
            contactPerson: validatedData.contact_person,
            email: validatedData.email,
            phone: validatedData.phone || null,
            address: validatedData.address || null,
        };

        // Create the vendor in the database
        const newVendor = await vendorService.createForMerchant(merchantId, vendorData);

        // Format the response using the utility function
        const formattedVendor = formatVendorResponse(newVendor);

        return NextResponse.json(formattedVendor, { status: 201 });
    } catch (error) {
        console.error("Failed to create vendor:", error);
        return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
    }
}