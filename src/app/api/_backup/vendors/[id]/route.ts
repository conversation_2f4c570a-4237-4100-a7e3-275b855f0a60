import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { updateVendorSchema } from '@/lib/validators/vendor';
import { mockVendors, updateMockVendor, deleteMockVendor } from '@/lib/vendorsMockData';
import { vendorService } from '@/lib/services/vendorService';
import { getMerchantId } from '@/lib/utils/auth';
import { formatVendorResponse } from '@/lib/utils/apiFormatters';
import { requirePermission } from '@/lib/utils/permissions';
import { PermissionLevel } from '@prisma/client';

interface Params {
    params: { id: string };
}

// Placeholder for role-based authorization
const checkUserRole = async (request: NextRequest, allowedRoles: string[]): Promise<boolean> => {
    // TODO: Replace with actual role checking logic
    console.log(`Placeholder: Checking if user has one of roles: ${allowedRoles.join(', ')}`);
    return true; // Assume authorized for now
};

// GET /api/vendors/[id] - Get a specific vendor
export async function GET(request: NextRequest, { params }: Params) {
    try {
        const { id } = await Promise.resolve(params);
        const merchantId = await getMerchantId(request);

        if (!merchantId) {
            return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
        }

        // Check if the user has permission to view this vendor (ReadOnly level or higher)
        const permissionCheckGet = await requirePermission(request, merchantId, PermissionLevel.ReadOnly);
        if (permissionCheckGet) {
            console.warn(`GET /api/vendors/${id} - Permission denied: User does not have ReadOnly level access`);
            return permissionCheckGet;
        }

        // Try to fetch from the database
        try {
            const vendor = await vendorService.findById(id);

            // Check if vendor exists and belongs to the merchant
            if (!vendor || vendor.merchantId !== merchantId) {
                // Fall back to mock data if not found in database
                const mockVendor = mockVendors.find(v => v.id === id);

                if (!mockVendor || mockVendor.restaurant_id !== 'resto-123') {
                    return NextResponse.json({ message: 'Vendor not found or does not belong to this restaurant' }, { status: 404 });
                }

                return NextResponse.json(mockVendor, { status: 200 });
            // // Fallback logic removed
            return NextResponse.json({ message: 'Vendor not found or does not belong to this merchant' }, { status: 404 });
             }

            // Format the response using the utility function
            const formattedVendor = formatVendorResponse(vendor);

            return NextResponse.json(formattedVendor, { status: 200 });
        } catch (dbError) {
            console.error('Database error, falling back to mock data:', dbError);

            // Fall back to mock data if database fails
            const vendor = mockVendors.find(v => v.id === id);

            // Check if found and belongs to the correct restaurant
            if (!vendor || vendor.restaurant_id !== 'resto-123') {
                return NextResponse.json({ message: 'Vendor not found or does not belong to this restaurant' }, { status: 404 });
            }

            return NextResponse.json(vendor, { status: 200 });
        }
     } catch (error) {
         console.error(`Failed to fetch vendor:`, error);
         return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
    }
}

// PUT /api/vendors/[id] - Update a specific vendor
export async function PUT(request: NextRequest, { params }: Params) {
    try {
        const { id } = await Promise.resolve(params);
        const merchantId = await getMerchantId(request);

        if (!merchantId) {
            return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
        }

        // Check if the user has permission to update vendors (Staff level or higher)
        const permissionCheckPut = await requirePermission(request, merchantId, PermissionLevel.Staff);
        if (permissionCheckPut) {
            console.warn(`PUT /api/vendors/${id} - Permission denied: User does not have Staff level access`);
            return permissionCheckPut;
        }

        const body = await request.json();

        // Validate request body for partial updates
        const validationResult = updateVendorSchema.safeParse(body);
        if (!validationResult.success) {
            return NextResponse.json({ message: 'Validation failed', errors: validationResult.error.flatten().fieldErrors }, { status: 400 });
        }

        const validatedData = validationResult.data;

        if (Object.keys(validatedData).length === 0) {
            return NextResponse.json({ message: 'No valid fields provided for update.' }, { status: 400 });
        }

        // Try to update in the database
        try {
            // Map the validated data to the database model
            const updateData = {
                name: validatedData.name,
                contactPerson: validatedData.contactPerson,
                email: validatedData.email,
                phone: validatedData.phone,
                address: validatedData.address,
            };

            // Remove undefined values
            Object.keys(updateData).forEach(keyStr => {
                const key = keyStr as keyof typeof updateData;
                if (updateData[key] === undefined) {
                    delete updateData[key];
                }
            });

            // Update the vendor
            const updatedVendor = await vendorService.updateForMerchant(merchantId, id, updateData);

            // Format the response using the utility function
            const formattedVendor = formatVendorResponse(updatedVendor);

            return NextResponse.json(formattedVendor, { status: 200 });
        } catch (dbError) {
            console.error('Database error, falling back to mock data:', dbError);

            // Fall back to mock data if database fails
            // Find the vendor first to check if it exists
            const existingVendor = mockVendors.find(v => v.id === id && v.restaurant_id === 'resto-123');

            if (!existingVendor) {
                return NextResponse.json({ message: 'Vendor not found or does not belong to this restaurant' }, { status: 404 });
            }

            // Update the vendor
            const updatedVendor = updateMockVendor(id, {
                name: validatedData.name || existingVendor.name,
                contactPerson: validatedData.contactPerson || existingVendor.contactPerson,
                email: validatedData.email || existingVendor.email,
                phone: validatedData.phone || existingVendor.phone,
                address: validatedData.address || existingVendor.address,
            });

            if (!updatedVendor) {
                return NextResponse.json({ message: 'Failed to update vendor' }, { status: 500 });
            }

            return NextResponse.json(updatedVendor, { status: 200 });
        }
     } catch (error) {
         console.error(`Failed to update vendor:`, error);
         if (error instanceof z.ZodError) {
             return NextResponse.json({ message: 'Validation failed', errors: error.flatten().fieldErrors }, { status: 400 });
        }
        if (error instanceof SyntaxError) {
             return NextResponse.json({ message: 'Invalid JSON format' }, { status: 400 });
        }
        return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
    }
}

// DELETE /api/vendors/[id] - Delete a specific vendor
export async function DELETE(request: NextRequest, { params }: Params) {
    try {
        const { id } = await Promise.resolve(params);
        const merchantId = await getMerchantId(request);

        if (!merchantId) {
            return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
        }

        // Check if the user has permission to delete vendors (Manager level or higher)
        const permissionCheckDelete = await requirePermission(request, merchantId, PermissionLevel.Manager);
        if (permissionCheckDelete) {
            console.warn(`DELETE /api/vendors/${id} - Permission denied: User does not have Manager level access`);
            return permissionCheckDelete;
        }

        // Try to delete from the database
        try {
            // Delete the vendor
            await vendorService.deleteForMerchant(merchantId, id);

            // Return 204 No Content on successful deletion
            return new NextResponse(null, { status: 204 });
        } catch (dbError) {
            console.error('Database error, falling back to mock data:', dbError);

            // Fall back to mock data if database fails
            // Find the vendor first to check if it exists
            const existingVendor = mockVendors.find(v => v.id === id && v.restaurant_id === 'resto-123');

            if (!existingVendor) {
                return NextResponse.json({ message: 'Vendor not found or does not belong to this restaurant' }, { status: 404 });
            }

            // Delete the vendor from mock data
            const success = deleteMockVendor(id);

            if (!success) {
                return NextResponse.json({ message: 'Failed to delete vendor' }, { status: 500 });
            }

            return new NextResponse(null, { status: 204 });
        }
     } catch (error) {
         console.error(`Failed to delete vendor:`, error);
         return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
    }
}