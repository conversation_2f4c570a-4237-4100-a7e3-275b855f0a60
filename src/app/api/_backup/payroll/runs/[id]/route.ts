import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { payrollService } from '@/lib/services/payrollService';
import { getMerchantId } from '@/lib/utils/auth';
import { requirePermission } from '@/lib/utils/permissions';
import { PermissionLevel } from '@prisma/client';
import { formatPayrollRunResponse } from '@/lib/utils/apiFormatters';
import {
  mockPayrollRuns,
  setMockPayrollRuns,
  mockJournalEntries,
  setMockJournalEntries,
  mockJournalEntryLines,
  setMockJournalEntryLines,
  generateMockId,
  MOCK_WAGE_EXPENSE_ACCOUNT_ID,
  MOCK_EMPLOYER_TAX_EXPENSE_ACCOUNT_ID,
  MOCK_CASH_ACCOUNT_ID_PAYROLL,
  MOCK_EMP_TAX_PAYABLE_ACCOUNT_ID,
  MOCK_EMPLR_TAX_PAYABLE_ACCOUNT_ID,
} from '@/lib/mockData';
import { PayrollRun, PayrollRunStatus, JournalEntry, JournalEntryLine } from '@/lib/types';

interface Params {
  id: string;
}

// Validation schema for updating a payroll run
const updatePayrollRunSchema = z.object({
  pay_period_start_date: z.string().datetime({ offset: true }).optional(),
  pay_period_end_date: z.string().datetime({ offset: true }).optional(),
  payment_date: z.string().datetime({ offset: true }).optional(),
  gross_wages: z.number().min(0).optional(),
  employee_taxes: z.number().min(0).optional(),
  employer_taxes: z.number().min(0).optional(),
  net_pay: z.number().min(0).optional(),
  status: z.enum(['Draft', 'Processed', 'Cancelled']).optional(),
});

// GET /api/payroll/runs/[id] - Get a specific payroll run
export async function GET(request: NextRequest, { params }: { params: Params }) {
  try {
    // Await params before destructuring
    const id = params.id;

    // Get the merchant ID from the session
    const merchantId = await getMerchantId();
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
    }

    // Check if the user has permission to view this payroll run (ReadOnly level or higher)
    const permissionCheckGet = await requirePermission(request, merchantId, PermissionLevel.ReadOnly);
    if (permissionCheckGet) {
      console.warn(`GET /api/payroll/runs/${id} - Permission denied: User does not have ReadOnly level access`);
      return permissionCheckGet;
    }

    // Try to fetch from the database
    try {
      const payrollRun = await payrollService.findPayrollRunByIdForMerchant(merchantId, id);
      if (!payrollRun) {
        // Fall back to mock data if not found in database
        const mockPayrollRun = mockPayrollRuns.find((run) => run.payroll_run_id === id);
        if (!mockPayrollRun) {
          return NextResponse.json({ message: 'Payroll Run not found' }, { status: 404 });
        }
        return NextResponse.json(mockPayrollRun);
      }

      // Format the response using the utility function
      const formattedPayrollRun = formatPayrollRunResponse(payrollRun);

      return NextResponse.json(formattedPayrollRun);
    } catch (dbError) {
      console.error('Database error, falling back to mock data:', dbError);

      // Fall back to mock data if database fails
      const mockPayrollRun = mockPayrollRuns.find((run) => run.payroll_run_id === id);
      if (!mockPayrollRun) {
        return NextResponse.json({ message: 'Payroll Run not found' }, { status: 404 });
      }
      return NextResponse.json(mockPayrollRun);
    }
  } catch (error) {
    console.error(`Failed to fetch payroll run ${params.id}:`, error);
    return NextResponse.json(
      { message: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

// PUT /api/payroll/runs/[id] - Update a payroll run or process it
export async function PUT(request: NextRequest, { params }: { params: Params }) {
  try {
    // Await params before destructuring
    const id = params.id;
    const body = await request.json();

    // Validate the request body
    const validation = updatePayrollRunSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json({
        message: 'Validation failed',
        errors: validation.error.flatten().fieldErrors
      }, { status: 400 });
    }

    const validatedData = validation.data;

    // Get the merchant ID from the session
    const merchantId = await getMerchantId();
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
    }

    // Check if the user has permission to update/process this payroll run (Staff level or higher)
    const permissionCheckPut = await requirePermission(request, merchantId, PermissionLevel.Staff);
    if (permissionCheckPut) {
      console.warn(`PUT /api/payroll/runs/${id} - Permission denied: User does not have Staff level access`);
      return permissionCheckPut;
    }

    // Try to update the payroll run in the database
    try {
      // Prepare the update data
      const updateData: any = {};
      if (validatedData.pay_period_start_date) {
        updateData.payPeriodStartDate = new Date(validatedData.pay_period_start_date);
      }
      if (validatedData.pay_period_end_date) {
        updateData.payPeriodEndDate = new Date(validatedData.pay_period_end_date);
      }
      if (validatedData.payment_date) {
        updateData.paymentDate = new Date(validatedData.payment_date);
      }
      if (validatedData.gross_wages !== undefined) {
        updateData.totalGrossWages = validatedData.gross_wages;
      }
      if (validatedData.employee_taxes !== undefined) {
        updateData.totalEmployeeTaxesWithheld = validatedData.employee_taxes;
      }
      if (validatedData.employer_taxes !== undefined) {
        updateData.totalEmployerTaxes = validatedData.employer_taxes;
      }
      if (validatedData.net_pay !== undefined) {
        updateData.totalNetPay = validatedData.net_pay;
      }
      if (validatedData.status) {
        updateData.status = validatedData.status;
      }

      // Update the payroll run
      const payrollRun = await payrollService.updatePayrollRunForMerchant(merchantId, id, updateData);

      // Format the response using the utility function
      const formattedPayrollRun = formatPayrollRunResponse(payrollRun);

      return NextResponse.json(formattedPayrollRun);
    } catch (dbError) {
      console.error('Database error, falling back to mock data:', dbError);

      // Fall back to mock data if database fails
      const runIndex = mockPayrollRuns.findIndex((run) => run.payroll_run_id === id);
      if (runIndex === -1) {
        return NextResponse.json({ message: 'Payroll Run not found' }, { status: 404 });
      }

      const existingRun = mockPayrollRuns[runIndex];
      let updatedRun = { ...existingRun };
      let journalEntryCreated = false;
      let newJournalEntryId: string | undefined = undefined;

      // Handle status change to 'Processed'
      if (validatedData.status === PayrollRunStatus.Processed) {
        if (existingRun.status === PayrollRunStatus.Processed) {
          // Already processed, maybe return current state or a specific message
          return NextResponse.json(existingRun, { status: 200 }); // Idempotent
        }
        if (existingRun.status !== PayrollRunStatus.Draft) {
          return NextResponse.json(
            { message: `Cannot process run with status: ${existingRun.status}` },
            { status: 400 }
          );
        }

        // --- Generate Summary Journal Entry ---
        newJournalEntryId = generateMockId('je');
        const entryDate = new Date().toISOString();

        const newJournalEntry: JournalEntry = {
          id: newJournalEntryId,
          restaurant_id: existingRun.restaurant_id,
          entry_date: entryDate,
          description: `Payroll Run Processed - Period: ${existingRun.pay_period_start_date} to ${existingRun.pay_period_end_date}`,
          source_type: 'PayrollRun',
          source_id: existingRun.payroll_run_id,
          created_at: entryDate,
          updated_at: entryDate,
        };

        const newLines: JournalEntryLine[] = [
          // Debits
          {
            id: generateMockId('jel'), journal_entry_id: newJournalEntryId, account_id: MOCK_WAGE_EXPENSE_ACCOUNT_ID,
            type: 'Debit', amount: existingRun.gross_wages, created_at: entryDate, updated_at: entryDate,
          },
          {
            id: generateMockId('jel'), journal_entry_id: newJournalEntryId, account_id: MOCK_EMPLOYER_TAX_EXPENSE_ACCOUNT_ID,
            type: 'Debit', amount: existingRun.employer_taxes, created_at: entryDate, updated_at: entryDate,
          },
          // Credits
          {
            id: generateMockId('jel'), journal_entry_id: newJournalEntryId, account_id: MOCK_CASH_ACCOUNT_ID_PAYROLL,
            type: 'Credit', amount: existingRun.net_pay, created_at: entryDate, updated_at: entryDate,
          },
          {
            id: generateMockId('jel'), journal_entry_id: newJournalEntryId, account_id: MOCK_EMP_TAX_PAYABLE_ACCOUNT_ID,
            type: 'Credit', amount: existingRun.employee_taxes, created_at: entryDate, updated_at: entryDate,
          },
          {
            id: generateMockId('jel'), journal_entry_id: newJournalEntryId, account_id: MOCK_EMPLR_TAX_PAYABLE_ACCOUNT_ID,
            type: 'Credit', amount: existingRun.employer_taxes, created_at: entryDate, updated_at: entryDate,
          },
        ];

        // Add to mock stores
        setMockJournalEntries([...mockJournalEntries, newJournalEntry]);
        setMockJournalEntryLines([...mockJournalEntryLines, ...newLines]);

        // Update run status and link JE
        updatedRun.status = PayrollRunStatus.Processed;
        updatedRun.journal_entry_id = newJournalEntryId;
        updatedRun.updated_at = new Date().toISOString();
        journalEntryCreated = true;
      } else if (existingRun.status === PayrollRunStatus.Draft) {
        // Allow updating other fields only if in Draft status
        // Exclude status updates here as they are handled above
        const { status, ...updateData } = validatedData;
        updatedRun = {
          ...updatedRun,
          ...updateData,
          // Recalculate net_pay if relevant fields changed
          net_pay: (updateData.gross_wages ?? updatedRun.gross_wages) - (updateData.employee_taxes ?? updatedRun.employee_taxes),
          updated_at: new Date().toISOString(),
        };
      } else {
        // If trying to update a non-Draft, non-status field, reject
        const { status, ...otherUpdates } = validatedData;
        if (Object.keys(otherUpdates).length > 0) {
          return NextResponse.json(
            { message: `Cannot update fields of a run with status: ${existingRun.status}` },
            { status: 400 }
          );
        }
        // If only status was sent but it wasn't 'Processed', ignore or return error? Let's ignore for now.
        return NextResponse.json(existingRun, { status: 200 });
      }

      // Update the mock data store
      const updatedRuns = [...mockPayrollRuns];
      updatedRuns[runIndex] = updatedRun;
      setMockPayrollRuns(updatedRuns);

      return NextResponse.json(updatedRun);
    }
  } catch (error) {
    console.error(`Failed to update payroll run ${params.id}:`, error);
    if (error instanceof SyntaxError) {
      return NextResponse.json({ message: 'Invalid JSON body' }, { status: 400 });
    }
    return NextResponse.json(
      { message: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

// DELETE /api/payroll/runs/[id] - Delete a payroll run
export async function DELETE(request: NextRequest, { params }: { params: Params }) {
  try {
    // Await params before destructuring
    const id = params.id;

    // Get the merchant ID from the session
    const merchantId = await getMerchantId();
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
    }

    // Check if the user has permission to delete this payroll run (Manager level or higher)
    const permissionCheckDelete = await requirePermission(request, merchantId, PermissionLevel.Manager);
    if (permissionCheckDelete) {
      console.warn(`DELETE /api/payroll/runs/${id} - Permission denied: User does not have Manager level access`);
      return permissionCheckDelete;
    }

    // Try to delete from the database
    try {
      // First check if the payroll run exists and is in Draft status
      const payrollRun = await payrollService.findPayrollRunByIdForMerchant(merchantId, id);

      if (!payrollRun) {
        return NextResponse.json({ message: 'Payroll Run not found' }, { status: 404 });
      }

      // Only allow deletion of Draft payroll runs
      if (payrollRun.status !== PayrollRunStatus.Draft) {
        return NextResponse.json({
          message: `Cannot delete payroll run with status: ${payrollRun.status}. Only Draft payroll runs can be deleted.`
        }, { status: 400 });
      }

      // Delete the payroll run and its details
      await payrollService.deletePayrollRunForMerchant(merchantId, id);

      return NextResponse.json({ message: 'Payroll run deleted successfully' });
    } catch (dbError) {
      console.error('Database error deleting payroll run:', dbError);

      // Fall back to mock data if database fails
      const runIndex = mockPayrollRuns.findIndex((run) => run.payroll_run_id === id);
      if (runIndex === -1) {
        return NextResponse.json({ message: 'Payroll Run not found' }, { status: 404 });
      }

      const existingRun = mockPayrollRuns[runIndex];

      // Only allow deletion of Draft payroll runs
      if (existingRun.status !== PayrollRunStatus.Draft) {
        return NextResponse.json({
          message: `Cannot delete payroll run with status: ${existingRun.status}. Only Draft payroll runs can be deleted.`
        }, { status: 400 });
      }

      // Remove from mock data
      const updatedRuns = mockPayrollRuns.filter((run) => run.payroll_run_id !== id);
      setMockPayrollRuns(updatedRuns);

      return NextResponse.json({ message: 'Payroll run deleted successfully' });
    }
  } catch (error) {
    console.error(`Failed to delete payroll run ${params.id}:`, error);
    return NextResponse.json(
      { message: 'Internal Server Error' },
      { status: 500 }
    );
  }
}