import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { payrollService } from '@/lib/services/payrollService';
import { getMerchantId } from '@/lib/utils/auth';
import { requirePermission } from '@/lib/utils/permissions';
import { PermissionLevel } from '@prisma/client';
import { formatPayrollRunsResponse, formatPayrollRunResponse } from '@/lib/utils/apiFormatters';
import {
  mockPayrollRuns,
  setMockPayrollRuns,
  generateMockId,
} from '@/lib/mockData';
import { PayrollRun, PayrollRunStatus } from '@/lib/types';

// Validation schema for creating a payroll run
const createPayrollRunSchema = z.object({
  pay_period_start_date: z.string().datetime({ offset: true }),
  pay_period_end_date: z.string().datetime({ offset: true }),
  payment_date: z.string().datetime({ offset: true }),
  gross_wages: z.number().min(0),
  employee_taxes: z.number().min(0),
  employer_taxes: z.number().min(0),
  net_pay: z.number().min(0).optional(),
  status: z.enum(['Draft', 'Processed', 'Cancelled']).optional(),
});

// GET /api/payroll/runs - List all payroll runs
export async function GET(request: NextRequest) {
  try {
    // Get URL parameters for filtering and pagination
    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('start_date');
    const endDate = searchParams.get('end_date');
    const status = searchParams.get('status') as PayrollRunStatus | null;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const sortBy = searchParams.get('sortBy') || 'paymentDate';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // Get the merchant ID from the session
    const merchantId = await getMerchantId();
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
    }

    // Check if the user has permission to view payroll runs (ReadOnly level or higher)
    const permissionCheckGet = await requirePermission(request, merchantId, PermissionLevel.ReadOnly);
    if (permissionCheckGet) {
      console.warn('GET /api/payroll/runs - Permission denied: User does not have ReadOnly level access');
      return permissionCheckGet;
    }

    // Try to fetch from the database
    try {
        const { runs, totalCount } = await payrollService.getPayrollRuns(merchantId, { page, limit, sortBy, sortOrder, status });
        console.log(`GET /api/payroll/runs - Fetched ${runs.length} payroll runs from database`);

        const totalPages = Math.ceil(totalCount / limit);
        const hasNextPage = page < totalPages;
        const hasPreviousPage = page > 1;

        return NextResponse.json({
            runs,
            pagination: {
                total: totalCount,
                page,
                limit,
                totalPages,
                hasNextPage,
                hasPreviousPage,
            },
        });
    } catch (dbError) {
        console.error('Database error fetching payroll runs:', dbError);
        // Removed fallback to mock data
        return NextResponse.json({ message: 'Failed to fetch payroll runs due to database error' }, { status: 500 });
    }
} catch (error) {
    console.error('Failed to fetch payroll runs:', error);
    if (error instanceof Error) {
        if (error.message.includes('Merchant ID not found')) {
            return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
        }
        if (error.message.includes('Permission denied')) {
            return NextResponse.json({ message: 'Forbidden: Insufficient permissions' }, { status: 403 });
        }
    }
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
}
}

// POST /api/payroll/runs - Create a new payroll run (in Draft status)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate the request body
    const validation = createPayrollRunSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json({
        message: 'Validation failed',
        errors: validation.error.flatten().fieldErrors
      }, { status: 400 });
    }

    const validatedData = validation.data;

    // Get the merchant ID from the session
    const merchantId = await getMerchantId();
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
    }

    // Check if the user has permission to create payroll runs (Staff level or higher)
    const permissionCheckPost = await requirePermission(request, merchantId, PermissionLevel.Staff);
    if (permissionCheckPost) {
      console.warn('POST /api/payroll/runs - Permission denied: User does not have Staff level access');
      return permissionCheckPost;
    }

    // Try to create in the database
    try {
        // Convert the validated data to the format expected by the service
        const payrollRunData = {
            payPeriodStartDate: new Date(validatedData.pay_period_start_date),
            payPeriodEndDate: new Date(validatedData.pay_period_end_date),
            paymentDate: new Date(validatedData.payment_date),
            totalGrossWages: validatedData.gross_wages,
            totalEmployeeTaxesWithheld: validatedData.employee_taxes,
            totalEmployerTaxes: validatedData.employer_taxes,
            totalNetPay: validatedData.net_pay ?? (validatedData.gross_wages - validatedData.employee_taxes),
            status: validatedData.status ?? PayrollRunStatus.Draft
        };

        const newRun = await payrollService.createPayrollRunForMerchant(merchantId, payrollRunData);
        console.log('POST /api/payroll/runs - Created new payroll run in database');

        // Format the response
        const formattedRun = formatPayrollRunResponse(newRun);
        return NextResponse.json(formattedRun, { status: 201 });
    } catch (dbError) {
        console.error('Database error creating payroll run:', dbError);
        // Removed fallback to mock data
        // Add more specific error handling if needed (e.g., overlapping dates)
        return NextResponse.json({ message: 'Failed to create payroll run due to database error' }, { status: 500 });
    }
} catch (error) {
    console.error('Failed to create payroll run:', error);
    if (error instanceof z.ZodError) {
        return NextResponse.json({ errors: error.errors }, { status: 400 });
    }
    if (error instanceof Error) {
        if (error.message.includes('Merchant ID not found')) {
            return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
        }
        if (error.message.includes('Permission denied')) {
            return NextResponse.json({ message: 'Forbidden: Insufficient permissions' }, { status: 403 });
        }
    }
    // Handle JSON parsing errors or other unexpected errors
    if (error instanceof SyntaxError) {
         return NextResponse.json({ message: 'Invalid JSON body' }, { status: 400 });
    }
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
}
}