import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/lib/auth/session';
import { proxyGet, proxyPost, proxyPut, proxyDelete } from '@/lib/utils/proxy';
import { z } from 'zod';

// Zod schema for creating/updating a payroll detail
const payrollDetailSchema = z.object({
  employeeId: z.string().uuid('Invalid employee ID format'),
  payrollRunId: z.string().uuid('Invalid payroll run ID format'),
  hoursWorked: z.number().min(0, 'Hours worked must be a non-negative number').optional(),
  regularPay: z.number().min(0, 'Regular pay must be a non-negative number'),
  overtimePay: z.number().min(0, 'Overtime pay must be a non-negative number').optional(),
  bonusPay: z.number().min(0, 'Bonus pay must be a non-negative number').optional(),
  grossPay: z.number().min(0, 'Gross pay must be a non-negative number'),
  federalTax: z.number().min(0, 'Federal tax must be a non-negative number'),
  stateTax: z.number().min(0, 'State tax must be a non-negative number'),
  ficaTax: z.number().min(0, 'FICA tax must be a non-negative number'),
  otherDeductions: z.number().min(0, 'Other deductions must be a non-negative number').optional(),
  netPay: z.number().min(0, 'Net pay must be a non-negative number'),
});

// GET /api/payroll/details - Get all payroll details
export async function GET(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    return await proxyGet('/payroll/details', request);
  } catch (error) {
    console.error('GET /api/payroll/details error:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}

// POST /api/payroll/details - Create a new payroll detail
export async function POST(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    // Parse and validate the request body
    const body = await request.json();
    const validationResult = payrollDetailSchema.safeParse({
      employeeId: body.employee_id,
      payrollRunId: body.payroll_run_id,
      hoursWorked: body.hours_worked,
      regularPay: body.regular_pay,
      overtimePay: body.overtime_pay,
      bonusPay: body.bonus_pay,
      grossPay: body.gross_pay,
      federalTax: body.federal_tax,
      stateTax: body.state_tax,
      ficaTax: body.fica_tax,
      otherDeductions: body.other_deductions,
      netPay: body.net_pay,
    });
    
    if (!validationResult.success) {
      return NextResponse.json(
        { errors: validationResult.error.errors },
        { status: 400 }
      );
    }

    const validatedData = validationResult.data;
    
    // Transform the validated data to match the Rust backend's expected format
    const rustRequestBody = {
      employee_id: validatedData.employeeId,
      payroll_run_id: validatedData.payrollRunId,
      hours_worked: validatedData.hoursWorked,
      regular_pay: validatedData.regularPay,
      overtime_pay: validatedData.overtimePay,
      bonus_pay: validatedData.bonusPay,
      gross_pay: validatedData.grossPay,
      federal_tax: validatedData.federalTax,
      state_tax: validatedData.stateTax,
      fica_tax: validatedData.ficaTax,
      other_deductions: validatedData.otherDeductions,
      net_pay: validatedData.netPay,
    };

    // Proxy to Rust backend
    return await proxyPost('/payroll/details', request, rustRequestBody);
  } catch (error) {
    console.error('POST /api/payroll/details error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { errors: error.errors },
        { status: 400 }
      );
    }
    
    if (error instanceof SyntaxError) {
      return NextResponse.json(
        { message: 'Invalid JSON body' },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { message: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

// PUT /api/payroll/details/:id - Update a payroll detail
export async function PUT(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    // Extract the payroll detail ID from the URL
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const detailId = pathParts[pathParts.length - 1];

    if (!detailId) {
      return NextResponse.json({ message: 'Payroll detail ID is required' }, { status: 400 });
    }

    // Parse and validate the request body
    const body = await request.json();
    const validationResult = payrollDetailSchema.safeParse({
      employeeId: body.employee_id,
      payrollRunId: body.payroll_run_id,
      hoursWorked: body.hours_worked,
      regularPay: body.regular_pay,
      overtimePay: body.overtime_pay,
      bonusPay: body.bonus_pay,
      grossPay: body.gross_pay,
      federalTax: body.federal_tax,
      stateTax: body.state_tax,
      ficaTax: body.fica_tax,
      otherDeductions: body.other_deductions,
      netPay: body.net_pay,
    });
    
    if (!validationResult.success) {
      return NextResponse.json(
        { errors: validationResult.error.errors },
        { status: 400 }
      );
    }

    const validatedData = validationResult.data;
    
    // Transform the validated data to match the Rust backend's expected format
    const rustRequestBody = {
      employee_id: validatedData.employeeId,
      payroll_run_id: validatedData.payrollRunId,
      hours_worked: validatedData.hoursWorked,
      regular_pay: validatedData.regularPay,
      overtime_pay: validatedData.overtimePay,
      bonus_pay: validatedData.bonusPay,
      gross_pay: validatedData.grossPay,
      federal_tax: validatedData.federalTax,
      state_tax: validatedData.stateTax,
      fica_tax: validatedData.ficaTax,
      other_deductions: validatedData.otherDeductions,
      net_pay: validatedData.netPay,
    };

    return await proxyPut(`/payroll/details/${detailId}`, request, rustRequestBody);
  } catch (error) {
    console.error('PUT /api/payroll/details/:id error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { errors: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { message: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

// DELETE /api/payroll/details/:id - Delete a payroll detail
export async function DELETE(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    // Extract the payroll detail ID from the URL
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const detailId = pathParts[pathParts.length - 1];

    if (!detailId) {
      return NextResponse.json({ message: 'Payroll detail ID is required' }, { status: 400 });
    }

    return await proxyDelete(`/payroll/details/${detailId}`, request);
  } catch (error) {
    console.error('DELETE /api/payroll/details/:id error:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}
