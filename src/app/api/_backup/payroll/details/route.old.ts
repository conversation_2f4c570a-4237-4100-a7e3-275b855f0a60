// src/app/api/payroll/details/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getMerchantId } from '@/lib/utils/auth';
import { requirePermission } from '@/lib/utils/permissions';
import { PermissionLevel } from '@prisma/client';
import { mockPayrollDetails, mockPayrollRuns, mockEmployees, generateMockId } from '@/lib/mockData';
import type { PayrollDetail } from '@/lib/types';

// GET /api/payroll/details - List payroll details, optionally filtered by payroll run
export async function GET(request: NextRequest) {
  try {
    // Get merchant ID from session
    const merchantId = await getMerchantId(request);
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized - Merchant ID not found' }, { status: 401 });
    }

    // Check if the user has permission to view payroll details (ReadOnly level or higher)
    const permissionCheckGet = await requirePermission(request, merchantId, PermissionLevel.ReadOnly);
    if (permissionCheckGet) {
      console.warn('GET /api/payroll/details - Permission denied: User does not have ReadOnly level access');
      return permissionCheckGet;
    }
    const { searchParams } = new URL(request.url);
    const payrollRunId = searchParams.get('payroll_run_id');
    const employeeId = searchParams.get('employee_id');
    
    // Filter by parameters if provided
    let details = mockPayrollDetails;
    
    if (payrollRunId) {
      details = details.filter(detail => detail.payroll_run_id === payrollRunId);
    }
    
    if (employeeId) {
      details = details.filter(detail => detail.employee_id === employeeId);
    }
    
    return NextResponse.json(details);
  } catch (error) {
    console.error('Failed to fetch payroll details:', error);
    return NextResponse.json({ message: 'Failed to fetch payroll details' }, { status: 500 });
  }
}

// POST /api/payroll/details - Create a new payroll detail record
export async function POST(request: NextRequest) {
  try {
    // Get merchant ID from session
    const merchantId = await getMerchantId(request);
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized - Merchant ID not found' }, { status: 401 });
    }

    // Check if the user has permission to create payroll details (Staff level or higher)
    const permissionCheckPost = await requirePermission(request, merchantId, PermissionLevel.Staff);
    if (permissionCheckPost) {
      console.warn('POST /api/payroll/details - Permission denied: User does not have Staff level access');
      return permissionCheckPost;
    }
    const body = await request.json();

    // Basic validation
    if (!body.payroll_run_id || !body.employee_id || body.gross_pay === undefined || 
        body.federal_tax === undefined || body.state_tax === undefined || 
        body.fica_tax === undefined || body.net_pay === undefined) {
      return NextResponse.json({ message: 'Missing required payroll detail fields' }, { status: 400 });
    }

    // Check if payroll run exists
    const payrollRun = mockPayrollRuns.find(run => run.payroll_run_id === body.payroll_run_id);
    if (!payrollRun) {
      return NextResponse.json({ message: 'Payroll run not found' }, { status: 404 });
    }

    // Check if employee exists
    const employee = mockEmployees.find(emp => emp.employee_id === body.employee_id);
    if (!employee) {
      return NextResponse.json({ message: 'Employee not found' }, { status: 404 });
    }

    // Check for duplicate (same employee in same payroll run)
    const existingDetail = mockPayrollDetails.find(
      detail => detail.payroll_run_id === body.payroll_run_id && 
                detail.employee_id === body.employee_id
    );

    if (existingDetail) {
      return NextResponse.json({ 
        message: 'A payroll detail record already exists for this employee in this payroll run' 
      }, { status: 409 });
    }

    // Create new payroll detail
    const newDetail: PayrollDetail = {
      id: generateMockId('payroll-detail'),
      payroll_run_id: body.payroll_run_id,
      employee_id: body.employee_id,
      hours_worked: body.hours_worked,
      regular_pay: body.regular_pay || 0,
      overtime_pay: body.overtime_pay || 0,
      bonus_pay: body.bonus_pay || 0,
      gross_pay: body.gross_pay,
      federal_tax: body.federal_tax,
      state_tax: body.state_tax,
      fica_tax: body.fica_tax,
      other_deductions: body.other_deductions || 0,
      net_pay: body.net_pay,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    // Add to mock data store
    const updatedDetails = [...mockPayrollDetails, newDetail];
    mockPayrollDetails.length = 0;
    mockPayrollDetails.push(...updatedDetails);

    return NextResponse.json(newDetail, { status: 201 });
  } catch (error) {
    console.error('Failed to create payroll detail:', error);
    return NextResponse.json({ message: 'Failed to create payroll detail' }, { status: 500 });
  }
}
