// src/app/api/payroll/details/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getMerchantId } from '@/lib/utils/auth';
import { requirePermission } from '@/lib/utils/permissions';
import { PermissionLevel } from '@prisma/client';
import { mockPayrollDetails } from '@/lib/mockData';
import type { PayrollDetail } from '@/lib/types';

interface Params {
  params: { id: string };
}

// GET /api/payroll/details/[id] - Get a specific payroll detail record
export async function GET(request: NextRequest, { params }: Params) {
  try {
    // Get merchant ID from session
    const merchantId = await getMerchantId(request);
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized - Merchant ID not found' }, { status: 401 });
    }

    // Check if the user has permission to view payroll details (ReadOnly level or higher)
    const permissionCheckGet = await requirePermission(request, merchantId, PermissionLevel.ReadOnly);
    if (permissionCheckGet) {
      console.warn(`GET /api/payroll/details/${params.id} - Permission denied: User does not have ReadOnly level access`);
      return permissionCheckGet;
    }
    const { id } = params;
    const detail = mockPayrollDetails.find((detail) => detail.id === id);

    if (!detail) {
      return NextResponse.json({ message: 'Payroll detail not found' }, { status: 404 });
    }

    return NextResponse.json(detail);
  } catch (error) {
    console.error(`Failed to fetch payroll detail ${params.id}:`, error);
    return NextResponse.json({ message: 'Failed to fetch payroll detail' }, { status: 500 });
  }
}

// PUT /api/payroll/details/[id] - Update a specific payroll detail record
export async function PUT(request: NextRequest, { params }: Params) {
  try {
    // Get merchant ID from session
    const merchantId = await getMerchantId(request);
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized - Merchant ID not found' }, { status: 401 });
    }

    // Check if the user has permission to update payroll details (Staff level or higher)
    const permissionCheckPut = await requirePermission(request, merchantId, PermissionLevel.Staff);
    if (permissionCheckPut) {
      console.warn(`PUT /api/payroll/details/${params.id} - Permission denied: User does not have Staff level access`);
      return permissionCheckPut;
    }
    const { id } = params;
    const body = await request.json();

    const detailIndex = mockPayrollDetails.findIndex((detail) => detail.id === id);

    if (detailIndex === -1) {
      return NextResponse.json({ message: 'Payroll detail not found' }, { status: 404 });
    }

    // Update the detail with new values, preserving existing values if not provided
    const updatedDetail: PayrollDetail = {
      ...mockPayrollDetails[detailIndex],
      ...body,
      id, // Ensure ID doesn't change
      payroll_run_id: mockPayrollDetails[detailIndex].payroll_run_id, // Don't allow changing payroll run
      employee_id: mockPayrollDetails[detailIndex].employee_id, // Don't allow changing employee
      updated_at: new Date().toISOString(),
    };

    // Update the mock data store
    const updatedDetails = [...mockPayrollDetails];
    updatedDetails[detailIndex] = updatedDetail;
    mockPayrollDetails.length = 0;
    mockPayrollDetails.push(...updatedDetails);

    return NextResponse.json(updatedDetail);
  } catch (error) {
    console.error(`Failed to update payroll detail ${params.id}:`, error);
    return NextResponse.json({ message: 'Failed to update payroll detail' }, { status: 500 });
  }
}

// DELETE /api/payroll/details/[id] - Delete a specific payroll detail record
export async function DELETE(request: NextRequest, { params }: Params) {
  try {
    // Get merchant ID from session
    const merchantId = await getMerchantId(request);
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized - Merchant ID not found' }, { status: 401 });
    }

    // Check if the user has permission to delete payroll details (Manager level or higher)
    const permissionCheckDelete = await requirePermission(request, merchantId, PermissionLevel.Manager);
    if (permissionCheckDelete) {
      console.warn(`DELETE /api/payroll/details/${params.id} - Permission denied: User does not have Manager level access`);
      return permissionCheckDelete;
    }
    const { id } = params;
    const detailIndex = mockPayrollDetails.findIndex((detail) => detail.id === id);

    if (detailIndex === -1) {
      return NextResponse.json({ message: 'Payroll detail not found' }, { status: 404 });
    }

    // Remove the detail from the mock data store
    const updatedDetails = mockPayrollDetails.filter((detail) => detail.id !== id);
    mockPayrollDetails.length = 0;
    mockPayrollDetails.push(...updatedDetails);

    return NextResponse.json({ message: 'Payroll detail deleted successfully' });
  } catch (error) {
    console.error(`Failed to delete payroll detail ${params.id}:`, error);
    return NextResponse.json({ message: 'Failed to delete payroll detail' }, { status: 500 });
  }
}
