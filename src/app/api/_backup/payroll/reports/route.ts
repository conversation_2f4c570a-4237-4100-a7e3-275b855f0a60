// src/app/api/payroll/reports/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getMerchantId } from '@/lib/utils/auth';
import { requirePermission } from '@/lib/utils/permissions';
import { PermissionLevel, PayrollRunStatus } from '@prisma/client';
import { payrollService } from '@/lib/services/payrollService';
import { z } from 'zod';

// GET /api/payroll/reports - Generate payroll reports
export async function GET(request: NextRequest) {
  try {
    // Get merchant ID from session
    const merchantId = await getMerchantId(request);
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized - Merchant ID not found' }, { status: 401 });
    }

    // Check if the user has permission to view payroll reports (ReadOnly level or higher)
    const permissionCheckGet = await requirePermission(request, merchantId, PermissionLevel.ReadOnly);
    if (permissionCheckGet) {
      console.warn('GET /api/payroll/reports - Permission denied: User does not have ReadOnly level access');
      return permissionCheckGet;
    }

    // Get URL parameters
    const { searchParams } = new URL(request.url);
    const reportType = searchParams.get('report_type') || 'summary';
    const startDate = searchParams.get('start_date');
    const endDate = searchParams.get('end_date');
    const employeeId = searchParams.get('employee_id');
    
    // Validate dates
    if (!startDate || !endDate) {
      return NextResponse.json({ message: 'Start date and end date are required' }, { status: 400 });
    }
    
    try {
      // Parse dates
      const parsedStartDate = new Date(startDate);
      const parsedEndDate = new Date(endDate);
      
      if (isNaN(parsedStartDate.getTime()) || isNaN(parsedEndDate.getTime())) {
        return NextResponse.json({ message: 'Invalid date format' }, { status: 400 });
      }
      
      // Generate the appropriate report based on type
      switch (reportType) {
        case 'summary':
          return await generateSummaryReport(merchantId, parsedStartDate, parsedEndDate);
        case 'detail':
          return await generateDetailedReport(merchantId, parsedStartDate, parsedEndDate, employeeId);
        case 'tax':
          return await generateTaxReport(merchantId, parsedStartDate, parsedEndDate);
        default:
          return NextResponse.json({ message: 'Invalid report type' }, { status: 400 });
      }
    } catch (error) {
      console.error('Error generating payroll report:', error);
      return NextResponse.json({ message: 'Failed to generate payroll report' }, { status: 500 });
    }
  } catch (error) {
    console.error('Failed to generate payroll report:', error);
    return NextResponse.json({ message: 'Internal server error' }, { status: 500 });
  }
}

// Generate summary report
async function generateSummaryReport(merchantId: string, startDate: Date, endDate: Date) {
  // Get all processed payroll runs in the date range
  const { runs, totalCount } = await payrollService.getPayrollRuns(merchantId, {
    startDate: startDate.toISOString(),
    endDate: endDate.toISOString(),
    status: PayrollRunStatus.Processed,
  });
  
  // Calculate summary totals
  const totalGrossWages = runs.reduce((sum, run) => sum + run.gross_wages, 0);
  const totalEmployeeTaxes = runs.reduce((sum, run) => sum + run.employee_taxes, 0);
  const totalEmployerTaxes = runs.reduce((sum, run) => sum + run.employer_taxes, 0);
  const totalNetPay = runs.reduce((sum, run) => sum + run.net_pay, 0);
  
  return NextResponse.json({
    report_type: 'summary',
    start_date: startDate.toISOString(),
    end_date: endDate.toISOString(),
    total_payroll_runs: runs.length,
    total_gross_wages: totalGrossWages,
    total_employee_taxes: totalEmployeeTaxes,
    total_employer_taxes: totalEmployerTaxes,
    total_net_pay: totalNetPay,
    payroll_runs: runs,
  });
}

// Generate detailed report
async function generateDetailedReport(merchantId: string, startDate: Date, endDate: Date, employeeId?: string | null) {
  // Get all processed payroll runs in the date range
  const { runs } = await payrollService.getPayrollRuns(merchantId, {
    startDate: startDate.toISOString(),
    endDate: endDate.toISOString(),
    status: PayrollRunStatus.Processed,
  });
  
  // Get all payroll run IDs
  const payrollRunIds = runs.map(run => run.payroll_run_id);
  
  // Get all payroll details for these runs
  const payrollDetails = await payrollService.findAllPayrollDetailsByMerchant(merchantId, {
    payrollRunIds,
    employeeId: employeeId || undefined,
  });
  
  // Group details by employee
  const employeeDetailsMap = new Map();
  
  for (const detail of payrollDetails) {
    if (!employeeDetailsMap.has(detail.employeeId)) {
      employeeDetailsMap.set(detail.employeeId, {
        employee_id: detail.employeeId,
        employee_name: detail.employee.employeeName,
        total_gross_pay: 0,
        total_regular_pay: 0,
        total_overtime_pay: 0,
        total_bonus_pay: 0,
        total_federal_tax: 0,
        total_state_tax: 0,
        total_fica_tax: 0,
        total_other_deductions: 0,
        total_net_pay: 0,
        details: [],
      });
    }
    
    const employeeData = employeeDetailsMap.get(detail.employeeId);
    
    // Add to totals
    employeeData.total_gross_pay += Number(detail.grossPay);
    employeeData.total_regular_pay += Number(detail.regularPay);
    employeeData.total_overtime_pay += Number(detail.overtimePay);
    employeeData.total_bonus_pay += Number(detail.bonusPay);
    employeeData.total_federal_tax += Number(detail.federalTax);
    employeeData.total_state_tax += Number(detail.stateTax);
    employeeData.total_fica_tax += Number(detail.ficaTax);
    employeeData.total_other_deductions += Number(detail.otherDeductions);
    employeeData.total_net_pay += Number(detail.netPay);
    
    // Add detail
    const payrollRun = runs.find(run => run.payroll_run_id === detail.payrollRunId);
    
    employeeData.details.push({
      detail_id: detail.id,
      payroll_run_id: detail.payrollRunId,
      pay_period: payrollRun ? `${payrollRun.pay_period_start_date} - ${payrollRun.pay_period_end_date}` : 'Unknown',
      payment_date: payrollRun ? payrollRun.payment_date : 'Unknown',
      hours_worked: detail.hoursWorked ? Number(detail.hoursWorked) : null,
      regular_pay: Number(detail.regularPay),
      overtime_pay: Number(detail.overtimePay),
      bonus_pay: Number(detail.bonusPay),
      gross_pay: Number(detail.grossPay),
      federal_tax: Number(detail.federalTax),
      state_tax: Number(detail.stateTax),
      fica_tax: Number(detail.ficaTax),
      other_deductions: Number(detail.otherDeductions),
      net_pay: Number(detail.netPay),
    });
  }
  
  // Convert map to array
  const employeeDetails = Array.from(employeeDetailsMap.values());
  
  // Calculate overall totals
  const totalGrossPay = employeeDetails.reduce((sum, emp) => sum + emp.total_gross_pay, 0);
  const totalNetPay = employeeDetails.reduce((sum, emp) => sum + emp.total_net_pay, 0);
  
  return NextResponse.json({
    report_type: 'detail',
    start_date: startDate.toISOString(),
    end_date: endDate.toISOString(),
    total_employees: employeeDetails.length,
    total_gross_pay: totalGrossPay,
    total_net_pay: totalNetPay,
    employee_details: employeeDetails,
  });
}

// Generate tax report
async function generateTaxReport(merchantId: string, startDate: Date, endDate: Date) {
  // Get all processed payroll runs in the date range
  const { runs } = await payrollService.getPayrollRuns(merchantId, {
    startDate: startDate.toISOString(),
    endDate: endDate.toISOString(),
    status: PayrollRunStatus.Processed,
  });
  
  // Get all payroll run IDs
  const payrollRunIds = runs.map(run => run.payroll_run_id);
  
  // Get all payroll details for these runs
  const payrollDetails = await payrollService.findAllPayrollDetailsByMerchant(merchantId, {
    payrollRunIds,
  });
  
  // Calculate tax totals
  const totalFederalTax = payrollDetails.reduce((sum, detail) => sum + Number(detail.federalTax), 0);
  const totalStateTax = payrollDetails.reduce((sum, detail) => sum + Number(detail.stateTax), 0);
  const totalFicaTax = payrollDetails.reduce((sum, detail) => sum + Number(detail.ficaTax), 0);
  const totalEmployerTax = runs.reduce((sum, run) => sum + run.employer_taxes, 0);
  
  // Group by month for quarterly reporting
  const monthlyTaxes = new Map();
  
  for (const run of runs) {
    const paymentDate = new Date(run.payment_date);
    const monthKey = `${paymentDate.getFullYear()}-${String(paymentDate.getMonth() + 1).padStart(2, '0')}`;
    
    if (!monthlyTaxes.has(monthKey)) {
      monthlyTaxes.set(monthKey, {
        year: paymentDate.getFullYear(),
        month: paymentDate.getMonth() + 1,
        month_name: paymentDate.toLocaleString('default', { month: 'long' }),
        federal_tax: 0,
        state_tax: 0,
        fica_tax: 0,
        employer_tax: 0,
      });
    }
    
    const monthData = monthlyTaxes.get(monthKey);
    
    // Get details for this run
    const runDetails = payrollDetails.filter(detail => detail.payrollRunId === run.payroll_run_id);
    
    // Add to monthly totals
    monthData.federal_tax += runDetails.reduce((sum, detail) => sum + Number(detail.federalTax), 0);
    monthData.state_tax += runDetails.reduce((sum, detail) => sum + Number(detail.stateTax), 0);
    monthData.fica_tax += runDetails.reduce((sum, detail) => sum + Number(detail.ficaTax), 0);
    monthData.employer_tax += run.employer_taxes;
  }
  
  // Convert map to array and sort by year/month
  const monthlyTaxData = Array.from(monthlyTaxes.values())
    .sort((a, b) => (a.year !== b.year) ? a.year - b.year : a.month - b.month);
  
  return NextResponse.json({
    report_type: 'tax',
    start_date: startDate.toISOString(),
    end_date: endDate.toISOString(),
    total_federal_tax: totalFederalTax,
    total_state_tax: totalStateTax,
    total_fica_tax: totalFicaTax,
    total_employer_tax: totalEmployerTax,
    monthly_tax_data: monthlyTaxData,
  });
}
