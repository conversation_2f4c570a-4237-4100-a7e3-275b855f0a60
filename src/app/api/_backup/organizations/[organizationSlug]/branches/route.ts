import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/lib/auth/session';
import { proxyGet, proxyPost } from '@/lib/utils/proxy';

// GET /api/organizations/[organizationSlug]/branches - Get all branches for an organization
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ organizationSlug: string }> }
) {
  const session = await getSession();
  if (!session) {
    return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
  }

  const { organizationSlug } = await params;
  console.log(`GET /api/organizations/${organizationSlug}/branches - User: ${session.user.email}, proxying to Rust backend`);
  
  // Proxy to Rust backend
  return await proxyGet(`/organizations/${organizationSlug}/branches`, request);
}

// POST /api/organizations/[organizationSlug]/branches - Create a new branch
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ organizationSlug: string }> }
) {
  const session = await getSession();
  if (!session) {
    return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
  }

  const { organizationSlug } = await params;
  console.log(`POST /api/organizations/${organizationSlug}/branches - User: ${session.user.email}, proxying to Rust backend`);
  
  // Proxy to Rust backend
  return await proxyPost(`/organizations/${organizationSlug}/branches`, request);
}
