import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/lib/auth/session';
import { proxyGet, proxyPut, proxyDelete } from '@/lib/utils/proxy';

// GET /api/organizations/[organizationSlug]/branches/[branchSlug] - Get a specific branch
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ organizationSlug: string; branchSlug: string }> }
) {
  const session = await getSession();
  if (!session) {
    return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
  }

  const { organizationSlug, branchSlug } = await params;
  console.log(`GET /api/organizations/${organizationSlug}/branches/${branchSlug} - User: ${session.user.email}, proxying to Rust backend`);
  
  // Proxy to Rust backend
  return await proxyGet(`/organizations/${organizationSlug}/branches/${branchSlug}`, request);
}

// PUT /api/organizations/[organizationSlug]/branches/[branchSlug] - Update a branch
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ organizationSlug: string; branchSlug: string }> }
) {
  const session = await getSession();
  if (!session) {
    return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
  }

  const { organizationSlug, branchSlug } = await params;
  console.log(`PUT /api/organizations/${organizationSlug}/branches/${branchSlug} - User: ${session.user.email}, proxying to Rust backend`);
  
  // Proxy to Rust backend
  return await proxyPut(`/organizations/${organizationSlug}/branches/${branchSlug}`, request);
}

// DELETE /api/organizations/[organizationSlug]/branches/[branchSlug] - Delete a branch
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ organizationSlug: string; branchSlug: string }> }
) {
  const session = await getSession();
  if (!session) {
    return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
  }

  const { organizationSlug, branchSlug } = await params;
  console.log(`DELETE /api/organizations/${organizationSlug}/branches/${branchSlug} - User: ${session.user.email}, proxying to Rust backend`);
  
  // Proxy to Rust backend
  return await proxyDelete(`/organizations/${organizationSlug}/branches/${branchSlug}`, request);
}
