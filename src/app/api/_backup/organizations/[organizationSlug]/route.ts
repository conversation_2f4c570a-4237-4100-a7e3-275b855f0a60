import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/lib/auth/session';
import { proxyGet, proxyPut, proxyDelete } from '@/lib/utils/proxy';

// GET /api/organizations/[organizationSlug] - Get a specific organization
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ organizationSlug: string }> }
) {
  const session = await getSession();
  if (!session) {
    return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
  }

  const { organizationSlug } = await params;
  console.log(`GET /api/organizations/${organizationSlug} - User: ${session.user.email}, proxying to Rust backend`);
  
  // Proxy to Rust backend
  return await proxyGet(`/organizations/${organizationSlug}`, request);
}

// PUT /api/organizations/[organizationSlug] - Update an organization
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ organizationSlug: string }> }
) {
  const session = await getSession();
  if (!session) {
    return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
  }

  const { organizationSlug } = await params;
  console.log(`PUT /api/organizations/${organizationSlug} - User: ${session.user.email}, proxying to Rust backend`);
  
  // Proxy to Rust backend
  return await proxyPut(`/organizations/${organizationSlug}`, request);
}

// DELETE /api/organizations/[organizationSlug] - Delete an organization
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ organizationSlug: string }> }
) {
  const session = await getSession();
  if (!session) {
    return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
  }

  const { organizationSlug } = await params;
  console.log(`DELETE /api/organizations/${organizationSlug} - User: ${session.user.email}, proxying to Rust backend`);
  
  // Proxy to Rust backend
  return await proxyDelete(`/organizations/${organizationSlug}`, request);
}
