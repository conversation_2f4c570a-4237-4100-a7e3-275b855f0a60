import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/lib/auth/session';
import { proxyGet, proxyPost } from '@/lib/utils/proxy';

// GET /api/organizations - Get all organizations the user has access to
export async function GET(request: NextRequest) {
  const session = await getSession();
  if (!session) {
    return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
  }

  console.log(`GET /api/organizations - User: ${session.user.email}, proxying to Rust backend`);

  // Proxy to Rust backend
  return await proxyGet('/organizations', request);
}

// POST /api/organizations - Create a new organization
export async function POST(request: NextRequest) {
  const session = await getSession();
  if (!session) {
    return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
  }

  console.log(`POST /api/organizations - User: ${session.user.email}, proxying to Rust backend`);

  // Proxy to Rust backend
  return await proxyPost('/organizations', request);
}
