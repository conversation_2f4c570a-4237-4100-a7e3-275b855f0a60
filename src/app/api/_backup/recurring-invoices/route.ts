import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { RecurringInvoice, RecurringFrequency, RecurringStatus } from '@/lib/types';
import { z } from 'zod';

import { recurringInvoiceService } from '@/lib/services/recurringInvoiceService';
import { getMerchantId } from '@/lib/utils/auth';
import { requirePermission } from '@/lib/utils/permissions';
import { PermissionLevel } from '@prisma/client';
import { formatRecurringInvoiceResponse, formatRecurringInvoicesResponse } from '@/lib/utils/apiFormatters';

// Zod schema for GET query parameters
const getRecurringInvoicesSchema = z.object({
  customer_id: z.string().nullable().optional(),
  status: z.string().nullable().optional().transform(val =>
    val ? (Object.values(RecurringStatus).includes(val as any) ? val as RecurringStatus : undefined) : undefined
  ),
  frequency: z.string().nullable().optional().transform(val =>
    val ? (Object.values(RecurringFrequency).includes(val as any) ? val as RecurringFrequency : undefined) : undefined
  ),
});

// Zod schema for POST request body
const createRecurringInvoiceSchema = z.object({
  customer_id: z.string(),
  name: z.string(),
  frequency: z.string().refine(val => Object.values(RecurringFrequency).includes(val as any), {
    message: `Frequency must be one of: ${Object.values(RecurringFrequency).join(', ')}`
  }),
  interval: z.number().int().positive(),
  start_date: z.string().transform(val => {
    try {
      // Try to parse the date and return it in ISO format
      return new Date(val).toISOString();
    } catch (e) {
      // Return the original value if parsing fails
      return val;
    }
  }),
  end_date: z.string().transform(val => {
    try {
      // Try to parse the date and return it in ISO format
      return new Date(val).toISOString();
    } catch (e) {
      // Return the original value if parsing fails
      return val;
    }
  }).optional(),
  days_due_after: z.number().int().nonnegative(),
  subtotal: z.number().nonnegative(),
  tax_amount: z.number().nonnegative(),
  total_amount: z.number().nonnegative(),
  notes: z.string().optional(),
  terms: z.string().optional(),
});

// GET /api/recurring-invoices - List all recurring invoices
export async function GET(request: NextRequest) {
  try {
    // Get URL parameters for filtering
    const { searchParams } = new URL(request.url);
    const customerId = searchParams.get('customer_id');
    const status = searchParams.get('status');
    const frequency = searchParams.get('frequency');

    // Validate query parameters using Zod
    const queryParams = {
      customer_id: customerId || null,
      status: status || null,
      frequency: frequency || null,
    };
    const validationResult = getRecurringInvoicesSchema.safeParse(queryParams);

    if (!validationResult.success) {
      return NextResponse.json({ errors: validationResult.error.errors }, { status: 400 });
    }
    const validatedParams = validationResult.data;

    // Get the merchant ID from the session
    const merchantId = await getMerchantId();
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
    }

    // Check permissions - this returns a Response if permission is denied, or null if allowed
    const permissionCheckResult = await requirePermission(request, merchantId, PermissionLevel.ReadOnly);
    if (permissionCheckResult) {
      console.warn(`GET /api/recurring-invoices - Permission denied: User does not have ReadOnly level access`);
      return permissionCheckResult;
    }

    // Try to fetch from the database
    try {
      const recurringInvoices = await recurringInvoiceService.findAllByMerchant(merchantId, {
        customerId: validatedParams.customer_id || undefined,
        status: validatedParams.status || undefined,
        frequency: validatedParams.frequency || undefined,
      });

      // Format the response using the utility function
      const formattedRecurringInvoices = formatRecurringInvoicesResponse(recurringInvoices);

      return NextResponse.json(formattedRecurringInvoices);
    } catch (dbError) {
      console.error('Database error fetching recurring invoices:', dbError);
      // Propagate the error without falling back to mock data
      return NextResponse.json({ message: 'Failed to fetch recurring invoices due to database error' }, { status: 500 });
    }
  } catch (error) {
    console.error('Failed to fetch recurring invoices:', error);
    if (error instanceof z.ZodError) {
      return NextResponse.json({ errors: error.errors }, { status: 400 });
    }
    if (error instanceof Error) {
      if (error.message.includes('Unauthorized')) {
        return NextResponse.json({ message: error.message }, { status: 401 });
      }
      if (error.message.includes('Forbidden')) {
        return NextResponse.json({ message: error.message }, { status: 403 });
      }
    }
    return NextResponse.json({ message: 'Failed to fetch recurring invoices' }, { status: 500 });
  }
}

// POST /api/recurring-invoices - Create a new recurring invoice
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate request body using Zod
    const validationResult = createRecurringInvoiceSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json({ errors: validationResult.error.errors }, { status: 400 });
    }
    const validatedData = validationResult.data;

    // Get the merchant ID from the session
    const merchantId = await getMerchantId();
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
    }

    // Check permissions - this returns a Response if permission is denied, or null if allowed
    const permissionCheckResult = await requirePermission(request, merchantId, PermissionLevel.Staff);
    if (permissionCheckResult) {
      console.warn(`POST /api/recurring-invoices - Permission denied: User does not have Staff level access`);
      return permissionCheckResult;
    }

    // Try to create in the database
    try {
      // Create the recurring invoice with the service
      const newRecurringInvoice = await recurringInvoiceService.createForMerchant(merchantId, {
        customerId: validatedData.customer_id,
        name: validatedData.name,
        frequency: validatedData.frequency,
        interval: validatedData.interval,
        startDate: validatedData.start_date,
        endDate: validatedData.end_date,
        daysDueAfter: validatedData.days_due_after,
        subtotal: validatedData.subtotal,
        taxAmount: validatedData.tax_amount,
        totalAmount: validatedData.total_amount,
        notes: validatedData.notes,
        terms: validatedData.terms,
      });

      // Format the response using the utility function
      const formattedRecurringInvoice = formatRecurringInvoiceResponse(newRecurringInvoice);

      return NextResponse.json(formattedRecurringInvoice, { status: 201 });
    } catch (dbError) {
      console.error('Database error creating recurring invoice:', dbError);
      // Handle specific database errors (e.g., foreign key constraint)
      if (dbError.code === 'P2003' && dbError.meta?.field_name?.includes('customerId')) {
        return NextResponse.json({ message: 'Customer not found' }, { status: 404 });
      }
      // Propagate the error without falling back to mock data
      return NextResponse.json({ message: 'Failed to create recurring invoice due to database error' }, { status: 500 });
    }
  } catch (error) {
    console.error('Failed to create recurring invoice:', error);
    if (error instanceof z.ZodError) {
      return NextResponse.json({ errors: error.errors }, { status: 400 });
    }
    if (error instanceof Error) {
      if (error.message.includes('Unauthorized')) {
        return NextResponse.json({ message: error.message }, { status: 401 });
      }
      if (error.message.includes('Forbidden')) {
        return NextResponse.json({ message: error.message }, { status: 403 });
      }
    }
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}
