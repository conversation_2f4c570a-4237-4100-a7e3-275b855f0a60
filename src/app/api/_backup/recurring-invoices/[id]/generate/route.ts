import { NextRequest, NextResponse } from 'next/server';
import { RecurringStatus } from '@/lib/types';
import { recurringInvoiceService } from '@/lib/services/recurringInvoiceService';
import { getMerchantId } from '@/lib/utils/auth';
import { requirePermission } from '@/lib/utils/permissions';
import { PermissionLevel } from '@prisma/client';
import { z } from 'zod';

interface Params {
  params: { id: string }; // The 'id' here refers to the recurring_invoice_id
}

// POST /api/recurring-invoices/[id]/generate - Generate an invoice from a recurring template
export async function POST(request: NextRequest, { params }: Params) {
  const recurring_invoice_id = params.id;

  try {
    // Get the merchant ID from the session
    const merchantId = await getMerchantId();
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
    }

    // Check permissions
    const permissionCheckPost = await requirePermission(request, merchantId, PermissionLevel.Staff);
    if (permissionCheckPost) {
      console.warn(`POST /api/recurring-invoices/${recurring_invoice_id}/generate - Permission denied: User does not have Staff level access`);
      return permissionCheckPost;
    }

    // Try to generate the invoice from the database
    try {
      // Generate the invoice
      const result = await recurringInvoiceService.generateInvoice(merchantId, recurring_invoice_id);

      return NextResponse.json({
        message: result.message,
        invoice_id: result.invoiceId
      }, { status: 201 });
    } catch (dbError) {
      console.error('Database error generating invoice:', dbError);

      // If the error is a validation error (Zod error)
      if (dbError.name === 'ZodError' || (dbError.errors && Array.isArray(dbError.errors))) {
        return NextResponse.json({ errors: dbError.errors || dbError.message }, { status: 400 });
      }

      // If the error is a specific business rule error, return it directly
      if (dbError instanceof Error) {
        if (dbError.message.includes('not found')) {
          return NextResponse.json({ message: 'Recurring invoice not found' }, { status: 404 });
        }
        if (dbError.message.includes('Cannot generate invoice from')) {
          return NextResponse.json({ message: dbError.message }, { status: 400 });
        }
        if (dbError.message.includes('Not time yet') || dbError.message.includes('No items to invoice')) {
          return NextResponse.json({ message: dbError.message }, { status: 400 });
        }

        // Check for validation errors in the message
        if (dbError.message.includes('validation') || dbError.message.includes('invalid')) {
          return NextResponse.json({ message: dbError.message }, { status: 400 });
        }

        // Return the error message for any other errors
        return NextResponse.json({ message: dbError.message }, { status: 400 });
      }

      // For non-Error objects, return a generic error
      return NextResponse.json({ message: 'Failed to generate invoice due to a database error' }, { status: 500 });
    }
  } catch (error) {
    console.error('Failed to generate invoice:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}
