import { NextResponse } from 'next/server';
import { RecurringInvoice, RecurringStatus, RecurringFrequency } from '@/lib/types';
import { recurringInvoiceService } from '@/lib/services/recurringInvoiceService';
import { getMerchantId } from '@/lib/utils/auth';
import { requirePermission } from '@/lib/utils/permissions';
import { PermissionLevel } from '@prisma/client';
import { z } from 'zod';
import { formatRecurringInvoiceResponse } from '@/lib/utils/apiFormatters';
import { addDays, addMonths, addWeeks, addYears } from 'date-fns';

// Helper function to calculate the next date based on frequency and interval
function calculateNextDate(baseDate: Date, frequency: RecurringFrequency, interval: number): Date {
  const date = new Date(baseDate);

  switch (frequency) {
    case RecurringFrequency.Daily:
      return addDays(date, interval);
    case RecurringFrequency.Weekly:
      return addWeeks(date, interval);
    case RecurringFrequency.Monthly:
      return addMonths(date, interval);
    case RecurringFrequency.Quarterly:
      return addMonths(date, interval * 3);
    case RecurringFrequency.Yearly:
      return addYears(date, interval);
    default:
      return addMonths(date, interval); // Default to monthly
  }
}

interface Params {
  params: { id: string };
}

// Zod schema for PUT request body
const updateRecurringInvoiceSchema = z.object({
  customer_id: z.string().optional(),
  name: z.string().optional(),
  frequency: z.nativeEnum(RecurringFrequency).optional(),
  interval: z.number().int().positive().optional(),
  start_date: z.string().datetime({ message: "Invalid start date format" }).optional(),
  end_date: z.string().datetime({ message: "Invalid end date format" }).optional().nullable(), // Allow null to clear end date
  days_due_after: z.number().int().nonnegative().optional(),
  status: z.nativeEnum(RecurringStatus).optional(),
  subtotal: z.number().nonnegative().optional(),
  tax_amount: z.number().nonnegative().optional(),
  total_amount: z.number().nonnegative().optional(),
  notes: z.string().optional().nullable(), // Allow null to clear notes
  terms: z.string().optional().nullable(), // Allow null to clear terms
}).refine(data => Object.keys(data).length > 0, {
  message: "At least one field must be provided for update",
});


// GET /api/recurring-invoices/[id] - Get a specific recurring invoice
export async function GET(request: Request, { params }: Params) {
  const { id } = params;

  try {
    // Get the merchant ID from the session
    const merchantId = await getMerchantId();
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
    }

    // Check permissions
    const permissionCheckGet = await requirePermission(request, merchantId, PermissionLevel.ReadOnly);
    if (permissionCheckGet) {
      console.warn(`GET /api/recurring-invoices/${id} - Permission denied: User does not have ReadOnly level access`);
      return permissionCheckGet;
    }

    // Try to fetch from the database
    try {
      const recurringInvoice = await recurringInvoiceService.findByIdForMerchant(merchantId, id); // Use findByIdForMerchant for consistency
       if (!recurringInvoice) {
         return NextResponse.json({ message: 'Recurring invoice not found' }, { status: 404 });
       }
       console.log(`GET /api/recurring-invoices/${id} - Fetched recurring invoice from database`);
      // Format the response using the utility function
      const formattedRecurringInvoice = formatRecurringInvoiceResponse(recurringInvoice);
      return NextResponse.json(formattedRecurringInvoice);
     } catch (dbError) {
       console.error(`Database error fetching recurring invoice ${id}:`, dbError);
       // Removed fallback to mock data
      return NextResponse.json({ message: 'Failed to fetch recurring invoice due to database error' }, { status: 500 });
    }
} catch (error) {
    console.error(`Failed to fetch recurring invoice ${id}:`, error);
    if (error instanceof Error) {
        if (error.message.includes('Merchant ID not found')) {
            return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
        }
        if (error.message.includes('Permission denied')) {
            return NextResponse.json({ message: 'Forbidden: Insufficient permissions' }, { status: 403 });
        }
    }
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
}
}

// PUT /api/recurring-invoices/[id] - Update a specific recurring invoice
export async function PUT(request: Request, { params }: Params) {
  const { id } = params;
  try {
    const body = await request.json();

    // Get the merchant ID from the session
    const merchantId = await getMerchantId();
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
    }

    // Check permissions
    const permissionCheckResult = await requirePermission(request, merchantId, PermissionLevel.Staff);
    if (permissionCheckResult) {
      console.warn(`PUT /api/recurring-invoices/${id} - Permission denied: User does not have Staff level access`);
      return permissionCheckResult;
    }

    // Validate request body
    const validationSchema = z.object({
      customer_id: z.string().optional(),
      name: z.string().optional(),
      frequency: z.enum(Object.values(RecurringFrequency) as [string, ...string[]]).optional(),
      interval: z.number().int().positive().optional(),
      start_date: z.string().optional().transform(val => val ? new Date(val) : undefined),
      end_date: z.string().nullable().optional().transform(val => val ? new Date(val) : null),
      days_due_after: z.number().int().nonnegative().optional(),
      status: z.enum(Object.values(RecurringStatus) as [string, ...string[]]).optional(),
      subtotal: z.number().nonnegative().optional(),
      tax_amount: z.number().nonnegative().optional(),
      total_amount: z.number().nonnegative().optional(),
      notes: z.string().nullable().optional(),
      terms: z.string().nullable().optional(),
    });

    const validationResult = validationSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json({ errors: validationResult.error.errors }, { status: 400 });
    }

    const validatedData = validationResult.data;

    // Prepare updates object
    const updates: any = {};
    if (validatedData.customer_id !== undefined) updates.customerId = validatedData.customer_id;
    if (validatedData.name !== undefined) updates.name = validatedData.name;
    if (validatedData.frequency !== undefined) updates.frequency = validatedData.frequency;
    if (validatedData.interval !== undefined) updates.interval = validatedData.interval;
    if (validatedData.start_date !== undefined) updates.startDate = validatedData.start_date;
    if (validatedData.end_date !== undefined) updates.endDate = validatedData.end_date;
    if (validatedData.days_due_after !== undefined) updates.daysDueAfter = validatedData.days_due_after;
    if (validatedData.status !== undefined) updates.status = validatedData.status;
    if (validatedData.subtotal !== undefined) updates.subtotal = validatedData.subtotal;
    if (validatedData.tax_amount !== undefined) updates.taxAmount = validatedData.tax_amount;
    if (validatedData.total_amount !== undefined) updates.totalAmount = validatedData.total_amount;
    if (validatedData.notes !== undefined) updates.notes = validatedData.notes;
    if (validatedData.terms !== undefined) updates.terms = validatedData.terms;

    // If no updates provided, return the current invoice
    if (Object.keys(updates).length === 0) {
      try {
        const recurringInvoice = await recurringInvoiceService.findByIdForMerchant(merchantId, id);
        if (!recurringInvoice) {
          return NextResponse.json({ message: 'Recurring invoice not found' }, { status: 404 });
        }

        // Format the response using the utility function
        const formattedRecurringInvoice = formatRecurringInvoiceResponse(recurringInvoice);
        return NextResponse.json(formattedRecurringInvoice);
      } catch (error) {
        console.error(`Error fetching recurring invoice ${id}:`, error);
        return NextResponse.json({ message: 'Failed to fetch recurring invoice' }, { status: 500 });
      }
    }

    // Calculate next date if frequency or interval changes
    if (validatedData.frequency !== undefined || validatedData.interval !== undefined) {
      try {
        const currentInvoice = await recurringInvoiceService.findByIdForMerchant(merchantId, id);
        if (currentInvoice) {
          const frequency = validatedData.frequency ?? currentInvoice.frequency;
          const interval = validatedData.interval ?? currentInvoice.interval;
          const baseDate = currentInvoice.lastGeneratedDate ?? currentInvoice.startDate;

          updates.nextDate = calculateNextDate(baseDate, frequency as RecurringFrequency, interval);
        }
      } catch (error) {
        console.error(`Error calculating next date for recurring invoice ${id}:`, error);
      }
    }

     // Try to update in the database
     try {
       const updatedRecurringInvoice = await recurringInvoiceService.updateForMerchant(merchantId, id, updates);

       if (!updatedRecurringInvoice) {
         return NextResponse.json({ message: 'Recurring invoice not found' }, { status: 404 });
       }

       console.log(`PUT /api/recurring-invoices/${id} - Updated recurring invoice in database`);
       // Format the response using the utility function
       const formattedRecurringInvoice = formatRecurringInvoiceResponse(updatedRecurringInvoice);
       return NextResponse.json(formattedRecurringInvoice);
     } catch (dbError) {
       console.error(`Database error updating recurring invoice ${id}:`, dbError);
       // Handle specific database errors (e.g., foreign key constraint for customer_id)
       if (dbError.code === 'P2003' && dbError.meta?.field_name?.includes('customerId')) {
         return NextResponse.json({ message: 'Customer not found' }, { status: 404 });
       }
       if (dbError.code === 'P2025') { // Prisma error code for record not found during update
         return NextResponse.json({ message: 'Recurring invoice not found' }, { status: 404 });
       }
       return NextResponse.json({ message: 'Failed to update recurring invoice due to database error' }, { status: 500 });
     }
   } catch (error) {
     console.error(`Failed to update recurring invoice ${id}:`, error);
     if (error instanceof z.ZodError) {
        return NextResponse.json({ errors: error.errors }, { status: 400 });
     }
     if (error instanceof Error) {
         if (error.message.includes('Merchant ID not found')) {
             return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
        }
        if (error.message.includes('Permission denied')) {
            return NextResponse.json({ message: 'Forbidden: Insufficient permissions' }, { status: 403 });
        }
    }
    // Handle JSON parsing errors or other unexpected errors
    if (error instanceof SyntaxError) {
         return NextResponse.json({ message: 'Invalid JSON body' }, { status: 400 });
    }
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}

// DELETE /api/recurring-invoices/[id] - Delete a specific recurring invoice
export async function DELETE(request: Request, { params }: Params) {
  const { id } = params;

  try {
    // Get the merchant ID from the session
    const merchantId = await getMerchantId();
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
    }

    // Check permissions
    const permissionCheckResult = await requirePermission(request, merchantId, PermissionLevel.Manager);
    if (permissionCheckResult) {
      console.warn(`DELETE /api/recurring-invoices/${id} - Permission denied: User does not have Manager level access`);
      return permissionCheckResult;
    }

    // Try to delete from the database
    try {
      const result = await recurringInvoiceService.deleteForMerchant(merchantId, id);
      if (!result) {
        return NextResponse.json({ message: 'Recurring invoice not found' }, { status: 404 });
      }
      return NextResponse.json({ message: 'Recurring invoice deleted successfully' }, { status: 200 });
    } catch (dbError) {
      console.error('Database error deleting recurring invoice:', dbError);

      if (dbError.code === 'P2025') { // Prisma error code for record not found
        return NextResponse.json({ message: 'Recurring invoice not found' }, { status: 404 });
      }

      return NextResponse.json({ message: 'Failed to delete recurring invoice due to database error' }, { status: 500 });
    }
  } catch (error) {
    console.error(`Error deleting recurring invoice ${id}:`, error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}
