import { NextRequest, NextResponse } from 'next/server';
import { RecurringInvoiceItem } from '@/lib/types';
import {
  findMockRecurringInvoiceItemsByRecurringInvoiceId,
  addMockRecurringInvoiceItem,
  generateMockRecurringInvoiceItemId,
  findMockRecurringInvoiceById,
  updateMockRecurringInvoice
} from '@/lib/recurringInvoicesMockData';
import { recurringInvoiceService } from '@/lib/services/recurringInvoiceService';
import { getMerchantId } from '@/lib/utils/auth';
import { requirePermission } from '@/lib/utils/permissions';
import { PermissionLevel } from '@prisma/client';
import { formatRecurringInvoiceItemResponse, formatRecurringInvoiceItemsResponse } from '@/lib/utils/apiFormatters';
import { z } from 'zod';

interface Params {
  params: { id: string }; // The 'id' here refers to the recurring_invoice_id
}

// GET /api/recurring-invoices/[id]/items - List items for a specific recurring invoice
export async function GET(request: Request, { params }: Params) {
  const recurring_invoice_id = params.id;

  try {
    // Get the merchant ID from the session
    const merchantId = await getMerchantId();
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
    }

    // Check permissions
    const permissionCheckResult = await requirePermission(request, merchantId, PermissionLevel.ReadOnly);
    if (permissionCheckResult) {
      console.warn(`GET /api/recurring-invoices/${recurring_invoice_id}/items - Permission denied: User does not have ReadOnly level access`);
      return permissionCheckResult;
    }

    // Try to fetch items using the service
    try {
        const items = await recurringInvoiceService.getRecurringInvoiceItems(merchantId, recurring_invoice_id);
        console.log(`GET /api/recurring-invoices/${recurring_invoice_id}/items - Fetched ${items.length} items from service`);
        return NextResponse.json(items);
    } catch (dbError) {
        console.error(`Service error fetching items for recurring invoice ${recurring_invoice_id}:`, dbError);
        // Removed fallback to mock data
        if (dbError instanceof Error && dbError.message.includes('not found')) {
            return NextResponse.json({ message: 'Recurring invoice not found' }, { status: 404 });
        }
        return NextResponse.json({ message: 'Failed to fetch recurring invoice items due to a service error' }, { status: 500 });
    }

} catch (error) {
    console.error(`Failed to fetch items for recurring invoice ${recurring_invoice_id}:`, error);
    if (error instanceof Error) {
        if (error.message.includes('Merchant ID not found')) {
            return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
        }
        if (error.message.includes('Permission denied')) {
            return NextResponse.json({ message: 'Forbidden: Insufficient permissions' }, { status: 403 });
        }
    }
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
}
}

// POST /api/recurring-invoices/[id]/items - Add a new item to a recurring invoice
export async function POST(request: NextRequest, { params }: Params) {
  const recurring_invoice_id = params.id;

  try {
    const body = await request.json();

    // Get the merchant ID from the session
    const merchantId = await getMerchantId();
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
    }

    // Check permissions
    const permissionCheckResult = await requirePermission(request, merchantId, PermissionLevel.Staff);
    if (permissionCheckResult) {
      console.warn(`POST /api/recurring-invoices/${recurring_invoice_id}/items - Permission denied: User does not have Staff level access`);
      return permissionCheckResult;
    }

    // Basic validation
    if (!body.description || typeof body.quantity !== 'number' || typeof body.unit_price !== 'number') {
      return NextResponse.json({
        message: 'Missing required fields (description, quantity, unit_price)'
      }, { status: 400 });
    }

    // Calculate tax amount if tax_rate is provided
    let taxAmount = 0;
    if (body.tax_rate) {
      taxAmount = body.quantity * body.unit_price * body.tax_rate;
    }

    // Calculate line total
    const lineTotal = (body.quantity * body.unit_price) + taxAmount;

    // Try to add the item to the database
    try {
      // First check if the recurring invoice exists
      const recurringInvoice = await recurringInvoiceService.findByIdForMerchant(merchantId, recurring_invoice_id);

      if (!recurringInvoice) {
        // Fall back to mock data if not found in database
        const mockRecurringInvoice = findMockRecurringInvoiceById(recurring_invoice_id);

        if (!mockRecurringInvoice) {
          return NextResponse.json({ message: 'Recurring invoice not found' }, { status: 404 });
        }

        // Create new recurring invoice item in mock data
        const newItem: RecurringInvoiceItem = {
          id: generateMockRecurringInvoiceItemId(),
          recurring_invoice_id,
          description: body.description,
          quantity: body.quantity,
          unit_price: body.unit_price,
          tax_rate_id: body.tax_rate_id || undefined,
          tax_rate: body.tax_rate || undefined,
          tax_amount: taxAmount,
          line_total: lineTotal,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        };

        // Add item to mock database
        addMockRecurringInvoiceItem(newItem);

        // Update recurring invoice totals
        const recurringInvoiceItems = findMockRecurringInvoiceItemsByRecurringInvoiceId(recurring_invoice_id);
        const newSubtotal = recurringInvoiceItems.reduce((sum, item) => sum + (item.quantity * item.unit_price), 0);
        const newTaxAmount = recurringInvoiceItems.reduce((sum, item) => sum + item.tax_amount, 0);
        const newTotalAmount = recurringInvoiceItems.reduce((sum, item) => sum + item.line_total, 0);

        // Update the recurring invoice with new totals
        updateMockRecurringInvoice(recurring_invoice_id, {
          subtotal: newSubtotal,
          tax_amount: newTaxAmount,
          total_amount: newTotalAmount,
        });

        return NextResponse.json(newItem, { status: 201 });
      }

      // Add the item to the database
      const newItem = await recurringInvoiceService.addRecurringInvoiceItem(recurring_invoice_id, {
        description: body.description,
        quantity: body.quantity,
        unitPrice: body.unit_price,
        taxRate: body.tax_rate,
        taxAmount: taxAmount,
        lineTotal: lineTotal,
      });

      // Format the response using the utility function
      const formattedItem = formatRecurringInvoiceItemResponse(newItem);

      return NextResponse.json(formattedItem, { status: 201 });
    } catch (dbError) {
      console.error('Database error, falling back to mock data:', dbError);

      // Fall back to mock data if database fails
      // Check if recurring invoice exists
      const recurringInvoice = findMockRecurringInvoiceById(recurring_invoice_id);
      if (!recurringInvoice) {
        return NextResponse.json({ message: 'Recurring invoice not found' }, { status: 404 });
      }

      // Create new recurring invoice item
      const newItem: RecurringInvoiceItem = {
        id: generateMockRecurringInvoiceItemId(),
        recurring_invoice_id,
        description: body.description,
        quantity: body.quantity,
        unit_price: body.unit_price,
        tax_rate_id: body.tax_rate_id || undefined,
        tax_rate: body.tax_rate || undefined,
        tax_amount: taxAmount,
        line_total: lineTotal,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      // Add item to mock database
      addMockRecurringInvoiceItem(newItem);

      // Update recurring invoice totals
      const recurringInvoiceItems = findMockRecurringInvoiceItemsByRecurringInvoiceId(recurring_invoice_id);
      const newSubtotal = recurringInvoiceItems.reduce((sum, item) => sum + (item.quantity * item.unit_price), 0);
      const newTaxAmount = recurringInvoiceItems.reduce((sum, item) => sum + item.tax_amount, 0);
      const newTotalAmount = recurringInvoiceItems.reduce((sum, item) => sum + item.line_total, 0);

      // Update the recurring invoice with new totals
      updateMockRecurringInvoice(recurring_invoice_id, {
        subtotal: newSubtotal,
        tax_amount: newTaxAmount,
        total_amount: newTotalAmount,
      });

      return NextResponse.json(newItem, { status: 201 });
    }
  } catch (error) {
    console.error('Failed to create recurring invoice item:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}
