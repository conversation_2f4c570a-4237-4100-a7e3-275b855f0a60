import { NextRequest, NextResponse } from 'next/server';
// Removed unused type import
// import { SalesOrder } from '@/lib/types';
import { salesOrderService } from '@/lib/services/salesOrderService';
import { getMerchantId } from '@/lib/utils/auth';
import { formatSalesOrdersResponse } from '@/lib/utils/apiFormatters';
import { requirePermission } from '@/lib/utils/permissions';
import { PermissionLevel } from '@prisma/client';
import { z } from 'zod'; // Added Zod import

// Removed mock data import
// import { mockSalesOrders } from '@/lib/mockData';

// Zod schema for GET query parameters
const getOrdersSchema = z.object({
  start_date: z.string().datetime({ message: "Invalid start date format" }).optional(),
  end_date: z.string().datetime({ message: "Invalid end date format" }).optional(),
  external_order_id: z.string().optional(),
});

export async function GET(request: NextRequest) {
  try {
    // Get URL parameters for filtering
    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('start_date');
    const endDate = searchParams.get('end_date');
    const externalOrderId = searchParams.get('external_order_id');

    // Validate query parameters using Zod
    const queryParams = {
      start_date: startDate,
      end_date: endDate,
      external_order_id: externalOrderId,
    };
    const validationResult = getOrdersSchema.safeParse(queryParams);

    if (!validationResult.success) {
      return NextResponse.json({ errors: validationResult.error.errors }, { status: 400 });
    }
    const validatedParams = validationResult.data;

    // Get the merchant ID from the session
    const merchantId = await getMerchantId();
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
    }

    // Check if the user has permission to view orders (ReadOnly level or higher)
    const permissionCheck = await requirePermission(request, merchantId, PermissionLevel.ReadOnly);
    if (permissionCheck) {
      console.warn('GET /api/orders - Permission denied: User does not have ReadOnly level access');
      return permissionCheck;
    }

    // Try to fetch from the database
    try {
       const salesOrders = await salesOrderService.findAllByMerchant(merchantId, {
        startDate: validatedParams.start_date ? new Date(validatedParams.start_date) : undefined,
        endDate: validatedParams.end_date ? new Date(validatedParams.end_date) : undefined,
        externalOrderId: validatedParams.external_order_id || undefined,
       });

       // Format the response using the utility function
      const formattedSalesOrders = formatSalesOrdersResponse(salesOrders);

      return NextResponse.json(formattedSalesOrders);
    } catch (dbError) {
      console.error('Database error fetching sales orders:', dbError);
      // Propagate the error without falling back to mock data
      return NextResponse.json({ message: 'Failed to fetch sales orders due to database error' }, { status: 500 });
     }
   } catch (error) {
     console.error('Error fetching orders:', error);
     if (error instanceof z.ZodError) {
       return NextResponse.json({ errors: error.errors }, { status: 400 });
     }
     if (error instanceof Error) {
       if (error.message.includes('Unauthorized')) {
         return NextResponse.json({ message: error.message }, { status: 401 });
       }
       if (error.message.includes('Forbidden')) {
         return NextResponse.json({ message: error.message }, { status: 403 });
       }
       if (error.message.includes('Invalid date format')) {
         return NextResponse.json({ message: 'Bad Request: Invalid date format' }, { status: 400 });
       }
     }
     return NextResponse.json({ message: 'Failed to fetch orders' }, { status: 500 });
   }
 }

// Note: Orders are also added via the /api/webhooks/orders endpoint.