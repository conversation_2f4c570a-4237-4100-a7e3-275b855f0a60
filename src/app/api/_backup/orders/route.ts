import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/lib/auth/session';
import { proxyGet, proxyPost, proxyPut, proxyDelete } from '@/lib/utils/proxy';
import { z } from 'zod';
import { OrderStatus } from '@/lib/types';

// Zod schema for creating an order
const createOrderSchema = z.object({
  customerId: z.string().min(1, 'Customer ID is required'),
  orderNumber: z.string().min(1, 'Order number is required'),
  orderDate: z.string().refine(val => {
    try {
      return new Date(val).toISOString();
    } catch (e) {
      return false;
    }
  }, { message: "Invalid order date format" }),
  dueDate: z.string().refine(val => {
    try {
      return new Date(val).toISOString();
    } catch (e) {
      return false;
    }
  }, { message: "Invalid due date format" }).optional(),
  subtotal: z.number().positive('Subtotal must be a positive number'),
  taxAmount: z.number().min(0, 'Tax amount cannot be negative'),
  totalAmount: z.number().positive('Total amount must be positive'),
  status: z.nativeEnum(OrderStatus).optional().default(OrderStatus.Pending),
  notes: z.string().optional(),
  externalOrderId: z.string().optional(),
  items: z.array(z.object({
    description: z.string().min(1, 'Line item description is required'),
    quantity: z.number().positive('Quantity must be positive'),
    unitPrice: z.number().positive('Unit price must be positive'),
    total: z.number().positive('Line item total must be positive'),
  })).optional().default([]),
});

// GET /api/orders - Proxy to Rust backend
export async function GET(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    return await proxyGet('/orders', request);
  } catch (error) {
    console.error('GET /api/orders error:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}

// POST /api/orders - Validate and proxy to Rust backend
export async function POST(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    // Parse and validate the request body
    const body = await request.json();
    const validationResult = createOrderSchema.safeParse({
      customerId: body.customer_id,
      orderNumber: body.order_number,
      orderDate: body.order_date,
      dueDate: body.due_date,
      subtotal: parseFloat(body.subtotal),
      taxAmount: parseFloat(body.tax_amount),
      totalAmount: parseFloat(body.total_amount),
      status: body.status,
      notes: body.notes,
      externalOrderId: body.external_order_id,
      items: body.items?.map(item => ({
        description: item.description,
        quantity: item.quantity,
        unitPrice: item.unit_price,
        total: item.total,
      })),
    });
    
    if (!validationResult.success) {
      return NextResponse.json(
        { errors: validationResult.error.errors },
        { status: 400 }
      );
    }

    const validatedData = validationResult.data;
    
    // Transform the validated data to match the Rust backend's expected format
    const rustRequestBody = {
      customer_id: validatedData.customerId,
      order_number: validatedData.orderNumber,
      order_date: validatedData.orderDate,
      due_date: validatedData.dueDate,
      subtotal: validatedData.subtotal,
      tax_amount: validatedData.taxAmount,
      total_amount: validatedData.totalAmount,
      status: validatedData.status,
      notes: validatedData.notes,
      external_order_id: validatedData.externalOrderId,
      items: validatedData.items?.map(item => ({
        description: item.description,
        quantity: item.quantity,
        unit_price: item.unitPrice,
        total: item.total,
      })),
    };

    // Proxy to Rust backend
    return await proxyPost('/orders', request, rustRequestBody);
  } catch (error) {
    console.error('POST /api/orders error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { errors: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { message: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

// PUT /api/orders/:id - Update an order
export async function PUT(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    // Extract the order ID from the URL
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const orderId = pathParts[pathParts.length - 1];

    if (!orderId) {
      return NextResponse.json({ message: 'Order ID is required' }, { status: 400 });
    }

    return await proxyPut(`/orders/${orderId}`, request);
  } catch (error) {
    console.error('PUT /api/orders/:id error:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}

// DELETE /api/orders/:id - Delete an order
export async function DELETE(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    // Extract the order ID from the URL
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const orderId = pathParts[pathParts.length - 1];

    if (!orderId) {
      return NextResponse.json({ message: 'Order ID is required' }, { status: 400 });
    }

    return await proxyDelete(`/orders/${orderId}`, request);
  } catch (error) {
    console.error('DELETE /api/orders/:id error:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}
