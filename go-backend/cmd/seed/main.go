package main

import (
	"log"
	"time"

	"adc-account-backend/internal/config"
	"adc-account-backend/internal/database"
	"adc-account-backend/internal/models"

	"github.com/shopspring/decimal"
)

func main() {
	log.Println("Starting database seeding...")

	// Load configuration
	cfg := config.Load()

	// Initialize database
	db, err := database.Initialize(cfg.DatabaseURL)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	// Clear existing data for re-seeding
	log.Println("Clearing existing data...")

	// Delete in reverse order of dependencies
	db.Exec("DELETE FROM expenses")
	db.Exec("DELETE FROM bill_payments")
	db.Exec("DELETE FROM bill_items")
	db.Exec("DELETE FROM bills")
	db.Exec("DELETE FROM invoice_payments")
	db.Exec("DELETE FROM invoice_items")
	db.Exec("DELETE FROM invoices")
	db.Exec("DELETE FROM chart_of_accounts")
	db.Exec("DELETE FROM vendors")
	db.Exec("DELETE FROM customers")
	db.Exec("DELETE FROM user_preferences")
	db.Exec("DELETE FROM user_merchant_permissions")
	db.Exec("DELETE FROM user_organization_permissions")
	db.Exec("DELETE FROM user_branch_permissions")
	db.Exec("DELETE FROM refresh_tokens")
	db.Exec("DELETE FROM users")
	db.Exec("DELETE FROM merchants")

	log.Println("Existing data cleared, proceeding with seeding...")

	// Helper functions
	stringPtr := func(s string) *string { return &s }
	timePtr := func(t time.Time) *time.Time { return &t }

	// Create merchant
	merchant := models.Merchant{
		ID:                  "cma6ptpdn0000f0lx2mp3lhob",
		Name:                "Demo Company Inc",
		LegalName:           stringPtr("Demo Company Incorporated"),
		Address:             stringPtr("123 Business St, Suite 100, Business City, BC 12345"),
		Phone:               stringPtr("******-123-4567"),
		PrimaryContactEmail: stringPtr("<EMAIL>"),
		Website:             stringPtr("https://democompany.com"),
		TaxID:               stringPtr("12-3456789"),
		Currency:            "USD",
		FiscalYearStart:     stringPtr("1"),
		LogoURL:             stringPtr(""),
		StripeCustomerID:    stringPtr(""),
	}

	if err := db.Create(&merchant).Error; err != nil {
		log.Fatalf("Failed to create merchant: %v", err)
	}
	log.Printf("Created merchant: %s", merchant.Name)

	// Create customers
	customers := []models.Customer{
		{
			MerchantID:   "cma6ptpdn0000f0lx2mp3lhob",
			Name:         "ABC Corporation",
			Email:        stringPtr("<EMAIL>"),
			Phone:        stringPtr("******-111-2222"),
			Address:      stringPtr("789 Corporate Blvd, Corp City, CC 11111"),
			TaxID:        stringPtr("11-1111111"),
			PaymentTerms: stringPtr("Net 30"),
			CreditLimit:  decimal.NewFromFloat(50000.00),
			IsActive:     true,
		},
		{
			MerchantID:   "cma6ptpdn0000f0lx2mp3lhob",
			Name:         "XYZ Industries",
			Email:        stringPtr("<EMAIL>"),
			Phone:        stringPtr("******-222-3333"),
			Address:      stringPtr("321 Industrial Way, Ind City, IC 22222"),
			TaxID:        stringPtr("22-2222222"),
			PaymentTerms: stringPtr("Net 15"),
			CreditLimit:  decimal.NewFromFloat(75000.00),
			IsActive:     true,
		},
		{
			MerchantID:   "cma6ptpdn0000f0lx2mp3lhob",
			Name:         "Tech Solutions Ltd",
			Email:        stringPtr("<EMAIL>"),
			Phone:        stringPtr("******-333-4444"),
			Address:      stringPtr("654 Tech Park Dr, Tech City, TC 33333"),
			TaxID:        stringPtr("33-3333333"),
			PaymentTerms: stringPtr("Net 30"),
			CreditLimit:  decimal.NewFromFloat(60000.00),
			IsActive:     true,
		},
		{
			MerchantID:   "cma6ptpdn0000f0lx2mp3lhob",
			Name:         "Global Services Inc",
			Email:        stringPtr("<EMAIL>"),
			Phone:        stringPtr("******-444-5555"),
			Address:      stringPtr("987 Global Plaza, Global City, GC 44444"),
			TaxID:        stringPtr("44-4444444"),
			PaymentTerms: stringPtr("Net 45"),
			CreditLimit:  decimal.NewFromFloat(40000.00),
			IsActive:     true,
		},
		{
			MerchantID:   "cma6ptpdn0000f0lx2mp3lhob",
			Name:         "Innovation Partners",
			Email:        stringPtr("<EMAIL>"),
			Phone:        stringPtr("******-555-6666"),
			Address:      stringPtr("147 Innovation St, Innov City, IC 55555"),
			TaxID:        stringPtr("55-5555555"),
			PaymentTerms: stringPtr("Net 30"),
			CreditLimit:  decimal.NewFromFloat(35000.00),
			IsActive:     true,
		},
	}

	for _, customer := range customers {
		if err := db.Create(&customer).Error; err != nil {
			log.Fatalf("Failed to create customer %s: %v", customer.Name, err)
		}
		log.Printf("Created customer: %s", customer.Name)
	}

	// Create vendors
	vendors := []models.Vendor{
		{
			MerchantID:   "cma6ptpdn0000f0lx2mp3lhob",
			Name:         "Office Supplies Co",
			Email:        stringPtr("<EMAIL>"),
			Phone:        stringPtr("******-666-7777"),
			Address:      stringPtr("258 Supply St, Supply City, SC 66666"),
			TaxID:        stringPtr("66-6666666"),
			PaymentTerms: stringPtr("Net 30"),
			IsActive:     true,
		},
		{
			MerchantID:   "cma6ptpdn0000f0lx2mp3lhob",
			Name:         "Tech Equipment Ltd",
			Email:        stringPtr("<EMAIL>"),
			Phone:        stringPtr("******-777-8888"),
			Address:      stringPtr("369 Equipment Ave, Equip City, EC 77777"),
			TaxID:        stringPtr("77-7777777"),
			PaymentTerms: stringPtr("Net 15"),
			IsActive:     true,
		},
		{
			MerchantID:   "cma6ptpdn0000f0lx2mp3lhob",
			Name:         "Utilities Provider",
			Email:        stringPtr("<EMAIL>"),
			Phone:        stringPtr("******-888-9999"),
			Address:      stringPtr("741 Utility Rd, Util City, UC 88888"),
			TaxID:        stringPtr("88-8888888"),
			PaymentTerms: stringPtr("Net 10"),
			IsActive:     true,
		},
		{
			MerchantID:   "cma6ptpdn0000f0lx2mp3lhob",
			Name:         "Marketing Agency",
			Email:        stringPtr("<EMAIL>"),
			Phone:        stringPtr("******-999-0000"),
			Address:      stringPtr("852 Marketing Blvd, Market City, MC 99999"),
			TaxID:        stringPtr("99-9999999"),
			PaymentTerms: stringPtr("Net 30"),
			IsActive:     true,
		},
		{
			MerchantID:   "cma6ptpdn0000f0lx2mp3lhob",
			Name:         "Legal Services",
			Email:        stringPtr("<EMAIL>"),
			Phone:        stringPtr("******-000-1111"),
			Address:      stringPtr("963 Legal Lane, Legal City, LC 00000"),
			TaxID:        stringPtr("00-0000000"),
			PaymentTerms: stringPtr("Net 15"),
			IsActive:     true,
		},
	}

	for _, vendor := range vendors {
		if err := db.Create(&vendor).Error; err != nil {
			log.Fatalf("Failed to create vendor %s: %v", vendor.Name, err)
		}
		log.Printf("Created vendor: %s", vendor.Name)
	}

	// Create chart of accounts
	accounts := []models.ChartOfAccount{
		{
			MerchantID:  "cma6ptpdn0000f0lx2mp3lhob",
			Code:        "1000",
			Name:        "Cash",
			Type:        models.AccountTypeAsset,
			Description: stringPtr("Cash and cash equivalents"),
			IsActive:    true,
			Balance:     decimal.NewFromFloat(50000.00),
		},
		{
			MerchantID:  "cma6ptpdn0000f0lx2mp3lhob",
			Code:        "1200",
			Name:        "Accounts Receivable",
			Type:        models.AccountTypeAsset,
			Description: stringPtr("Money owed by customers"),
			IsActive:    true,
			Balance:     decimal.NewFromFloat(29000.00),
		},
		{
			MerchantID:  "cma6ptpdn0000f0lx2mp3lhob",
			Code:        "2000",
			Name:        "Accounts Payable",
			Type:        models.AccountTypeLiability,
			Description: stringPtr("Money owed to vendors"),
			IsActive:    true,
			Balance:     decimal.NewFromFloat(20500.00),
		},
		{
			MerchantID:  "cma6ptpdn0000f0lx2mp3lhob",
			Code:        "4000",
			Name:        "Sales Revenue",
			Type:        models.AccountTypeRevenue,
			Description: stringPtr("Revenue from sales"),
			IsActive:    true,
			Balance:     decimal.NewFromFloat(0.00),
		},
		{
			MerchantID:  "cma6ptpdn0000f0lx2mp3lhob",
			Code:        "5000",
			Name:        "Cost of Goods Sold",
			Type:        models.AccountTypeExpense,
			Description: stringPtr("Direct costs of goods sold"),
			IsActive:    true,
			Balance:     decimal.NewFromFloat(0.00),
		},
		{
			MerchantID:  "cma6ptpdn0000f0lx2mp3lhob",
			Code:        "6000",
			Name:        "Operating Expenses",
			Type:        models.AccountTypeExpense,
			Description: stringPtr("General operating expenses"),
			IsActive:    true,
			Balance:     decimal.NewFromFloat(0.00),
		},
	}

	for _, account := range accounts {
		if err := db.Create(&account).Error; err != nil {
			log.Fatalf("Failed to create account %s: %v", account.Name, err)
		}
		log.Printf("Created account: %s", account.Name)
	}

	// Get customer IDs for invoices
	var customerIDs []string
	err = db.Model(&models.Customer{}).Where("merchant_id = ?", "cma6ptpdn0000f0lx2mp3lhob").Pluck("id", &customerIDs).Error
	if err != nil {
		log.Fatalf("Failed to get customer IDs: %v", err)
	}

	// Get vendor IDs for bills
	var vendorIDs []string
	err = db.Model(&models.Vendor{}).Where("merchant_id = ?", "cma6ptpdn0000f0lx2mp3lhob").Pluck("id", &vendorIDs).Error
	if err != nil {
		log.Fatalf("Failed to get vendor IDs: %v", err)
	}

	// Create invoices
	now := time.Now()
	invoices := []models.Invoice{
		{
			MerchantID:    "cma6ptpdn0000f0lx2mp3lhob",
			CustomerID:    customerIDs[0], // ABC Corporation
			InvoiceNumber: "INV-2024-001",
			IssueDate:     now.AddDate(0, 0, -30),
			DueDate:       timePtr(now.AddDate(0, 0, 0)), // Due today
			SubTotal:      decimal.NewFromFloat(45000.00),
			TaxAmount:     decimal.NewFromFloat(4500.00),
			TotalAmount:   decimal.NewFromFloat(49500.00),
			PaidAmount:    decimal.NewFromFloat(44500.00),
			Status:        "PartiallyPaid",
			Notes:         stringPtr("Monthly service contract"),
		},
		{
			MerchantID:    "cma6ptpdn0000f0lx2mp3lhob",
			CustomerID:    customerIDs[1], // XYZ Industries
			InvoiceNumber: "INV-2024-002",
			IssueDate:     now.AddDate(0, 0, -25),
			DueDate:       timePtr(now.AddDate(0, 0, -10)), // Overdue
			SubTotal:      decimal.NewFromFloat(32000.00),
			TaxAmount:     decimal.NewFromFloat(3200.00),
			TotalAmount:   decimal.NewFromFloat(35200.00),
			PaidAmount:    decimal.NewFromFloat(32000.00),
			Status:        "PartiallyPaid",
			Notes:         stringPtr("Equipment installation"),
		},
		{
			MerchantID:    "cma6ptpdn0000f0lx2mp3lhob",
			CustomerID:    customerIDs[2], // Tech Solutions Ltd
			InvoiceNumber: "INV-2024-003",
			IssueDate:     now.AddDate(0, 0, -20),
			DueDate:       timePtr(now.AddDate(0, 0, 10)), // Due in 10 days
			SubTotal:      decimal.NewFromFloat(28000.00),
			TaxAmount:     decimal.NewFromFloat(2800.00),
			TotalAmount:   decimal.NewFromFloat(30800.00),
			PaidAmount:    decimal.NewFromFloat(28000.00),
			Status:        "PartiallyPaid",
			Notes:         stringPtr("Consulting services"),
		},
		{
			MerchantID:    "cma6ptpdn0000f0lx2mp3lhob",
			CustomerID:    customerIDs[3], // Global Services Inc
			InvoiceNumber: "INV-2024-004",
			IssueDate:     now.AddDate(0, 0, -15),
			DueDate:       timePtr(now.AddDate(0, 0, 30)), // Due in 30 days
			SubTotal:      decimal.NewFromFloat(22000.00),
			TaxAmount:     decimal.NewFromFloat(2200.00),
			TotalAmount:   decimal.NewFromFloat(24200.00),
			PaidAmount:    decimal.NewFromFloat(22000.00),
			Status:        "PartiallyPaid",
			Notes:         stringPtr("Software licensing"),
		},
		{
			MerchantID:    "cma6ptpdn0000f0lx2mp3lhob",
			CustomerID:    customerIDs[4], // Innovation Partners
			InvoiceNumber: "INV-2024-005",
			IssueDate:     now.AddDate(0, 0, -10),
			DueDate:       timePtr(now.AddDate(0, 0, 20)), // Due in 20 days
			SubTotal:      decimal.NewFromFloat(18000.00),
			TaxAmount:     decimal.NewFromFloat(1800.00),
			TotalAmount:   decimal.NewFromFloat(19800.00),
			PaidAmount:    decimal.NewFromFloat(18000.00),
			Status:        "PartiallyPaid",
			Notes:         stringPtr("Training services"),
		},
	}

	for _, invoice := range invoices {
		if err := db.Create(&invoice).Error; err != nil {
			log.Fatalf("Failed to create invoice %s: %v", invoice.InvoiceNumber, err)
		}
		log.Printf("Created invoice: %s", invoice.InvoiceNumber)
	}

	// Create bills
	bills := []models.Bill{
		{
			MerchantID:  "cma6ptpdn0000f0lx2mp3lhob",
			VendorID:    vendorIDs[0], // Office Supplies Co
			BillNumber:  stringPtr("BILL-2024-001"),
			BillDate:    now.AddDate(0, 0, -25),
			DueDate:     timePtr(now.AddDate(0, 0, 5)), // Due in 5 days
			SubTotal:    decimal.NewFromFloat(5000.00),
			TaxAmount:   decimal.NewFromFloat(500.00),
			TotalAmount: decimal.NewFromFloat(5500.00),
			PaidAmount:  decimal.NewFromFloat(5000.00),
			Status:      "PartiallyPaid",
			Notes:       stringPtr("Office supplies monthly order"),
		},
		{
			MerchantID:  "cma6ptpdn0000f0lx2mp3lhob",
			VendorID:    vendorIDs[1], // Tech Equipment Ltd
			BillNumber:  stringPtr("BILL-2024-002"),
			BillDate:    now.AddDate(0, 0, -20),
			DueDate:     timePtr(now.AddDate(0, 0, -5)), // Overdue
			SubTotal:    decimal.NewFromFloat(15000.00),
			TaxAmount:   decimal.NewFromFloat(1500.00),
			TotalAmount: decimal.NewFromFloat(16500.00),
			PaidAmount:  decimal.NewFromFloat(15000.00),
			Status:      "PartiallyPaid",
			Notes:       stringPtr("Computer equipment purchase"),
		},
		{
			MerchantID:  "cma6ptpdn0000f0lx2mp3lhob",
			VendorID:    vendorIDs[2], // Utilities Provider
			BillNumber:  stringPtr("BILL-2024-003"),
			BillDate:    now.AddDate(0, 0, -15),
			DueDate:     timePtr(now.AddDate(0, 0, -5)), // Overdue
			SubTotal:    decimal.NewFromFloat(2000.00),
			TaxAmount:   decimal.NewFromFloat(200.00),
			TotalAmount: decimal.NewFromFloat(2200.00),
			PaidAmount:  decimal.NewFromFloat(2000.00),
			Status:      "PartiallyPaid",
			Notes:       stringPtr("Monthly utilities"),
		},
		{
			MerchantID:  "cma6ptpdn0000f0lx2mp3lhob",
			VendorID:    vendorIDs[3], // Marketing Agency
			BillNumber:  stringPtr("BILL-2024-004"),
			BillDate:    now.AddDate(0, 0, -10),
			DueDate:     timePtr(now.AddDate(0, 0, 20)), // Due in 20 days
			SubTotal:    decimal.NewFromFloat(8000.00),
			TaxAmount:   decimal.NewFromFloat(800.00),
			TotalAmount: decimal.NewFromFloat(8800.00),
			PaidAmount:  decimal.NewFromFloat(8000.00),
			Status:      "PartiallyPaid",
			Notes:       stringPtr("Marketing campaign"),
		},
		{
			MerchantID:  "cma6ptpdn0000f0lx2mp3lhob",
			VendorID:    vendorIDs[4], // Legal Services
			BillNumber:  stringPtr("BILL-2024-005"),
			BillDate:    now.AddDate(0, 0, -5),
			DueDate:     timePtr(now.AddDate(0, 0, 10)), // Due in 10 days
			SubTotal:    decimal.NewFromFloat(3000.00),
			TaxAmount:   decimal.NewFromFloat(300.00),
			TotalAmount: decimal.NewFromFloat(3300.00),
			PaidAmount:  decimal.NewFromFloat(3000.00),
			Status:      "PartiallyPaid",
			Notes:       stringPtr("Legal consultation"),
		},
	}

	for _, bill := range bills {
		if err := db.Create(&bill).Error; err != nil {
			billNum := "Unknown"
			if bill.BillNumber != nil {
				billNum = *bill.BillNumber
			}
			log.Fatalf("Failed to create bill %s: %v", billNum, err)
		}
		billNum := "Unknown"
		if bill.BillNumber != nil {
			billNum = *bill.BillNumber
		}
		log.Printf("Created bill: %s", billNum)
	}

	// Get an expense account ID
	var expenseAccountID string
	err = db.Model(&models.ChartOfAccount{}).Where("merchant_id = ? AND name = ?", "cma6ptpdn0000f0lx2mp3lhob", "Operating Expenses").Pluck("id", &expenseAccountID).Error
	if err != nil {
		log.Fatalf("Failed to get expense account ID: %v", err)
	}

	// Create some expenses
	expenses := []models.Expense{
		{
			MerchantID:  "cma6ptpdn0000f0lx2mp3lhob",
			AccountID:   expenseAccountID,
			Amount:      decimal.NewFromFloat(1200.00),
			Description: "Business lunch with clients",
			ExpenseDate: now.AddDate(0, 0, -5),
			Status:      "Approved",
			Notes:       stringPtr("Client meeting at restaurant"),
		},
		{
			MerchantID:  "cma6ptpdn0000f0lx2mp3lhob",
			AccountID:   expenseAccountID,
			Amount:      decimal.NewFromFloat(800.00),
			Description: "Taxi fare for business trip",
			ExpenseDate: now.AddDate(0, 0, -10),
			Status:      "Approved",
			Notes:       stringPtr("Airport to hotel transportation"),
		},
		{
			MerchantID:  "cma6ptpdn0000f0lx2mp3lhob",
			AccountID:   expenseAccountID,
			Amount:      decimal.NewFromFloat(2500.00),
			Description: "Conference registration fee",
			ExpenseDate: now.AddDate(0, 0, -15),
			Status:      "Approved",
			Notes:       stringPtr("Annual industry conference"),
		},
	}

	for _, expense := range expenses {
		if err := db.Create(&expense).Error; err != nil {
			log.Fatalf("Failed to create expense: %v", err)
		}
		log.Printf("Created expense: %s", expense.Description)
	}

	log.Println("Database seeding completed successfully!")
}
