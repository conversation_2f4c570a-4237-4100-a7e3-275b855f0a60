package main

import (
	"fmt"
	"log"
	"regexp"
	"strings"

	"adc-account-backend/internal/config"
	"adc-account-backend/internal/models"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

// generateSlug creates a URL-friendly slug from a name
func generateSlug(name string) string {
	// Convert to lowercase
	slug := strings.ToLower(name)

	// Remove special characters except spaces and hyphens
	reg := regexp.MustCompile(`[^a-z0-9\s-]`)
	slug = reg.ReplaceAllString(slug, "")

	// Replace spaces with hyphens
	slug = strings.ReplaceAll(slug, " ", "-")

	// Replace multiple hyphens with single hyphen
	reg = regexp.MustCompile(`-+`)
	slug = reg.ReplaceAllString(slug, "-")

	// Trim hyphens from start and end
	slug = strings.Trim(slug, "-")

	return slug
}

// ensureUniqueSlug ensures the slug is unique by appending a counter if needed
func ensureUniqueSlug(db *gorm.DB, baseSlug string, excludeID string) string {
	slug := baseSlug
	counter := 1

	for {
		var count int64
		query := db.Model(&models.Organization{}).Where("slug = ?", slug)
		if excludeID != "" {
			query = query.Where("id != ?", excludeID)
		}
		query.Count(&count)

		if count == 0 {
			return slug
		}

		slug = fmt.Sprintf("%s-%d", baseSlug, counter)
		counter++
	}
}

func main() {
	// Load configuration
	cfg := config.Load()

	// Connect to database directly without auto-migration
	db, err := gorm.Open(postgres.Open(cfg.DatabaseURL), &gorm.Config{})
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Add slug column to organizations table
	fmt.Println("Adding slug column to organizations table...")
	if err := db.Exec("ALTER TABLE organizations ADD COLUMN IF NOT EXISTS slug VARCHAR(255)").Error; err != nil {
		log.Fatalf("Failed to add slug column: %v", err)
	}

	// Get all organizations without slugs
	var organizations []models.Organization
	if err := db.Where("slug IS NULL OR slug = ''").Find(&organizations).Error; err != nil {
		log.Fatalf("Failed to fetch organizations: %v", err)
	}

	fmt.Printf("Found %d organizations without slugs\n", len(organizations))

	// Generate slugs for existing organizations
	for _, org := range organizations {
		baseSlug := generateSlug(org.Name)
		uniqueSlug := ensureUniqueSlug(db, baseSlug, org.ID)

		if err := db.Model(&org).Update("slug", uniqueSlug).Error; err != nil {
			log.Printf("Failed to update slug for organization %s: %v", org.Name, err)
			continue
		}

		fmt.Printf("Generated slug '%s' for organization '%s'\n", uniqueSlug, org.Name)
	}

	// Add unique constraint to slug column
	fmt.Println("Adding unique constraint to slug column...")
	if err := db.Exec("ALTER TABLE organizations ADD CONSTRAINT organizations_slug_key UNIQUE (slug)").Error; err != nil {
		// If constraint already exists, that's fine
		if !strings.Contains(err.Error(), "already exists") {
			log.Printf("Warning: Failed to add unique constraint: %v", err)
		}
	}

	// Make slug column NOT NULL
	fmt.Println("Making slug column NOT NULL...")
	if err := db.Exec("ALTER TABLE organizations ALTER COLUMN slug SET NOT NULL").Error; err != nil {
		log.Fatalf("Failed to make slug column NOT NULL: %v", err)
	}

	fmt.Println("Migration completed successfully!")
}
