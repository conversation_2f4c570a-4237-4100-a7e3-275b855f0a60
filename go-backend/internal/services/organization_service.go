package services

import (
	"errors"

	"adc-account-backend/internal/models"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type OrganizationService struct {
	db *gorm.DB
}

func NewOrganizationService(db *gorm.DB) *OrganizationService {
	return &OrganizationService{db: db}
}

// CreateOrganizationRequest represents a request to create an organization
type CreateOrganizationRequest struct {
	Name            string  `json:"name" binding:"required"`
	Description     *string `json:"description"`
	Address         *string `json:"address"`
	Phone           *string `json:"phone"`
	Email           *string `json:"email"`
	Website         *string `json:"website"`
	TaxID           *string `json:"taxId"`
	LogoURL         *string `json:"logoUrl"`
	Currency        string  `json:"currency"`
	FiscalYearStart *string `json:"fiscalYearStart"`
	LegalName       *string `json:"legalName"`
}

// UpdateOrganizationRequest represents a request to update an organization
type UpdateOrganizationRequest struct {
	Name            *string `json:"name"`
	Description     *string `json:"description"`
	Address         *string `json:"address"`
	Phone           *string `json:"phone"`
	Email           *string `json:"email"`
	Website         *string `json:"website"`
	TaxID           *string `json:"taxId"`
	LogoURL         *string `json:"logoUrl"`
	Currency        *string `json:"currency"`
	FiscalYearStart *string `json:"fiscalYearStart"`
	LegalName       *string `json:"legalName"`
	IsActive        *bool   `json:"isActive"`
}

// OrganizationResponse represents an organization response
type OrganizationResponse struct {
	ID              string                              `json:"id"`
	Name            string                              `json:"name"`
	Slug            string                              `json:"slug"`
	Description     *string                             `json:"description"`
	Address         *string                             `json:"address"`
	Phone           *string                             `json:"phone"`
	Email           *string                             `json:"email"`
	Website         *string                             `json:"website"`
	TaxID           *string                             `json:"taxId"`
	LogoURL         *string                             `json:"logoUrl"`
	IsActive        bool                                `json:"isActive"`
	CreatedAt       string                              `json:"createdAt"`
	UpdatedAt       string                              `json:"updatedAt"`
	Currency        string                              `json:"currency"`
	FiscalYearStart *string                             `json:"fiscalYearStart"`
	LegalName       *string                             `json:"legalName"`
	Branches        []models.Branch                     `json:"branches,omitempty"`
	UserPermissions []models.UserOrganizationPermission `json:"userPermissions,omitempty"`
}

// GetAllOrganizations returns all organizations with pagination
func (s *OrganizationService) GetAllOrganizations(page, limit int, userID string) ([]OrganizationResponse, int64, error) {
	var organizations []models.Organization
	var total int64

	// Build query to get organizations user has access to
	query := s.db.Model(&models.Organization{}).
		Joins("LEFT JOIN user_organization_permissions ON organizations.id = user_organization_permissions.organization_id").
		Where("user_organization_permissions.user_id = ? OR organizations.id IN (SELECT organization_id FROM user_organization_permissions WHERE user_id = ?)", userID, userID)

	// Count total organizations
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get organizations with pagination
	offset := (page - 1) * limit
	if err := query.Preload("Branches").
		Preload("UserPermissions").
		Offset(offset).Limit(limit).Find(&organizations).Error; err != nil {
		return nil, 0, err
	}

	// Convert to response format
	var responses []OrganizationResponse
	for _, org := range organizations {
		responses = append(responses, s.toOrganizationResponse(org))
	}

	return responses, total, nil
}

// GetOrganizationByID returns an organization by ID
func (s *OrganizationService) GetOrganizationByID(id, userID string) (*OrganizationResponse, error) {
	var organization models.Organization

	// Check if user has access to this organization
	if !s.hasOrganizationAccess(userID, id) {
		return nil, errors.New("access denied to this organization")
	}

	if err := s.db.Preload("Branches").
		Preload("UserPermissions").
		Where("id = ?", id).First(&organization).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("organization not found")
		}
		return nil, err
	}

	response := s.toOrganizationResponse(organization)
	return &response, nil
}

// GetOrganizationBySlug returns an organization by slug
func (s *OrganizationService) GetOrganizationBySlug(slug string, userID string) (*OrganizationResponse, error) {
	var organization models.Organization

	// Check if user has access to this organization
	err := s.db.Joins("JOIN user_organization_permissions ON organizations.id = user_organization_permissions.organization_id").
		Where("organizations.slug = ? AND user_organization_permissions.user_id = ?", slug, userID).
		Preload("Branches").
		Preload("UserPermissions").
		First(&organization).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New("organization not found")
		}
		return nil, err
	}

	response := s.toOrganizationResponse(organization)
	return &response, nil
}

// CreateOrganization creates a new organization
func (s *OrganizationService) CreateOrganization(req CreateOrganizationRequest, userID string) (*OrganizationResponse, error) {
	// Set defaults
	currency := req.Currency
	if currency == "" {
		currency = "USD"
	}

	// Create organization
	organization := models.Organization{
		ID:              uuid.New().String(),
		Name:            req.Name,
		Slug:            "", // Will be auto-generated in BeforeCreate hook
		Description:     req.Description,
		Address:         req.Address,
		Phone:           req.Phone,
		Email:           req.Email,
		Website:         req.Website,
		TaxID:           req.TaxID,
		LogoURL:         req.LogoURL,
		IsActive:        true,
		Currency:        currency,
		FiscalYearStart: req.FiscalYearStart,
		LegalName:       req.LegalName,
	}

	// Start transaction
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Create organization
	if err := tx.Create(&organization).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// Create owner permission for the user
	permission := models.UserOrganizationPermission{
		ID:              uuid.New().String(),
		UserID:          userID,
		OrganizationID:  organization.ID,
		PermissionLevel: models.PermissionOwner,
	}

	if err := tx.Create(&permission).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return nil, err
	}

	// Reload organization with relationships
	if err := s.db.Preload("Branches").
		Preload("UserPermissions").
		Where("id = ?", organization.ID).First(&organization).Error; err != nil {
		return nil, err
	}

	response := s.toOrganizationResponse(organization)
	return &response, nil
}

// UpdateOrganization updates an existing organization
func (s *OrganizationService) UpdateOrganization(id string, req UpdateOrganizationRequest, userID string) (*OrganizationResponse, error) {
	// Check if user has admin access to this organization
	if !s.hasOrganizationAdminAccess(userID, id) {
		return nil, errors.New("insufficient permissions to update this organization")
	}

	var organization models.Organization
	if err := s.db.Where("id = ?", id).First(&organization).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("organization not found")
		}
		return nil, err
	}

	// Update fields
	updates := make(map[string]interface{})
	if req.Name != nil {
		updates["name"] = *req.Name
	}
	if req.Description != nil {
		updates["description"] = *req.Description
	}
	if req.Address != nil {
		updates["address"] = *req.Address
	}
	if req.Phone != nil {
		updates["phone"] = *req.Phone
	}
	if req.Email != nil {
		updates["email"] = *req.Email
	}
	if req.Website != nil {
		updates["website"] = *req.Website
	}
	if req.TaxID != nil {
		updates["tax_id"] = *req.TaxID
	}
	if req.LogoURL != nil {
		updates["logo_url"] = *req.LogoURL
	}
	if req.Currency != nil {
		updates["currency"] = *req.Currency
	}
	if req.FiscalYearStart != nil {
		updates["fiscal_year_start"] = *req.FiscalYearStart
	}
	if req.LegalName != nil {
		updates["legal_name"] = *req.LegalName
	}
	if req.IsActive != nil {
		updates["is_active"] = *req.IsActive
	}

	if len(updates) > 0 {
		if err := s.db.Model(&organization).Updates(updates).Error; err != nil {
			return nil, err
		}
	}

	// Reload organization with relationships
	if err := s.db.Preload("Branches").
		Preload("UserPermissions").
		Where("id = ?", id).First(&organization).Error; err != nil {
		return nil, err
	}

	response := s.toOrganizationResponse(organization)
	return &response, nil
}

// DeleteOrganization soft deletes an organization
func (s *OrganizationService) DeleteOrganization(id, userID string) error {
	// Check if user has owner access to this organization
	if !s.hasOrganizationOwnerAccess(userID, id) {
		return errors.New("insufficient permissions to delete this organization")
	}

	var organization models.Organization
	if err := s.db.Where("id = ?", id).First(&organization).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("organization not found")
		}
		return err
	}

	// Soft delete by setting is_active to false
	return s.db.Model(&organization).Update("is_active", false).Error
}

// AddUserToOrganization adds a user to an organization with specified permissions
func (s *OrganizationService) AddUserToOrganization(organizationID, userID, targetUserID string, permissionLevel models.PermissionLevel) error {
	// Check if user has admin access to this organization
	if !s.hasOrganizationAdminAccess(userID, organizationID) {
		return errors.New("insufficient permissions to add users to this organization")
	}

	// Check if user already has permission
	var existingPermission models.UserOrganizationPermission
	if err := s.db.Where("user_id = ? AND organization_id = ?", targetUserID, organizationID).First(&existingPermission).Error; err == nil {
		return errors.New("user already has access to this organization")
	}

	// Create permission
	permission := models.UserOrganizationPermission{
		ID:              uuid.New().String(),
		UserID:          targetUserID,
		OrganizationID:  organizationID,
		PermissionLevel: permissionLevel,
	}

	return s.db.Create(&permission).Error
}

// RemoveUserFromOrganization removes a user from an organization
func (s *OrganizationService) RemoveUserFromOrganization(organizationID, userID, targetUserID string) error {
	// Check if user has admin access to this organization
	if !s.hasOrganizationAdminAccess(userID, organizationID) {
		return errors.New("insufficient permissions to remove users from this organization")
	}

	// Cannot remove yourself if you're the only owner
	if userID == targetUserID {
		var ownerCount int64
		s.db.Model(&models.UserOrganizationPermission{}).
			Where("organization_id = ? AND permission_level = ?", organizationID, models.PermissionOwner).
			Count(&ownerCount)

		if ownerCount <= 1 {
			return errors.New("cannot remove the last owner from the organization")
		}
	}

	return s.db.Where("user_id = ? AND organization_id = ?", targetUserID, organizationID).
		Delete(&models.UserOrganizationPermission{}).Error
}

// Helper methods
func (s *OrganizationService) hasOrganizationAccess(userID, organizationID string) bool {
	var count int64
	s.db.Model(&models.UserOrganizationPermission{}).
		Where("user_id = ? AND organization_id = ?", userID, organizationID).
		Count(&count)
	return count > 0
}

func (s *OrganizationService) hasOrganizationAdminAccess(userID, organizationID string) bool {
	var count int64
	s.db.Model(&models.UserOrganizationPermission{}).
		Where("user_id = ? AND organization_id = ? AND permission_level IN ?",
			userID, organizationID, []models.PermissionLevel{models.PermissionOwner, models.PermissionAdmin}).
		Count(&count)
	return count > 0
}

func (s *OrganizationService) hasOrganizationOwnerAccess(userID, organizationID string) bool {
	var count int64
	s.db.Model(&models.UserOrganizationPermission{}).
		Where("user_id = ? AND organization_id = ? AND permission_level = ?",
			userID, organizationID, models.PermissionOwner).
		Count(&count)
	return count > 0
}

// toOrganizationResponse converts an Organization model to OrganizationResponse
func (s *OrganizationService) toOrganizationResponse(org models.Organization) OrganizationResponse {
	return OrganizationResponse{
		ID:              org.ID,
		Name:            org.Name,
		Slug:            org.Slug,
		Description:     org.Description,
		Address:         org.Address,
		Phone:           org.Phone,
		Email:           org.Email,
		Website:         org.Website,
		TaxID:           org.TaxID,
		LogoURL:         org.LogoURL,
		IsActive:        org.IsActive,
		CreatedAt:       org.CreatedAt.Format("2006-01-02T15:04:05Z"),
		UpdatedAt:       org.UpdatedAt.Format("2006-01-02T15:04:05Z"),
		Currency:        org.Currency,
		FiscalYearStart: org.FiscalYearStart,
		LegalName:       org.LegalName,
		Branches:        org.Branches,
		UserPermissions: org.UserPermissions,
	}
}
