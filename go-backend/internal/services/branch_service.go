package services

import (
	"errors"

	"adc-account-backend/internal/models"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type BranchService struct {
	db *gorm.DB
}

func NewBranchService(db *gorm.DB) *BranchService {
	return &BranchService{db: db}
}

// CreateBranchRequest represents a request to create a branch
type CreateBranchRequest struct {
	OrganizationID string  `json:"organizationId" binding:"required"`
	Name           string  `json:"name" binding:"required"`
	Description    *string `json:"description"`
	Address        *string `json:"address"`
	Phone          *string `json:"phone"`
	Email          *string `json:"email"`
	ManagerName    *string `json:"managerName"`
	BranchCode     *string `json:"branchCode"`
}

// CreateBranchBySlugRequest represents a request to create a branch using organization slug
type CreateBranchBySlugRequest struct {
	Name        string  `json:"name" binding:"required"`
	Description *string `json:"description"`
	Address     *string `json:"address"`
	Phone       *string `json:"phone"`
	Email       *string `json:"email"`
	ManagerName *string `json:"managerName"`
	BranchCode  *string `json:"branchCode"`
}

// UpdateBranchRequest represents a request to update a branch
type UpdateBranchRequest struct {
	Name        *string `json:"name"`
	Description *string `json:"description"`
	Address     *string `json:"address"`
	Phone       *string `json:"phone"`
	Email       *string `json:"email"`
	ManagerName *string `json:"managerName"`
	BranchCode  *string `json:"branchCode"`
	IsActive    *bool   `json:"isActive"`
}

// BranchResponse represents a branch response
type BranchResponse struct {
	ID              string                        `json:"id"`
	OrganizationID  string                        `json:"organizationId"`
	Name            string                        `json:"name"`
	Description     *string                       `json:"description"`
	Address         *string                       `json:"address"`
	Phone           *string                       `json:"phone"`
	Email           *string                       `json:"email"`
	IsActive        bool                          `json:"isActive"`
	CreatedAt       string                        `json:"createdAt"`
	UpdatedAt       string                        `json:"updatedAt"`
	ManagerName     *string                       `json:"managerName"`
	BranchCode      *string                       `json:"branchCode"`
	Organization    *models.Organization          `json:"organization,omitempty"`
	UserPermissions []models.UserBranchPermission `json:"userPermissions,omitempty"`
}

// GetAllBranches returns all branches with pagination
func (s *BranchService) GetAllBranches(page, limit int, userID string) ([]BranchResponse, int64, error) {
	var branches []models.Branch
	var total int64

	// Build query to get branches from organizations user has access to
	query := s.db.Model(&models.Branch{}).
		Joins("JOIN user_organization_permissions ON branches.organization_id = user_organization_permissions.organization_id").
		Where("user_organization_permissions.user_id = ?", userID)

	// Count total branches
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get branches with pagination
	offset := (page - 1) * limit
	if err := query.Preload("Organization").
		Preload("UserPermissions").
		Offset(offset).Limit(limit).Find(&branches).Error; err != nil {
		return nil, 0, err
	}

	// Convert to response format
	var responses []BranchResponse
	for _, branch := range branches {
		responses = append(responses, s.toBranchResponse(branch))
	}

	return responses, total, nil
}

// GetBranchesByOrganization returns branches for a specific organization
func (s *BranchService) GetBranchesByOrganization(organizationID string, page, limit int, userID string) ([]BranchResponse, int64, error) {
	// Check if user has access to this organization
	if !s.hasOrganizationAccess(userID, organizationID) {
		return nil, 0, errors.New("access denied to this organization")
	}

	var branches []models.Branch
	var total int64

	// Count total branches for this organization
	if err := s.db.Model(&models.Branch{}).Where("organization_id = ? AND is_active = ?", organizationID, true).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get branches with pagination
	offset := (page - 1) * limit
	if err := s.db.Where("organization_id = ? AND is_active = ?", organizationID, true).
		Preload("Organization").
		Preload("UserPermissions").
		Offset(offset).Limit(limit).Find(&branches).Error; err != nil {
		return nil, 0, err
	}

	// Convert to response format
	var responses []BranchResponse
	for _, branch := range branches {
		responses = append(responses, s.toBranchResponse(branch))
	}

	return responses, total, nil
}

// GetBranchByID returns a branch by ID
func (s *BranchService) GetBranchByID(id, userID string) (*BranchResponse, error) {
	var branch models.Branch

	// Get branch and check access through organization
	if err := s.db.Joins("JOIN user_organization_permissions ON branches.organization_id = user_organization_permissions.organization_id").
		Where("branches.id = ? AND user_organization_permissions.user_id = ?", id, userID).
		Preload("Organization").
		Preload("UserPermissions").
		First(&branch).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("branch not found or access denied")
		}
		return nil, err
	}

	response := s.toBranchResponse(branch)
	return &response, nil
}

// CreateBranch creates a new branch
func (s *BranchService) CreateBranch(req CreateBranchRequest, userID string) (*BranchResponse, error) {
	// Validate that OrganizationID is provided
	if req.OrganizationID == "" {
		return nil, errors.New("organization ID is required")
	}

	// Check if user has admin access to this organization
	if !s.hasOrganizationAdminAccess(userID, req.OrganizationID) {
		return nil, errors.New("insufficient permissions to create branches in this organization")
	}

	// Verify organization exists
	var organization models.Organization
	if err := s.db.Where("id = ? AND is_active = ?", req.OrganizationID, true).First(&organization).Error; err != nil {
		return nil, errors.New("organization not found or inactive")
	}

	// Check if branch code is unique within the organization
	if req.BranchCode != nil {
		var existingBranch models.Branch
		if err := s.db.Where("organization_id = ? AND branch_code = ?", req.OrganizationID, *req.BranchCode).First(&existingBranch).Error; err == nil {
			return nil, errors.New("branch code already exists in this organization")
		}
	}

	// Create branch
	branch := models.Branch{
		ID:             uuid.New().String(),
		OrganizationID: req.OrganizationID,
		Name:           req.Name,
		Description:    req.Description,
		Address:        req.Address,
		Phone:          req.Phone,
		Email:          req.Email,
		IsActive:       true,
		ManagerName:    req.ManagerName,
		BranchCode:     req.BranchCode,
	}

	// Start transaction
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Create branch
	if err := tx.Create(&branch).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// Create admin permission for the user
	permission := models.UserBranchPermission{
		ID:              uuid.New().String(),
		UserID:          userID,
		BranchID:        branch.ID,
		PermissionLevel: models.PermissionAdmin,
	}

	if err := tx.Create(&permission).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return nil, err
	}

	// Reload branch with relationships
	if err := s.db.Preload("Organization").
		Preload("UserPermissions").
		Where("id = ?", branch.ID).First(&branch).Error; err != nil {
		return nil, err
	}

	response := s.toBranchResponse(branch)
	return &response, nil
}

// CreateBranchBySlug creates a new branch using organization slug
func (s *BranchService) CreateBranchBySlug(req CreateBranchBySlugRequest, organizationID, userID string) (*BranchResponse, error) {
	// Convert to regular CreateBranchRequest
	createReq := CreateBranchRequest{
		OrganizationID: organizationID,
		Name:           req.Name,
		Description:    req.Description,
		Address:        req.Address,
		Phone:          req.Phone,
		Email:          req.Email,
		ManagerName:    req.ManagerName,
		BranchCode:     req.BranchCode,
	}

	return s.CreateBranch(createReq, userID)
}

// UpdateBranch updates an existing branch
func (s *BranchService) UpdateBranch(id string, req UpdateBranchRequest, userID string) (*BranchResponse, error) {
	var branch models.Branch

	// Get branch and check access through organization
	if err := s.db.Joins("JOIN user_organization_permissions ON branches.organization_id = user_organization_permissions.organization_id").
		Where("branches.id = ? AND user_organization_permissions.user_id = ? AND user_organization_permissions.permission_level IN ?",
			id, userID, []models.PermissionLevel{models.PermissionOwner, models.PermissionAdmin}).
		First(&branch).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("branch not found or insufficient permissions")
		}
		return nil, err
	}

	// Check if branch code is being changed and if it conflicts
	if req.BranchCode != nil && *req.BranchCode != "" {
		var existingBranch models.Branch
		if err := s.db.Where("organization_id = ? AND branch_code = ? AND id != ?", branch.OrganizationID, *req.BranchCode, id).First(&existingBranch).Error; err == nil {
			return nil, errors.New("branch code already exists in this organization")
		}
	}

	// Update fields
	updates := make(map[string]interface{})
	if req.Name != nil {
		updates["name"] = *req.Name
	}
	if req.Description != nil {
		updates["description"] = *req.Description
	}
	if req.Address != nil {
		updates["address"] = *req.Address
	}
	if req.Phone != nil {
		updates["phone"] = *req.Phone
	}
	if req.Email != nil {
		updates["email"] = *req.Email
	}
	if req.ManagerName != nil {
		updates["manager_name"] = *req.ManagerName
	}
	if req.BranchCode != nil {
		updates["branch_code"] = *req.BranchCode
	}
	if req.IsActive != nil {
		updates["is_active"] = *req.IsActive
	}

	if len(updates) > 0 {
		if err := s.db.Model(&branch).Updates(updates).Error; err != nil {
			return nil, err
		}
	}

	// Reload branch with relationships
	if err := s.db.Preload("Organization").
		Preload("UserPermissions").
		Where("id = ?", id).First(&branch).Error; err != nil {
		return nil, err
	}

	response := s.toBranchResponse(branch)
	return &response, nil
}

// DeleteBranch soft deletes a branch
func (s *BranchService) DeleteBranch(id, userID string) error {
	var branch models.Branch

	// Get branch and check access through organization
	if err := s.db.Joins("JOIN user_organization_permissions ON branches.organization_id = user_organization_permissions.organization_id").
		Where("branches.id = ? AND user_organization_permissions.user_id = ? AND user_organization_permissions.permission_level IN ?",
			id, userID, []models.PermissionLevel{models.PermissionOwner, models.PermissionAdmin}).
		First(&branch).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("branch not found or insufficient permissions")
		}
		return err
	}

	// Soft delete by setting is_active to false
	return s.db.Model(&branch).Update("is_active", false).Error
}

// AddUserToBranch adds a user to a branch with specified permissions
func (s *BranchService) AddUserToBranch(branchID, userID, targetUserID string, permissionLevel models.PermissionLevel) error {
	// Check if user has admin access to this branch's organization
	var branch models.Branch
	if err := s.db.Where("id = ?", branchID).First(&branch).Error; err != nil {
		return errors.New("branch not found")
	}

	if !s.hasOrganizationAdminAccess(userID, branch.OrganizationID) {
		return errors.New("insufficient permissions to add users to this branch")
	}

	// Check if user already has permission
	var existingPermission models.UserBranchPermission
	if err := s.db.Where("user_id = ? AND branch_id = ?", targetUserID, branchID).First(&existingPermission).Error; err == nil {
		return errors.New("user already has access to this branch")
	}

	// Create permission
	permission := models.UserBranchPermission{
		ID:              uuid.New().String(),
		UserID:          targetUserID,
		BranchID:        branchID,
		PermissionLevel: permissionLevel,
	}

	return s.db.Create(&permission).Error
}

// RemoveUserFromBranch removes a user from a branch
func (s *BranchService) RemoveUserFromBranch(branchID, userID, targetUserID string) error {
	// Check if user has admin access to this branch's organization
	var branch models.Branch
	if err := s.db.Where("id = ?", branchID).First(&branch).Error; err != nil {
		return errors.New("branch not found")
	}

	if !s.hasOrganizationAdminAccess(userID, branch.OrganizationID) {
		return errors.New("insufficient permissions to remove users from this branch")
	}

	return s.db.Where("user_id = ? AND branch_id = ?", targetUserID, branchID).
		Delete(&models.UserBranchPermission{}).Error
}

// Helper methods
func (s *BranchService) hasOrganizationAccess(userID, organizationID string) bool {
	var count int64
	s.db.Model(&models.UserOrganizationPermission{}).
		Where("user_id = ? AND organization_id = ?", userID, organizationID).
		Count(&count)
	return count > 0
}

func (s *BranchService) hasOrganizationAdminAccess(userID, organizationID string) bool {
	var count int64
	s.db.Model(&models.UserOrganizationPermission{}).
		Where("user_id = ? AND organization_id = ? AND permission_level IN ?",
			userID, organizationID, []models.PermissionLevel{models.PermissionOwner, models.PermissionAdmin}).
		Count(&count)
	return count > 0
}

// toBranchResponse converts a Branch model to BranchResponse
func (s *BranchService) toBranchResponse(branch models.Branch) BranchResponse {
	return BranchResponse{
		ID:              branch.ID,
		OrganizationID:  branch.OrganizationID,
		Name:            branch.Name,
		Description:     branch.Description,
		Address:         branch.Address,
		Phone:           branch.Phone,
		Email:           branch.Email,
		IsActive:        branch.IsActive,
		CreatedAt:       branch.CreatedAt.Format("2006-01-02T15:04:05Z"),
		UpdatedAt:       branch.UpdatedAt.Format("2006-01-02T15:04:05Z"),
		ManagerName:     branch.ManagerName,
		BranchCode:      branch.BranchCode,
		Organization:    &branch.Organization,
		UserPermissions: branch.UserPermissions,
	}
}
