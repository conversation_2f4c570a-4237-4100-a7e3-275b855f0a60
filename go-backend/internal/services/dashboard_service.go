package services

import (
	"fmt"
	"time"

	"adc-account-backend/internal/models"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type DashboardService struct {
	db *gorm.DB
}

func NewDashboardService(db *gorm.DB) *DashboardService {
	return &DashboardService{db: db}
}

// FinancialSummary represents financial overview data
type FinancialSummary struct {
	TotalRevenue       float64 `json:"total_revenue"`
	TotalExpenses      float64 `json:"total_expenses"`
	NetIncome          float64 `json:"net_income"`
	AccountsReceivable float64 `json:"accounts_receivable"`
	AccountsPayable    float64 `json:"accounts_payable"`
	CashBalance        float64 `json:"cash_balance"`
}

// AccountsReceivableSummary represents AR aging data
type AccountsReceivableSummary struct {
	Current    float64 `json:"current"`
	Days1To30  float64 `json:"days_1_30"`
	Days31To60 float64 `json:"days_31_60"`
	Days61To90 float64 `json:"days_61_90"`
	DaysOver90 float64 `json:"days_over_90"`
	Total      float64 `json:"total"`
}

// AccountsPayableSummary represents AP aging data
type AccountsPayableSummary struct {
	Current    float64 `json:"current"`
	Days1To30  float64 `json:"days_1_30"`
	Days31To60 float64 `json:"days_31_60"`
	Days61To90 float64 `json:"days_61_90"`
	DaysOver90 float64 `json:"days_over_90"`
	Total      float64 `json:"total"`
}

// TopCustomer represents top customer data
type TopCustomer struct {
	ID           string  `json:"id"`
	Name         string  `json:"name"`
	TotalSales   float64 `json:"total_sales"`
	OpenInvoices float64 `json:"open_invoices"`
}

// TopVendor represents top vendor data
type TopVendor struct {
	ID             string  `json:"id"`
	Name           string  `json:"name"`
	TotalPurchases float64 `json:"total_purchases"`
	OpenBills      float64 `json:"open_bills"`
}

// RecentTransaction represents recent transaction data
type RecentTransaction struct {
	ID          string    `json:"id"`
	Date        time.Time `json:"date"`
	Description string    `json:"description"`
	Amount      float64   `json:"amount"`
	Type        string    `json:"type"`
}

// CashFlowData represents cash flow chart data
type CashFlowData struct {
	Labels   []string  `json:"labels"`
	Income   []float64 `json:"income"`
	Expenses []float64 `json:"expenses"`
}

// GetFinancialSummary returns financial overview for a merchant
func (s *DashboardService) GetFinancialSummary(merchantID, startDate, endDate string) (*FinancialSummary, error) {
	// Get cash balance from chart of accounts
	var cashAccount models.ChartOfAccount
	err := s.db.Where("merchant_id = ? AND code = ?", merchantID, "1000").First(&cashAccount).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get cash account: %w", err)
	}

	// Get accounts receivable balance
	var arAccount models.ChartOfAccount
	err = s.db.Where("merchant_id = ? AND code = ?", merchantID, "1200").First(&arAccount).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get AR account: %w", err)
	}

	// Get accounts payable balance
	var apAccount models.ChartOfAccount
	err = s.db.Where("merchant_id = ? AND code = ?", merchantID, "2000").First(&apAccount).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get AP account: %w", err)
	}

	// Calculate total revenue from invoices
	var totalRevenue decimal.Decimal
	err = s.db.Model(&models.Invoice{}).
		Where("merchant_id = ? AND status IN (?)", merchantID, []string{"Paid", "Partially Paid"}).
		Select("COALESCE(SUM(paid_amount), 0)").
		Scan(&totalRevenue).Error
	if err != nil {
		return nil, fmt.Errorf("failed to calculate total revenue: %w", err)
	}

	// Calculate total expenses from bills and expenses
	var totalExpenses decimal.Decimal
	err = s.db.Model(&models.Bill{}).
		Where("merchant_id = ? AND status IN (?)", merchantID, []string{"Paid", "Partially Paid"}).
		Select("COALESCE(SUM(paid_amount), 0)").
		Scan(&totalExpenses).Error
	if err != nil {
		return nil, fmt.Errorf("failed to calculate total expenses: %w", err)
	}

	// Add expenses from expense records
	var expenseAmount decimal.Decimal
	err = s.db.Model(&models.Expense{}).
		Where("merchant_id = ?", merchantID).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&expenseAmount).Error
	if err != nil {
		return nil, fmt.Errorf("failed to calculate expense amount: %w", err)
	}

	totalExpenses = totalExpenses.Add(expenseAmount)

	// Convert decimals to float64 for response
	revenueFloat, _ := totalRevenue.Float64()
	expensesFloat, _ := totalExpenses.Float64()
	cashFloat, _ := cashAccount.Balance.Float64()
	arFloat, _ := arAccount.Balance.Float64()
	apFloat, _ := apAccount.Balance.Float64()

	summary := &FinancialSummary{
		TotalRevenue:       revenueFloat,
		TotalExpenses:      expensesFloat,
		NetIncome:          revenueFloat - expensesFloat,
		AccountsReceivable: arFloat,
		AccountsPayable:    apFloat,
		CashBalance:        cashFloat,
	}

	return summary, nil
}

// GetAccountsReceivableSummary returns AR aging summary
func (s *DashboardService) GetAccountsReceivableSummary(merchantID, asOfDate string) (*AccountsReceivableSummary, error) {
	now := time.Now()

	// Calculate aging buckets
	var current, days1To30, days31To60, days61To90, daysOver90 decimal.Decimal

	// Current (0-30 days)
	err := s.db.Model(&models.Invoice{}).
		Where("merchant_id = ? AND status IN (?) AND due_date >= ?",
			merchantID, []string{"Sent", "Partially Paid"}, now.AddDate(0, 0, -30)).
		Select("COALESCE(SUM(total_amount - paid_amount), 0)").
		Scan(&current).Error
	if err != nil {
		return nil, fmt.Errorf("failed to calculate current AR: %w", err)
	}

	// 1-30 days past due
	err = s.db.Model(&models.Invoice{}).
		Where("merchant_id = ? AND status IN (?) AND due_date < ? AND due_date >= ?",
			merchantID, []string{"Sent", "Partially Paid"}, now.AddDate(0, 0, -30), now.AddDate(0, 0, -60)).
		Select("COALESCE(SUM(total_amount - paid_amount), 0)").
		Scan(&days1To30).Error
	if err != nil {
		return nil, fmt.Errorf("failed to calculate 1-30 days AR: %w", err)
	}

	// 31-60 days past due
	err = s.db.Model(&models.Invoice{}).
		Where("merchant_id = ? AND status IN (?) AND due_date < ? AND due_date >= ?",
			merchantID, []string{"Sent", "Partially Paid"}, now.AddDate(0, 0, -60), now.AddDate(0, 0, -90)).
		Select("COALESCE(SUM(total_amount - paid_amount), 0)").
		Scan(&days31To60).Error
	if err != nil {
		return nil, fmt.Errorf("failed to calculate 31-60 days AR: %w", err)
	}

	// 61-90 days past due
	err = s.db.Model(&models.Invoice{}).
		Where("merchant_id = ? AND status IN (?) AND due_date < ? AND due_date >= ?",
			merchantID, []string{"Sent", "Partially Paid"}, now.AddDate(0, 0, -90), now.AddDate(0, 0, -120)).
		Select("COALESCE(SUM(total_amount - paid_amount), 0)").
		Scan(&days61To90).Error
	if err != nil {
		return nil, fmt.Errorf("failed to calculate 61-90 days AR: %w", err)
	}

	// Over 90 days past due
	err = s.db.Model(&models.Invoice{}).
		Where("merchant_id = ? AND status IN (?) AND due_date < ?",
			merchantID, []string{"Sent", "Partially Paid"}, now.AddDate(0, 0, -120)).
		Select("COALESCE(SUM(total_amount - paid_amount), 0)").
		Scan(&daysOver90).Error
	if err != nil {
		return nil, fmt.Errorf("failed to calculate over 90 days AR: %w", err)
	}

	// Convert to float64
	currentFloat, _ := current.Float64()
	days1To30Float, _ := days1To30.Float64()
	days31To60Float, _ := days31To60.Float64()
	days61To90Float, _ := days61To90.Float64()
	daysOver90Float, _ := daysOver90.Float64()

	total := currentFloat + days1To30Float + days31To60Float + days61To90Float + daysOver90Float

	summary := &AccountsReceivableSummary{
		Current:    currentFloat,
		Days1To30:  days1To30Float,
		Days31To60: days31To60Float,
		Days61To90: days61To90Float,
		DaysOver90: daysOver90Float,
		Total:      total,
	}

	return summary, nil
}

// GetAccountsPayableSummary returns AP aging summary
func (s *DashboardService) GetAccountsPayableSummary(merchantID, asOfDate string) (*AccountsPayableSummary, error) {
	now := time.Now()

	// Calculate aging buckets for bills
	var current, days1To30, days31To60, days61To90, daysOver90 decimal.Decimal

	// Current (0-30 days)
	err := s.db.Model(&models.Bill{}).
		Where("merchant_id = ? AND status IN (?) AND due_date >= ?",
			merchantID, []string{"Received", "Partially Paid"}, now.AddDate(0, 0, -30)).
		Select("COALESCE(SUM(total_amount - paid_amount), 0)").
		Scan(&current).Error
	if err != nil {
		return nil, fmt.Errorf("failed to calculate current AP: %w", err)
	}

	// 1-30 days past due
	err = s.db.Model(&models.Bill{}).
		Where("merchant_id = ? AND status IN (?) AND due_date < ? AND due_date >= ?",
			merchantID, []string{"Received", "Partially Paid"}, now.AddDate(0, 0, -30), now.AddDate(0, 0, -60)).
		Select("COALESCE(SUM(total_amount - paid_amount), 0)").
		Scan(&days1To30).Error
	if err != nil {
		return nil, fmt.Errorf("failed to calculate 1-30 days AP: %w", err)
	}

	// 31-60 days past due
	err = s.db.Model(&models.Bill{}).
		Where("merchant_id = ? AND status IN (?) AND due_date < ? AND due_date >= ?",
			merchantID, []string{"Received", "Partially Paid"}, now.AddDate(0, 0, -60), now.AddDate(0, 0, -90)).
		Select("COALESCE(SUM(total_amount - paid_amount), 0)").
		Scan(&days31To60).Error
	if err != nil {
		return nil, fmt.Errorf("failed to calculate 31-60 days AP: %w", err)
	}

	// 61-90 days past due
	err = s.db.Model(&models.Bill{}).
		Where("merchant_id = ? AND status IN (?) AND due_date < ? AND due_date >= ?",
			merchantID, []string{"Received", "Partially Paid"}, now.AddDate(0, 0, -90), now.AddDate(0, 0, -120)).
		Select("COALESCE(SUM(total_amount - paid_amount), 0)").
		Scan(&days61To90).Error
	if err != nil {
		return nil, fmt.Errorf("failed to calculate 61-90 days AP: %w", err)
	}

	// Over 90 days past due
	err = s.db.Model(&models.Bill{}).
		Where("merchant_id = ? AND status IN (?) AND due_date < ?",
			merchantID, []string{"Received", "Partially Paid"}, now.AddDate(0, 0, -120)).
		Select("COALESCE(SUM(total_amount - paid_amount), 0)").
		Scan(&daysOver90).Error
	if err != nil {
		return nil, fmt.Errorf("failed to calculate over 90 days AP: %w", err)
	}

	// Convert to float64
	currentFloat, _ := current.Float64()
	days1To30Float, _ := days1To30.Float64()
	days31To60Float, _ := days31To60.Float64()
	days61To90Float, _ := days61To90.Float64()
	daysOver90Float, _ := daysOver90.Float64()

	total := currentFloat + days1To30Float + days31To60Float + days61To90Float + daysOver90Float

	summary := &AccountsPayableSummary{
		Current:    currentFloat,
		Days1To30:  days1To30Float,
		Days31To60: days31To60Float,
		Days61To90: days61To90Float,
		DaysOver90: daysOver90Float,
		Total:      total,
	}

	return summary, nil
}

// GetTopCustomers returns top customers by sales
func (s *DashboardService) GetTopCustomers(merchantID string, limit int, period string) ([]TopCustomer, error) {
	var customers []TopCustomer

	query := `
		SELECT
			c.id,
			c.name,
			COALESCE(SUM(i.paid_amount), 0) as total_sales,
			COALESCE(SUM(i.total_amount - i.paid_amount), 0) as open_invoices
		FROM customers c
		LEFT JOIN invoices i ON c.id = i.customer_id AND i.merchant_id = ?
		WHERE c.merchant_id = ?
		GROUP BY c.id, c.name
		ORDER BY total_sales DESC
		LIMIT ?
	`

	err := s.db.Raw(query, merchantID, merchantID, limit).Scan(&customers).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get top customers: %w", err)
	}

	return customers, nil
}

// GetTopVendors returns top vendors by purchases
func (s *DashboardService) GetTopVendors(merchantID string, limit int, period string) ([]TopVendor, error) {
	var vendors []TopVendor

	query := `
		SELECT
			v.id,
			v.name,
			COALESCE(SUM(b.paid_amount), 0) as total_purchases,
			COALESCE(SUM(b.total_amount - b.paid_amount), 0) as open_bills
		FROM vendors v
		LEFT JOIN bills b ON v.id = b.vendor_id AND b.merchant_id = ?
		WHERE v.merchant_id = ?
		GROUP BY v.id, v.name
		ORDER BY total_purchases DESC
		LIMIT ?
	`

	err := s.db.Raw(query, merchantID, merchantID, limit).Scan(&vendors).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get top vendors: %w", err)
	}

	return vendors, nil
}

// GetRecentTransactions returns recent transactions
func (s *DashboardService) GetRecentTransactions(merchantID string, limit int) ([]RecentTransaction, error) {
	var transactions []RecentTransaction

	// Get recent invoice payments
	var invoiceTransactions []RecentTransaction
	invoiceQuery := `
		SELECT
			i.id,
			i.updated_at as date,
			CONCAT('Payment from ', c.name) as description,
			i.paid_amount as amount,
			'income' as type
		FROM invoices i
		JOIN customers c ON i.customer_id = c.id
		WHERE i.merchant_id = ? AND i.paid_amount > 0
		ORDER BY i.updated_at DESC
		LIMIT ?
	`

	err := s.db.Raw(invoiceQuery, merchantID, limit/2).Scan(&invoiceTransactions).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get invoice transactions: %w", err)
	}

	// Get recent bill payments
	var billTransactions []RecentTransaction
	billQuery := `
		SELECT
			b.id,
			b.updated_at as date,
			CONCAT('Payment to ', v.name) as description,
			-b.paid_amount as amount,
			'expense' as type
		FROM bills b
		JOIN vendors v ON b.vendor_id = v.id
		WHERE b.merchant_id = ? AND b.paid_amount > 0
		ORDER BY b.updated_at DESC
		LIMIT ?
	`

	err = s.db.Raw(billQuery, merchantID, limit/2).Scan(&billTransactions).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get bill transactions: %w", err)
	}

	// Combine and sort transactions
	transactions = append(transactions, invoiceTransactions...)
	transactions = append(transactions, billTransactions...)

	// Sort by date descending
	for i := 0; i < len(transactions)-1; i++ {
		for j := i + 1; j < len(transactions); j++ {
			if transactions[i].Date.Before(transactions[j].Date) {
				transactions[i], transactions[j] = transactions[j], transactions[i]
			}
		}
	}

	// Limit results
	if len(transactions) > limit {
		transactions = transactions[:limit]
	}

	return transactions, nil
}

// GetCashFlow returns cash flow data for charts
func (s *DashboardService) GetCashFlow(merchantID, period string) (*CashFlowData, error) {
	// Get last 6 months of data
	var labels []string
	var income []float64
	var expenses []float64

	now := time.Now()
	for i := 5; i >= 0; i-- {
		monthStart := time.Date(now.Year(), now.Month()-time.Month(i), 1, 0, 0, 0, 0, now.Location())
		monthEnd := monthStart.AddDate(0, 1, 0).Add(-time.Second)

		labels = append(labels, monthStart.Format("Jan"))

		// Calculate income for the month
		var monthlyIncome decimal.Decimal
		err := s.db.Model(&models.Invoice{}).
			Where("merchant_id = ? AND updated_at >= ? AND updated_at <= ? AND paid_amount > 0",
				merchantID, monthStart, monthEnd).
			Select("COALESCE(SUM(paid_amount), 0)").
			Scan(&monthlyIncome).Error
		if err != nil {
			return nil, fmt.Errorf("failed to calculate monthly income: %w", err)
		}

		// Calculate expenses for the month
		var monthlyExpenses decimal.Decimal
		err = s.db.Model(&models.Bill{}).
			Where("merchant_id = ? AND updated_at >= ? AND updated_at <= ? AND paid_amount > 0",
				merchantID, monthStart, monthEnd).
			Select("COALESCE(SUM(paid_amount), 0)").
			Scan(&monthlyExpenses).Error
		if err != nil {
			return nil, fmt.Errorf("failed to calculate monthly expenses: %w", err)
		}

		// Add expense records
		var monthlyExpenseRecords decimal.Decimal
		err = s.db.Model(&models.Expense{}).
			Where("merchant_id = ? AND created_at >= ? AND created_at <= ?",
				merchantID, monthStart, monthEnd).
			Select("COALESCE(SUM(amount), 0)").
			Scan(&monthlyExpenseRecords).Error
		if err != nil {
			return nil, fmt.Errorf("failed to calculate monthly expense records: %w", err)
		}

		monthlyExpenses = monthlyExpenses.Add(monthlyExpenseRecords)

		incomeFloat, _ := monthlyIncome.Float64()
		expensesFloat, _ := monthlyExpenses.Float64()

		income = append(income, incomeFloat)
		expenses = append(expenses, expensesFloat)
	}

	cashFlow := &CashFlowData{
		Labels:   labels,
		Income:   income,
		Expenses: expenses,
	}

	return cashFlow, nil
}
