package models

import (
	"regexp"
	"strings"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Helper function to generate slug from name
func generateSlug(name string) string {
	// Convert to lowercase
	slug := strings.ToLower(name)

	// Remove special characters except spaces and hyphens
	reg := regexp.MustCompile(`[^a-z0-9\s-]`)
	slug = reg.ReplaceAllString(slug, "")

	// Replace spaces with hyphens
	slug = strings.ReplaceAll(slug, " ", "-")

	// Replace multiple hyphens with single hyphen
	reg = regexp.MustCompile(`-+`)
	slug = reg.ReplaceAllString(slug, "-")

	// Trim hyphens from start and end
	slug = strings.Trim(slug, "-")

	return slug
}

// Enums
type PermissionLevel string

const (
	PermissionOwner    PermissionLevel = "Owner"
	PermissionAdmin    PermissionLevel = "Admin"
	PermissionManager  PermissionLevel = "Manager"
	PermissionStaff    PermissionLevel = "Staff"
	PermissionReadOnly PermissionLevel = "ReadOnly"
)

// User represents the users table
type User struct {
	ID                    string     `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	Name                  *string    `json:"name" gorm:"type:varchar(255)"`
	Email                 string     `json:"email" gorm:"type:varchar(255);uniqueIndex;not null"`
	PasswordHash          *string    `json:"-" gorm:"type:varchar(255)"`
	CreatedAt             time.Time  `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt             time.Time  `json:"updatedAt" gorm:"autoUpdateTime"`
	EmailVerified         *time.Time `json:"emailVerified"`
	Image                 *string    `json:"image" gorm:"type:text"`
	IsActive              bool       `json:"isActive" gorm:"default:true"`
	Role                  string     `json:"role" gorm:"type:varchar(50);default:'staff'"`
	PersonalMerchantID    *string    `json:"personalMerchantId" gorm:"type:varchar(36);uniqueIndex"`
	DefaultOrganizationID *string    `json:"defaultOrganizationId" gorm:"type:varchar(36)"`

	// Relationships
	Accounts                []Account                    `json:"accounts" gorm:"foreignKey:UserID"`
	CustodianAssets         []Asset                      `json:"custodianAssets" gorm:"foreignKey:CustodianID"`
	AuditLogs               []AuditLog                   `json:"auditLogs" gorm:"foreignKey:UserID"`
	JournalEntries          []JournalEntry               `json:"journalEntries" gorm:"foreignKey:CreatedByID"`
	Sessions                []Session                    `json:"sessions" gorm:"foreignKey:UserID"`
	MerchantPermissions     []UserMerchantPermission     `json:"merchantPermissions" gorm:"foreignKey:UserID"`
	OrganizationPermissions []UserOrganizationPermission `json:"organizationPermissions" gorm:"foreignKey:UserID"`
	BranchPermissions       []UserBranchPermission       `json:"branchPermissions" gorm:"foreignKey:UserID"`
	Preferences             *UserPreferences             `json:"preferences" gorm:"foreignKey:UserID"`
	Merchants               []Merchant                   `json:"merchants" gorm:"many2many:user_merchants"`
	PersonalMerchant        *Merchant                    `json:"personalMerchant" gorm:"foreignKey:PersonalMerchantID"`
	DefaultOrganization     *Organization                `json:"defaultOrganization" gorm:"foreignKey:DefaultOrganizationID"`
}

// Organization represents the organizations table
type Organization struct {
	ID              string    `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	Name            string    `json:"name" gorm:"type:varchar(255);not null;index"`
	Slug            string    `json:"slug" gorm:"type:varchar(255);not null;uniqueIndex"`
	Description     *string   `json:"description" gorm:"type:text"`
	Address         *string   `json:"address" gorm:"type:text"`
	Phone           *string   `json:"phone" gorm:"type:varchar(50)"`
	Email           *string   `json:"email" gorm:"type:varchar(255)"`
	Website         *string   `json:"website" gorm:"type:varchar(255)"`
	TaxID           *string   `json:"taxId" gorm:"type:varchar(100)"`
	LogoURL         *string   `json:"logoUrl" gorm:"type:text"`
	IsActive        bool      `json:"isActive" gorm:"default:true;index"`
	CreatedAt       time.Time `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt       time.Time `json:"updatedAt" gorm:"autoUpdateTime"`
	Currency        string    `json:"currency" gorm:"type:varchar(10);default:'USD'"`
	FiscalYearStart *string   `json:"fiscalYearStart" gorm:"type:varchar(10)"`
	LegalName       *string   `json:"legalName" gorm:"type:varchar(255)"`

	// Relationships
	Branches        []Branch                     `json:"branches" gorm:"foreignKey:OrganizationID"`
	UserPermissions []UserOrganizationPermission `json:"userPermissions" gorm:"foreignKey:OrganizationID"`
	DefaultUsers    []User                       `json:"defaultUsers" gorm:"foreignKey:DefaultOrganizationID"`
}

// Branch represents the branches table
type Branch struct {
	ID             string    `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	OrganizationID string    `json:"organizationId" gorm:"type:varchar(36);not null;index"`
	Name           string    `json:"name" gorm:"type:varchar(255);not null"`
	Description    *string   `json:"description" gorm:"type:text"`
	Address        *string   `json:"address" gorm:"type:text"`
	Phone          *string   `json:"phone" gorm:"type:varchar(50)"`
	Email          *string   `json:"email" gorm:"type:varchar(255)"`
	IsActive       bool      `json:"isActive" gorm:"default:true;index"`
	CreatedAt      time.Time `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt      time.Time `json:"updatedAt" gorm:"autoUpdateTime"`
	ManagerName    *string   `json:"managerName" gorm:"type:varchar(255)"`
	BranchCode     *string   `json:"branchCode" gorm:"type:varchar(50);uniqueIndex:idx_org_branch_code"`

	// Relationships
	Organization    Organization           `json:"organization" gorm:"foreignKey:OrganizationID"`
	UserPermissions []UserBranchPermission `json:"userPermissions" gorm:"foreignKey:BranchID"`
}

// UserOrganizationPermission represents user permissions for organizations
type UserOrganizationPermission struct {
	ID                string          `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	UserID            string          `json:"userId" gorm:"type:varchar(36);not null;index;uniqueIndex:idx_user_org"`
	OrganizationID    string          `json:"organizationId" gorm:"type:varchar(36);not null;index;uniqueIndex:idx_user_org"`
	PermissionLevel   PermissionLevel `json:"permissionLevel" gorm:"type:varchar(50);default:'Staff'"`
	CustomPermissions *string         `json:"customPermissions" gorm:"type:jsonb"`
	CreatedAt         time.Time       `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt         time.Time       `json:"updatedAt" gorm:"autoUpdateTime"`

	// Relationships
	User         User         `json:"user" gorm:"foreignKey:UserID"`
	Organization Organization `json:"organization" gorm:"foreignKey:OrganizationID"`
}

// UserBranchPermission represents user permissions for branches
type UserBranchPermission struct {
	ID                string          `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	UserID            string          `json:"userId" gorm:"type:varchar(36);not null;index;uniqueIndex:idx_user_branch"`
	BranchID          string          `json:"branchId" gorm:"type:varchar(36);not null;index;uniqueIndex:idx_user_branch"`
	PermissionLevel   PermissionLevel `json:"permissionLevel" gorm:"type:varchar(50);default:'Staff'"`
	CustomPermissions *string         `json:"customPermissions" gorm:"type:jsonb"`
	CreatedAt         time.Time       `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt         time.Time       `json:"updatedAt" gorm:"autoUpdateTime"`

	// Relationships
	User   User   `json:"user" gorm:"foreignKey:UserID"`
	Branch Branch `json:"branch" gorm:"foreignKey:BranchID"`
}

// UserMerchantPermission represents user permissions for merchants
type UserMerchantPermission struct {
	ID                string          `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	UserID            string          `json:"userId" gorm:"type:varchar(36);not null;index;uniqueIndex:idx_user_merchant"`
	MerchantID        string          `json:"merchantId" gorm:"type:varchar(36);not null;index;uniqueIndex:idx_user_merchant"`
	PermissionLevel   PermissionLevel `json:"permissionLevel" gorm:"type:varchar(50);default:'Staff'"`
	CustomPermissions *string         `json:"customPermissions" gorm:"type:jsonb"`
	CreatedAt         time.Time       `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt         time.Time       `json:"updatedAt" gorm:"autoUpdateTime"`

	// Relationships
	User     User     `json:"user" gorm:"foreignKey:UserID"`
	Merchant Merchant `json:"merchant" gorm:"foreignKey:MerchantID"`
}

// UserPreferences represents user preferences
type UserPreferences struct {
	ID                string    `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	UserID            string    `json:"userId" gorm:"type:varchar(36);not null;uniqueIndex"`
	Theme             string    `json:"theme" gorm:"type:varchar(50);default:'system'"`
	DateFormat        *string   `json:"dateFormat" gorm:"type:varchar(50)"`
	TimeFormat        *string   `json:"timeFormat" gorm:"type:varchar(50)"`
	Language          *string   `json:"language" gorm:"type:varchar(10)"`
	StartPage         *string   `json:"startPage" gorm:"type:varchar(255)"`
	Notifications     *string   `json:"notifications" gorm:"type:jsonb"`
	CreatedAt         time.Time `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt         time.Time `json:"updatedAt" gorm:"autoUpdateTime"`
	CurrencyFormat    *string   `json:"currencyFormat" gorm:"type:varchar(50)"`
	DisplayDensity    *string   `json:"displayDensity" gorm:"type:varchar(50);default:'comfortable'"`
	NumberFormat      *string   `json:"numberFormat" gorm:"type:varchar(50)"`
	TablePageSize     *int      `json:"tablePageSize" gorm:"default:10"`
	DefaultMerchantID *string   `json:"defaultMerchantId" gorm:"type:varchar(36)"`
	Bio               *string   `json:"bio" gorm:"type:text"`
	Department        *string   `json:"department" gorm:"type:varchar(255)"`
	JobTitle          *string   `json:"jobTitle" gorm:"type:varchar(255)"`
	Phone             *string   `json:"phone" gorm:"type:varchar(50)"`

	// Relationships
	User User `json:"user" gorm:"foreignKey:UserID"`
}

// BeforeCreate hook for User
func (u *User) BeforeCreate(tx *gorm.DB) error {
	if u.ID == "" {
		u.ID = uuid.New().String()
	}
	return nil
}

// BeforeCreate hook for Organization
func (o *Organization) BeforeCreate(tx *gorm.DB) error {
	if o.ID == "" {
		o.ID = uuid.New().String()
	}

	// Generate slug if not provided
	if o.Slug == "" {
		o.Slug = generateSlug(o.Name)

		// Ensure slug is unique
		counter := 1
		originalSlug := o.Slug
		for {
			var existingOrg Organization
			if err := tx.Where("slug = ?", o.Slug).First(&existingOrg).Error; err != nil {
				if err == gorm.ErrRecordNotFound {
					break // Slug is unique
				}
				return err // Database error
			}
			// Slug exists, try with counter
			o.Slug = originalSlug + "-" + strings.Trim(strings.Replace(uuid.New().String(), "-", "", 4)[:8], "-")
			counter++
		}
	}

	return nil
}

// BeforeCreate hook for Branch
func (b *Branch) BeforeCreate(tx *gorm.DB) error {
	if b.ID == "" {
		b.ID = uuid.New().String()
	}
	return nil
}
