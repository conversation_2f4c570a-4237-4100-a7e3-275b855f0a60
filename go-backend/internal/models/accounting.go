package models

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// Accounting Enums
type AccountType string
type JournalEntrySourceType string
type JournalEntryLineType string

const (
	AccountTypeAsset     AccountType = "Asset"
	AccountTypeLiability AccountType = "Liability"
	AccountTypeEquity    AccountType = "Equity"
	AccountTypeRevenue   AccountType = "Revenue"
	AccountTypeExpense   AccountType = "Expense"

	JournalEntrySourceManual         JournalEntrySourceType = "Manual"
	JournalEntrySourceSalesOrder     JournalEntrySourceType = "SalesOrder"
	JournalEntrySourceBill           JournalEntrySourceType = "Bill"
	JournalEntrySourceBillPayment    JournalEntrySourceType = "BillPayment"
	JournalEntrySourceDepreciation   JournalEntrySourceType = "Depreciation"
	JournalEntrySourceAssetPurchase  JournalEntrySourceType = "AssetPurchase"
	JournalEntrySourceAssetDisposal  JournalEntrySourceType = "AssetDisposal"
	JournalEntrySourcePayrollRun     JournalEntrySourceType = "PayrollRun"
	JournalEntrySourceBankAdjustment JournalEntrySourceType = "BankAdjustment"
	JournalEntrySourceInvoice        JournalEntrySourceType = "Invoice"
	JournalEntrySourceInvoicePayment JournalEntrySourceType = "InvoicePayment"
	JournalEntrySourceCreditNote     JournalEntrySourceType = "CreditNote"

	JournalEntryLineTypeDebit  JournalEntryLineType = "Debit"
	JournalEntryLineTypeCredit JournalEntryLineType = "Credit"
)

// ChartOfAccount represents the chart_of_accounts table
type ChartOfAccount struct {
	ID          string          `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	MerchantID  string          `json:"merchantId" gorm:"type:varchar(36);not null;index"`
	Code        string          `json:"code" gorm:"type:varchar(50);not null;uniqueIndex:idx_merchant_account_code"`
	Name        string          `json:"name" gorm:"type:varchar(255);not null"`
	Type        AccountType     `json:"type" gorm:"type:varchar(50);not null;index"`
	ParentID    *string         `json:"parentId" gorm:"type:varchar(36);index"`
	Description *string         `json:"description" gorm:"type:text"`
	IsActive    bool            `json:"isActive" gorm:"default:true;index"`
	CreatedAt   time.Time       `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt   time.Time       `json:"updatedAt" gorm:"autoUpdateTime"`
	Balance     decimal.Decimal `json:"balance" gorm:"type:decimal(15,2);default:0"`
	IsSystem    bool            `json:"isSystem" gorm:"default:false"`

	// Relationships
	Merchant     Merchant           `json:"merchant" gorm:"foreignKey:MerchantID"`
	Parent       *ChartOfAccount    `json:"parent" gorm:"foreignKey:ParentID"`
	Children     []ChartOfAccount   `json:"children" gorm:"foreignKey:ParentID"`
	JournalLines []JournalEntryLine `json:"journalLines" gorm:"foreignKey:AccountID"`
	Expenses     []Expense          `json:"expenses" gorm:"foreignKey:AccountID"`
	BankAccounts []BankAccount      `json:"bankAccounts" gorm:"foreignKey:ChartOfAccountID"`
}

// JournalEntry represents the journal_entries table
type JournalEntry struct {
	ID           string                 `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	MerchantID   string                 `json:"merchantId" gorm:"type:varchar(36);not null;index"`
	EntryNumber  string                 `json:"entryNumber" gorm:"type:varchar(100);not null;uniqueIndex:idx_merchant_entry_number"`
	Date         time.Time              `json:"date" gorm:"not null;index"`
	Description  string                 `json:"description" gorm:"type:text;not null"`
	Reference    *string                `json:"reference" gorm:"type:varchar(255)"`
	SourceType   JournalEntrySourceType `json:"sourceType" gorm:"type:varchar(50);not null;index"`
	SourceID     *string                `json:"sourceId" gorm:"type:varchar(36);index"`
	TotalAmount  decimal.Decimal        `json:"totalAmount" gorm:"type:decimal(15,2);not null;default:0"`
	CreatedByID  string                 `json:"createdById" gorm:"type:varchar(36);not null;index"`
	CreatedAt    time.Time              `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt    time.Time              `json:"updatedAt" gorm:"autoUpdateTime"`
	IsReversed   bool                   `json:"isReversed" gorm:"default:false;index"`
	ReversedByID *string                `json:"reversedById" gorm:"type:varchar(36)"`
	ReversedAt   *time.Time             `json:"reversedAt"`

	// Relationships
	Merchant   Merchant           `json:"merchant" gorm:"foreignKey:MerchantID"`
	CreatedBy  User               `json:"createdBy" gorm:"foreignKey:CreatedByID"`
	ReversedBy *User              `json:"reversedBy" gorm:"foreignKey:ReversedByID"`
	Lines      []JournalEntryLine `json:"lines" gorm:"foreignKey:JournalEntryID"`
}

// JournalEntryLine represents the journal_entry_lines table
type JournalEntryLine struct {
	ID             string               `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	JournalEntryID string               `json:"journalEntryId" gorm:"type:varchar(36);not null;index"`
	AccountID      string               `json:"accountId" gorm:"type:varchar(36);not null;index"`
	Type           JournalEntryLineType `json:"type" gorm:"type:varchar(50);not null"`
	Amount         decimal.Decimal      `json:"amount" gorm:"type:decimal(15,2);not null"`
	Description    *string              `json:"description" gorm:"type:text"`
	SortOrder      int                  `json:"sortOrder" gorm:"default:0"`

	// Relationships
	JournalEntry JournalEntry   `json:"journalEntry" gorm:"foreignKey:JournalEntryID"`
	Account      ChartOfAccount `json:"account" gorm:"foreignKey:AccountID"`
}

// TaxRate represents the tax_rates table
type TaxRate struct {
	ID          string          `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	MerchantID  string          `json:"merchantId" gorm:"type:varchar(36);not null;index"`
	Name        string          `json:"name" gorm:"type:varchar(255);not null"`
	Rate        decimal.Decimal `json:"rate" gorm:"type:decimal(5,4);not null"`
	Description *string         `json:"description" gorm:"type:text"`
	IsActive    bool            `json:"isActive" gorm:"default:true;index"`
	CreatedAt   time.Time       `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt   time.Time       `json:"updatedAt" gorm:"autoUpdateTime"`
	IsDefault   bool            `json:"isDefault" gorm:"default:false"`

	// Relationships
	Merchant     Merchant      `json:"merchant" gorm:"foreignKey:MerchantID"`
	InvoiceItems []InvoiceItem `json:"invoiceItems" gorm:"foreignKey:TaxRateID"`
	BillItems    []BillItem    `json:"billItems" gorm:"foreignKey:TaxRateID"`
	Expenses     []Expense     `json:"expenses" gorm:"foreignKey:TaxRateID"`
}

// BeforeCreate hooks
func (c *ChartOfAccount) BeforeCreate(tx *gorm.DB) error {
	if c.ID == "" {
		c.ID = uuid.New().String()
	}
	return nil
}

func (j *JournalEntry) BeforeCreate(tx *gorm.DB) error {
	if j.ID == "" {
		j.ID = uuid.New().String()
	}
	return nil
}

func (j *JournalEntryLine) BeforeCreate(tx *gorm.DB) error {
	if j.ID == "" {
		j.ID = uuid.New().String()
	}
	return nil
}

func (t *TaxRate) BeforeCreate(tx *gorm.DB) error {
	if t.ID == "" {
		t.ID = uuid.New().String()
	}
	return nil
}
