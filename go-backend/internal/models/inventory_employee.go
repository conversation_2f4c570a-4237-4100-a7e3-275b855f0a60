package models

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// InventoryItem represents the inventory_items table
type InventoryItem struct {
	ID             string              `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	MerchantID     string              `json:"merchantId" gorm:"type:varchar(36);not null;index"`
	Name           string              `json:"name" gorm:"type:varchar(255);not null"`
	Description    *string             `json:"description" gorm:"type:text"`
	SKU            *string             `json:"sku" gorm:"type:varchar(100);uniqueIndex:idx_merchant_sku"`
	Barcode        *string             `json:"barcode" gorm:"type:varchar(100)"`
	Category       *string             `json:"category" gorm:"type:varchar(100)"`
	UnitOfMeasure  string              `json:"unitOfMeasure" gorm:"type:varchar(50);default:'each'"`
	UnitCost       decimal.Decimal     `json:"unitCost" gorm:"type:decimal(15,2);default:0"`
	UnitPrice      decimal.Decimal     `json:"unitPrice" gorm:"type:decimal(15,2);default:0"`
	QuantityOnHand decimal.Decimal     `json:"quantityOnHand" gorm:"type:decimal(10,2);default:0"`
	ReorderLevel   decimal.Decimal     `json:"reorderLevel" gorm:"type:decimal(10,2);default:0"`
	MaxStockLevel  *decimal.Decimal    `json:"maxStockLevel" gorm:"type:decimal(10,2)"`
	Status         InventoryItemStatus `json:"status" gorm:"type:varchar(50);default:'Active';index"`
	Location       *string             `json:"location" gorm:"type:varchar(255)"`
	Notes          *string             `json:"notes" gorm:"type:text"`
	CreatedAt      time.Time           `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt      time.Time           `json:"updatedAt" gorm:"autoUpdateTime"`
	LastStockDate  *time.Time          `json:"lastStockDate"`
	Supplier       *string             `json:"supplier" gorm:"type:varchar(255)"`
	SupplierSKU    *string             `json:"supplierSku" gorm:"type:varchar(100)"`

	// Relationships
	Merchant     Merchant               `json:"merchant" gorm:"foreignKey:MerchantID"`
	Transactions []InventoryTransaction `json:"transactions" gorm:"foreignKey:InventoryItemID"`
}

// InventoryTransaction represents the inventory_transactions table
type InventoryTransaction struct {
	ID              string                   `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	MerchantID      string                   `json:"merchantId" gorm:"type:varchar(36);not null;index"`
	InventoryItemID string                   `json:"inventoryItemId" gorm:"type:varchar(36);not null;index"`
	Type            InventoryTransactionType `json:"type" gorm:"type:varchar(50);not null;index"`
	Quantity        decimal.Decimal          `json:"quantity" gorm:"type:decimal(10,2);not null"`
	UnitCost        decimal.Decimal          `json:"unitCost" gorm:"type:decimal(15,2);default:0"`
	TotalCost       decimal.Decimal          `json:"totalCost" gorm:"type:decimal(15,2);default:0"`
	Date            time.Time                `json:"date" gorm:"not null;index"`
	Reference       *string                  `json:"reference" gorm:"type:varchar(255)"`
	Notes           *string                  `json:"notes" gorm:"type:text"`
	CreatedAt       time.Time                `json:"createdAt" gorm:"autoCreateTime"`
	SourceType      *string                  `json:"sourceType" gorm:"type:varchar(50)"`
	SourceID        *string                  `json:"sourceId" gorm:"type:varchar(36)"`

	// Relationships
	Merchant      Merchant      `json:"merchant" gorm:"foreignKey:MerchantID"`
	InventoryItem InventoryItem `json:"inventoryItem" gorm:"foreignKey:InventoryItemID"`
}

// Employee represents the employees table
type Employee struct {
	ID               string           `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	MerchantID       string           `json:"merchantId" gorm:"type:varchar(36);not null;index"`
	EmployeeNumber   *string          `json:"employeeNumber" gorm:"type:varchar(100)"`
	FirstName        string           `json:"firstName" gorm:"type:varchar(255);not null"`
	LastName         string           `json:"lastName" gorm:"type:varchar(255);not null"`
	Email            *string          `json:"email" gorm:"type:varchar(255)"`
	Phone            *string          `json:"phone" gorm:"type:varchar(50)"`
	Address          *string          `json:"address" gorm:"type:text"`
	City             *string          `json:"city" gorm:"type:varchar(100)"`
	State            *string          `json:"state" gorm:"type:varchar(100)"`
	PostalCode       *string          `json:"postalCode" gorm:"type:varchar(20)"`
	Country          *string          `json:"country" gorm:"type:varchar(100)"`
	DateOfBirth      *time.Time       `json:"dateOfBirth"`
	HireDate         time.Time        `json:"hireDate" gorm:"not null;index"`
	TerminationDate  *time.Time       `json:"terminationDate"`
	JobTitle         *string          `json:"jobTitle" gorm:"type:varchar(255)"`
	Department       *string          `json:"department" gorm:"type:varchar(255)"`
	Salary           decimal.Decimal  `json:"salary" gorm:"type:decimal(15,2);default:0"`
	HourlyRate       *decimal.Decimal `json:"hourlyRate" gorm:"type:decimal(10,2)"`
	PayFrequency     *string          `json:"payFrequency" gorm:"type:varchar(50)"`
	Status           EmploymentStatus `json:"status" gorm:"type:varchar(50);default:'Active';index"`
	TaxID            *string          `json:"taxId" gorm:"type:varchar(100)"`
	BankAccount      *string          `json:"bankAccount" gorm:"type:varchar(100)"`
	RoutingNumber    *string          `json:"routingNumber" gorm:"type:varchar(50)"`
	EmergencyContact *string          `json:"emergencyContact" gorm:"type:text"`
	Notes            *string          `json:"notes" gorm:"type:text"`
	CreatedAt        time.Time        `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt        time.Time        `json:"updatedAt" gorm:"autoUpdateTime"`

	// Relationships
	Merchant       Merchant        `json:"merchant" gorm:"foreignKey:MerchantID"`
	PayrollDetails []PayrollDetail `json:"payrollDetails" gorm:"foreignKey:EmployeeID"`
}

// PayrollRun represents the payroll_runs table
type PayrollRun struct {
	ID              string           `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	MerchantID      string           `json:"merchantId" gorm:"type:varchar(36);not null;index"`
	RunNumber       string           `json:"runNumber" gorm:"type:varchar(100);not null;uniqueIndex:idx_merchant_payroll_run"`
	PayPeriodStart  time.Time        `json:"payPeriodStart" gorm:"not null"`
	PayPeriodEnd    time.Time        `json:"payPeriodEnd" gorm:"not null"`
	PayDate         time.Time        `json:"payDate" gorm:"not null;index"`
	Status          PayrollRunStatus `json:"status" gorm:"type:varchar(50);default:'Draft';index"`
	TotalGross      decimal.Decimal  `json:"totalGross" gorm:"type:decimal(15,2);default:0"`
	TotalNet        decimal.Decimal  `json:"totalNet" gorm:"type:decimal(15,2);default:0"`
	TotalTaxes      decimal.Decimal  `json:"totalTaxes" gorm:"type:decimal(15,2);default:0"`
	TotalDeductions decimal.Decimal  `json:"totalDeductions" gorm:"type:decimal(15,2);default:0"`
	Notes           *string          `json:"notes" gorm:"type:text"`
	CreatedAt       time.Time        `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt       time.Time        `json:"updatedAt" gorm:"autoUpdateTime"`
	ProcessedAt     *time.Time       `json:"processedAt"`

	// Relationships
	Merchant Merchant        `json:"merchant" gorm:"foreignKey:MerchantID"`
	Details  []PayrollDetail `json:"details" gorm:"foreignKey:PayrollRunID"`
}

// PayrollDetail represents the payroll_details table
type PayrollDetail struct {
	ID             string           `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	MerchantID     string           `json:"merchantId" gorm:"type:varchar(36);not null;index"`
	PayrollRunID   string           `json:"payrollRunId" gorm:"type:varchar(36);not null;index"`
	EmployeeID     string           `json:"employeeId" gorm:"type:varchar(36);not null;index"`
	GrossPay       decimal.Decimal  `json:"grossPay" gorm:"type:decimal(15,2);not null;default:0"`
	NetPay         decimal.Decimal  `json:"netPay" gorm:"type:decimal(15,2);not null;default:0"`
	FederalTax     decimal.Decimal  `json:"federalTax" gorm:"type:decimal(15,2);default:0"`
	StateTax       decimal.Decimal  `json:"stateTax" gorm:"type:decimal(15,2);default:0"`
	SocialSecurity decimal.Decimal  `json:"socialSecurity" gorm:"type:decimal(15,2);default:0"`
	Medicare       decimal.Decimal  `json:"medicare" gorm:"type:decimal(15,2);default:0"`
	Unemployment   decimal.Decimal  `json:"unemployment" gorm:"type:decimal(15,2);default:0"`
	OtherTaxes     decimal.Decimal  `json:"otherTaxes" gorm:"type:decimal(15,2);default:0"`
	Deductions     decimal.Decimal  `json:"deductions" gorm:"type:decimal(15,2);default:0"`
	HoursWorked    *decimal.Decimal `json:"hoursWorked" gorm:"type:decimal(8,2)"`
	OvertimeHours  *decimal.Decimal `json:"overtimeHours" gorm:"type:decimal(8,2)"`
	RegularPay     decimal.Decimal  `json:"regularPay" gorm:"type:decimal(15,2);default:0"`
	OvertimePay    decimal.Decimal  `json:"overtimePay" gorm:"type:decimal(15,2);default:0"`
	BonusPay       decimal.Decimal  `json:"bonusPay" gorm:"type:decimal(15,2);default:0"`
	CommissionPay  decimal.Decimal  `json:"commissionPay" gorm:"type:decimal(15,2);default:0"`
	Notes          *string          `json:"notes" gorm:"type:text"`
	CreatedAt      time.Time        `json:"createdAt" gorm:"autoCreateTime"`

	// Relationships
	Merchant   Merchant   `json:"merchant" gorm:"foreignKey:MerchantID"`
	PayrollRun PayrollRun `json:"payrollRun" gorm:"foreignKey:PayrollRunID"`
	Employee   Employee   `json:"employee" gorm:"foreignKey:EmployeeID"`
}

// BeforeCreate hooks
func (i *InventoryItem) BeforeCreate(tx *gorm.DB) error {
	if i.ID == "" {
		i.ID = uuid.New().String()
	}
	return nil
}

func (i *InventoryTransaction) BeforeCreate(tx *gorm.DB) error {
	if i.ID == "" {
		i.ID = uuid.New().String()
	}
	return nil
}

func (e *Employee) BeforeCreate(tx *gorm.DB) error {
	if e.ID == "" {
		e.ID = uuid.New().String()
	}
	return nil
}

func (p *PayrollRun) BeforeCreate(tx *gorm.DB) error {
	if p.ID == "" {
		p.ID = uuid.New().String()
	}
	return nil
}

func (p *PayrollDetail) BeforeCreate(tx *gorm.DB) error {
	if p.ID == "" {
		p.ID = uuid.New().String()
	}
	return nil
}
