package models

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// CreditNote represents the credit_notes table
type CreditNote struct {
	ID               string           `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	MerchantID       string           `json:"merchantId" gorm:"type:varchar(36);not null;index"`
	CustomerID       *string          `json:"customerId" gorm:"type:varchar(36);index"`
	InvoiceID        *string          `json:"invoiceId" gorm:"type:varchar(36);index"`
	CreditNoteNumber string           `json:"creditNoteNumber" gorm:"type:varchar(100);not null;uniqueIndex:idx_merchant_credit_note_number"`
	Status           CreditNoteStatus `json:"status" gorm:"type:varchar(50);default:'Draft';index"`
	Type             CreditNoteType   `json:"type" gorm:"type:varchar(50);not null;index"`
	IssueDate        time.Time        `json:"issueDate" gorm:"not null;index"`
	Amount           decimal.Decimal  `json:"amount" gorm:"type:decimal(15,2);not null"`
	TaxAmount        decimal.Decimal  `json:"taxAmount" gorm:"type:decimal(15,2);not null;default:0"`
	TotalAmount      decimal.Decimal  `json:"totalAmount" gorm:"type:decimal(15,2);not null"`
	AppliedAmount    decimal.Decimal  `json:"appliedAmount" gorm:"type:decimal(15,2);not null;default:0"`
	BalanceAmount    decimal.Decimal  `json:"balanceAmount" gorm:"type:decimal(15,2);not null"`
	Reason           string           `json:"reason" gorm:"type:text;not null"`
	Notes            *string          `json:"notes" gorm:"type:text"`
	CreatedAt        time.Time        `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt        time.Time        `json:"updatedAt" gorm:"autoUpdateTime"`
	Currency         string           `json:"currency" gorm:"type:varchar(10);default:'USD'"`
	ExchangeRate     decimal.Decimal  `json:"exchangeRate" gorm:"type:decimal(10,4);default:1"`

	// Relationships
	Merchant     Merchant            `json:"merchant" gorm:"foreignKey:MerchantID"`
	Customer     *Customer           `json:"customer" gorm:"foreignKey:CustomerID"`
	Invoice      *Invoice            `json:"invoice" gorm:"foreignKey:InvoiceID"`
	Items        []CreditNoteItem    `json:"items" gorm:"foreignKey:CreditNoteID"`
	Applications []CreditApplication `json:"applications" gorm:"foreignKey:CreditNoteID"`
}

// CreditNoteItem represents the credit_note_items table
type CreditNoteItem struct {
	ID           string          `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	CreditNoteID string          `json:"creditNoteId" gorm:"type:varchar(36);not null;index"`
	Description  string          `json:"description" gorm:"type:text;not null"`
	Quantity     decimal.Decimal `json:"quantity" gorm:"type:decimal(10,2);not null;default:1"`
	UnitPrice    decimal.Decimal `json:"unitPrice" gorm:"type:decimal(15,2);not null;default:0"`
	TotalPrice   decimal.Decimal `json:"totalPrice" gorm:"type:decimal(15,2);not null;default:0"`
	TaxRateID    *string         `json:"taxRateId" gorm:"type:varchar(36)"`
	TaxAmount    decimal.Decimal `json:"taxAmount" gorm:"type:decimal(15,2);not null;default:0"`
	SortOrder    int             `json:"sortOrder" gorm:"default:0"`

	// Relationships
	CreditNote CreditNote `json:"creditNote" gorm:"foreignKey:CreditNoteID"`
	TaxRate    *TaxRate   `json:"taxRate" gorm:"foreignKey:TaxRateID"`
}

// CreditApplication represents the credit_applications table
type CreditApplication struct {
	ID           string          `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	CreditNoteID string          `json:"creditNoteId" gorm:"type:varchar(36);not null;index"`
	InvoiceID    string          `json:"invoiceId" gorm:"type:varchar(36);not null;index"`
	Amount       decimal.Decimal `json:"amount" gorm:"type:decimal(15,2);not null"`
	AppliedDate  time.Time       `json:"appliedDate" gorm:"not null;index"`
	CreatedAt    time.Time       `json:"createdAt" gorm:"autoCreateTime"`

	// Relationships
	CreditNote CreditNote `json:"creditNote" gorm:"foreignKey:CreditNoteID"`
	Invoice    Invoice    `json:"invoice" gorm:"foreignKey:InvoiceID"`
}

// CustomerCredit represents the customer_credits table
type CustomerCredit struct {
	ID         string          `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	CustomerID string          `json:"customerId" gorm:"type:varchar(36);not null;index"`
	Amount     decimal.Decimal `json:"amount" gorm:"type:decimal(15,2);not null"`
	Balance    decimal.Decimal `json:"balance" gorm:"type:decimal(15,2);not null"`
	Source     string          `json:"source" gorm:"type:varchar(100);not null"`
	SourceID   *string         `json:"sourceId" gorm:"type:varchar(36)"`
	Notes      *string         `json:"notes" gorm:"type:text"`
	CreatedAt  time.Time       `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt  time.Time       `json:"updatedAt" gorm:"autoUpdateTime"`
	ExpiresAt  *time.Time      `json:"expiresAt"`

	// Relationships
	Customer Customer `json:"customer" gorm:"foreignKey:CustomerID"`
}

// PaymentReminder represents the payment_reminders table
type PaymentReminder struct {
	ID            string                `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	MerchantID    string                `json:"merchantId" gorm:"type:varchar(36);not null;index"`
	CustomerID    string                `json:"customerId" gorm:"type:varchar(36);not null;index"`
	InvoiceID     string                `json:"invoiceId" gorm:"type:varchar(36);not null;index"`
	Type          string                `json:"type" gorm:"type:varchar(50);not null"`
	Status        PaymentReminderStatus `json:"status" gorm:"type:varchar(50);default:'Scheduled';index"`
	ScheduledDate time.Time             `json:"scheduledDate" gorm:"not null;index"`
	SentDate      *time.Time            `json:"sentDate"`
	Subject       string                `json:"subject" gorm:"type:varchar(255);not null"`
	Message       string                `json:"message" gorm:"type:text;not null"`
	CreatedAt     time.Time             `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt     time.Time             `json:"updatedAt" gorm:"autoUpdateTime"`

	// Relationships
	Merchant Merchant `json:"merchant" gorm:"foreignKey:MerchantID"`
	Customer Customer `json:"customer" gorm:"foreignKey:CustomerID"`
	Invoice  Invoice  `json:"invoice" gorm:"foreignKey:InvoiceID"`
}

// CustomerStatement represents the customer_statements table
type CustomerStatement struct {
	ID         string    `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	MerchantID string    `json:"merchantId" gorm:"type:varchar(36);not null;index"`
	CustomerID string    `json:"customerId" gorm:"type:varchar(36);not null;index"`
	StartDate  time.Time `json:"startDate" gorm:"not null"`
	EndDate    time.Time `json:"endDate" gorm:"not null"`
	CreatedAt  time.Time `json:"createdAt" gorm:"autoCreateTime"`

	// Relationships
	Merchant Merchant        `json:"merchant" gorm:"foreignKey:MerchantID"`
	Customer Customer        `json:"customer" gorm:"foreignKey:CustomerID"`
	Items    []StatementItem `json:"items" gorm:"foreignKey:StatementID"`
}

// StatementItem represents the statement_items table
type StatementItem struct {
	ID          string          `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	StatementID string          `json:"statementId" gorm:"type:varchar(36);not null;index"`
	Date        time.Time       `json:"date" gorm:"not null"`
	Description string          `json:"description" gorm:"type:text;not null"`
	Amount      decimal.Decimal `json:"amount" gorm:"type:decimal(15,2);not null"`
	Balance     decimal.Decimal `json:"balance" gorm:"type:decimal(15,2);not null"`
	Type        string          `json:"type" gorm:"type:varchar(50);not null"`
	Reference   *string         `json:"reference" gorm:"type:varchar(255)"`

	// Relationships
	Statement CustomerStatement `json:"statement" gorm:"foreignKey:StatementID"`
}

// CollectionCase represents the collection_cases table
type CollectionCase struct {
	ID              string               `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	MerchantID      string               `json:"merchantId" gorm:"type:varchar(36);not null;index"`
	CustomerID      string               `json:"customerId" gorm:"type:varchar(36);not null;index"`
	InvoiceID       *string              `json:"invoiceId" gorm:"type:varchar(36);index"`
	CaseNumber      string               `json:"caseNumber" gorm:"type:varchar(100);not null;uniqueIndex:idx_merchant_case_number"`
	Status          CollectionCaseStatus `json:"status" gorm:"type:varchar(50);default:'New';index"`
	Priority        string               `json:"priority" gorm:"type:varchar(20);default:'Medium'"`
	TotalAmount     decimal.Decimal      `json:"totalAmount" gorm:"type:decimal(15,2);not null"`
	CollectedAmount decimal.Decimal      `json:"collectedAmount" gorm:"type:decimal(15,2);not null;default:0"`
	BalanceAmount   decimal.Decimal      `json:"balanceAmount" gorm:"type:decimal(15,2);not null"`
	AssignedTo      *string              `json:"assignedTo" gorm:"type:varchar(36);index"`
	CreatedAt       time.Time            `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt       time.Time            `json:"updatedAt" gorm:"autoUpdateTime"`
	DueDate         *time.Time           `json:"dueDate"`
	LastContactDate *time.Time           `json:"lastContactDate"`
	NextContactDate *time.Time           `json:"nextContactDate"`
	Notes           *string              `json:"notes" gorm:"type:text"`

	// Relationships
	Merchant     Merchant             `json:"merchant" gorm:"foreignKey:MerchantID"`
	Customer     Customer             `json:"customer" gorm:"foreignKey:CustomerID"`
	Invoice      *Invoice             `json:"invoice" gorm:"foreignKey:InvoiceID"`
	AssignedUser *User                `json:"assignedUser" gorm:"foreignKey:AssignedTo"`
	Activities   []CollectionActivity `json:"activities" gorm:"foreignKey:CollectionCaseID"`
}

// CollectionActivity represents the collection_activities table
type CollectionActivity struct {
	ID               string                 `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	CollectionCaseID string                 `json:"collectionCaseId" gorm:"type:varchar(36);not null;index"`
	Type             CollectionActivityType `json:"type" gorm:"type:varchar(50);not null;index"`
	Date             time.Time              `json:"date" gorm:"not null;index"`
	Description      string                 `json:"description" gorm:"type:text;not null"`
	Amount           *decimal.Decimal       `json:"amount" gorm:"type:decimal(15,2)"`
	ContactMethod    *string                `json:"contactMethod" gorm:"type:varchar(100)"`
	Outcome          *string                `json:"outcome" gorm:"type:varchar(255)"`
	NextAction       *string                `json:"nextAction" gorm:"type:text"`
	NextActionDate   *time.Time             `json:"nextActionDate"`
	CreatedBy        string                 `json:"createdBy" gorm:"type:varchar(36);not null;index"`
	CreatedAt        time.Time              `json:"createdAt" gorm:"autoCreateTime"`

	// Relationships
	CollectionCase CollectionCase `json:"collectionCase" gorm:"foreignKey:CollectionCaseID"`
	Creator        User           `json:"creator" gorm:"foreignKey:CreatedBy"`
}

// CollectionTemplate represents the collection_templates table
type CollectionTemplate struct {
	ID          string    `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	MerchantID  string    `json:"merchantId" gorm:"type:varchar(36);not null;index"`
	Name        string    `json:"name" gorm:"type:varchar(255);not null"`
	Description *string   `json:"description" gorm:"type:text"`
	IsActive    bool      `json:"isActive" gorm:"default:true;index"`
	CreatedAt   time.Time `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt   time.Time `json:"updatedAt" gorm:"autoUpdateTime"`

	// Relationships
	Merchant Merchant                 `json:"merchant" gorm:"foreignKey:MerchantID"`
	Steps    []CollectionTemplateStep `json:"steps" gorm:"foreignKey:TemplateID"`
}

// CollectionTemplateStep represents the collection_template_steps table
type CollectionTemplateStep struct {
	ID           string                 `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	TemplateID   string                 `json:"templateId" gorm:"type:varchar(36);not null;index"`
	StepNumber   int                    `json:"stepNumber" gorm:"not null"`
	Name         string                 `json:"name" gorm:"type:varchar(255);not null"`
	Type         CollectionActivityType `json:"type" gorm:"type:varchar(50);not null"`
	DaysAfterDue int                    `json:"daysAfterDue" gorm:"not null"`
	Subject      *string                `json:"subject" gorm:"type:varchar(255)"`
	Message      *string                `json:"message" gorm:"type:text"`
	IsActive     bool                   `json:"isActive" gorm:"default:true"`

	// Relationships
	Template CollectionTemplate `json:"template" gorm:"foreignKey:TemplateID"`
}

// BeforeCreate hooks
func (c *CreditNote) BeforeCreate(tx *gorm.DB) error {
	if c.ID == "" {
		c.ID = uuid.New().String()
	}
	return nil
}

func (c *CreditNoteItem) BeforeCreate(tx *gorm.DB) error {
	if c.ID == "" {
		c.ID = uuid.New().String()
	}
	return nil
}

func (c *CreditApplication) BeforeCreate(tx *gorm.DB) error {
	if c.ID == "" {
		c.ID = uuid.New().String()
	}
	return nil
}

func (c *CustomerCredit) BeforeCreate(tx *gorm.DB) error {
	if c.ID == "" {
		c.ID = uuid.New().String()
	}
	return nil
}

func (p *PaymentReminder) BeforeCreate(tx *gorm.DB) error {
	if p.ID == "" {
		p.ID = uuid.New().String()
	}
	return nil
}

func (c *CustomerStatement) BeforeCreate(tx *gorm.DB) error {
	if c.ID == "" {
		c.ID = uuid.New().String()
	}
	return nil
}

func (s *StatementItem) BeforeCreate(tx *gorm.DB) error {
	if s.ID == "" {
		s.ID = uuid.New().String()
	}
	return nil
}

func (c *CollectionCase) BeforeCreate(tx *gorm.DB) error {
	if c.ID == "" {
		c.ID = uuid.New().String()
	}
	return nil
}

func (c *CollectionActivity) BeforeCreate(tx *gorm.DB) error {
	if c.ID == "" {
		c.ID = uuid.New().String()
	}
	return nil
}

func (c *CollectionTemplate) BeforeCreate(tx *gorm.DB) error {
	if c.ID == "" {
		c.ID = uuid.New().String()
	}
	return nil
}

func (c *CollectionTemplateStep) BeforeCreate(tx *gorm.DB) error {
	if c.ID == "" {
		c.ID = uuid.New().String()
	}
	return nil
}
