package models

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// InvoiceTemplate represents the invoice_templates table
type InvoiceTemplate struct {
	ID          string    `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	MerchantID  string    `json:"merchantId" gorm:"type:varchar(36);not null;index"`
	Name        string    `json:"name" gorm:"type:varchar(255);not null"`
	Description *string   `json:"description" gorm:"type:text"`
	Terms       *string   `json:"terms" gorm:"type:text"`
	Notes       *string   `json:"notes" gorm:"type:text"`
	IsDefault   bool      `json:"isDefault" gorm:"default:false"`
	IsActive    bool      `json:"isActive" gorm:"default:true;index"`
	CreatedAt   time.Time `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt   time.Time `json:"updatedAt" gorm:"autoUpdateTime"`

	// Relationships
	Merchant Merchant              `json:"merchant" gorm:"foreignKey:MerchantID"`
	Items    []InvoiceTemplateItem `json:"items" gorm:"foreignKey:TemplateID"`
}

// InvoiceTemplateItem represents the invoice_template_items table
type InvoiceTemplateItem struct {
	ID          string          `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	TemplateID  string          `json:"templateId" gorm:"type:varchar(36);not null;index"`
	Description string          `json:"description" gorm:"type:text;not null"`
	Quantity    decimal.Decimal `json:"quantity" gorm:"type:decimal(10,2);not null;default:1"`
	UnitPrice   decimal.Decimal `json:"unitPrice" gorm:"type:decimal(15,2);not null;default:0"`
	TaxRateID   *string         `json:"taxRateId" gorm:"type:varchar(36)"`
	SortOrder   int             `json:"sortOrder" gorm:"default:0"`

	// Relationships
	Template InvoiceTemplate `json:"template" gorm:"foreignKey:TemplateID"`
	TaxRate  *TaxRate        `json:"taxRate" gorm:"foreignKey:TaxRateID"`
}

// RecurringInvoice represents the recurring_invoices table
type RecurringInvoice struct {
	ID              string             `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	MerchantID      string             `json:"merchantId" gorm:"type:varchar(36);not null;index"`
	CustomerID      string             `json:"customerId" gorm:"type:varchar(36);not null;index"`
	TemplateID      *string            `json:"templateId" gorm:"type:varchar(36)"`
	Name            string             `json:"name" gorm:"type:varchar(255);not null"`
	Frequency       RecurringFrequency `json:"frequency" gorm:"type:varchar(50);not null"`
	Status          RecurringStatus    `json:"status" gorm:"type:varchar(50);default:'Active';index"`
	StartDate       time.Time          `json:"startDate" gorm:"not null"`
	EndDate         *time.Time         `json:"endDate"`
	NextInvoiceDate time.Time          `json:"nextInvoiceDate" gorm:"not null;index"`
	LastInvoiceDate *time.Time         `json:"lastInvoiceDate"`
	Terms           *string            `json:"terms" gorm:"type:text"`
	Notes           *string            `json:"notes" gorm:"type:text"`
	CreatedAt       time.Time          `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt       time.Time          `json:"updatedAt" gorm:"autoUpdateTime"`
	CustomInterval  *int               `json:"customInterval"`

	// Relationships
	Merchant Merchant               `json:"merchant" gorm:"foreignKey:MerchantID"`
	Customer Customer               `json:"customer" gorm:"foreignKey:CustomerID"`
	Template *InvoiceTemplate       `json:"template" gorm:"foreignKey:TemplateID"`
	Items    []RecurringInvoiceItem `json:"items" gorm:"foreignKey:RecurringInvoiceID"`
}

// RecurringInvoiceItem represents the recurring_invoice_items table
type RecurringInvoiceItem struct {
	ID                 string          `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	RecurringInvoiceID string          `json:"recurringInvoiceId" gorm:"type:varchar(36);not null;index"`
	Description        string          `json:"description" gorm:"type:text;not null"`
	Quantity           decimal.Decimal `json:"quantity" gorm:"type:decimal(10,2);not null;default:1"`
	UnitPrice          decimal.Decimal `json:"unitPrice" gorm:"type:decimal(15,2);not null;default:0"`
	TaxRateID          *string         `json:"taxRateId" gorm:"type:varchar(36)"`
	SortOrder          int             `json:"sortOrder" gorm:"default:0"`

	// Relationships
	RecurringInvoice RecurringInvoice `json:"recurringInvoice" gorm:"foreignKey:RecurringInvoiceID"`
	TaxRate          *TaxRate         `json:"taxRate" gorm:"foreignKey:TaxRateID"`
}

// BudgetItem represents the budget_items table
type BudgetItem struct {
	ID           string          `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	MerchantID   string          `json:"merchantId" gorm:"type:varchar(36);not null;index"`
	AccountID    string          `json:"accountId" gorm:"type:varchar(36);not null;index"`
	Year         int             `json:"year" gorm:"not null;index"`
	Month        int             `json:"month" gorm:"not null;index"`
	Amount       decimal.Decimal `json:"amount" gorm:"type:decimal(15,2);not null;default:0"`
	ActualAmount decimal.Decimal `json:"actualAmount" gorm:"type:decimal(15,2);default:0"`
	Notes        *string         `json:"notes" gorm:"type:text"`
	CreatedAt    time.Time       `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt    time.Time       `json:"updatedAt" gorm:"autoUpdateTime"`

	// Relationships
	Merchant Merchant       `json:"merchant" gorm:"foreignKey:MerchantID"`
	Account  ChartOfAccount `json:"account" gorm:"foreignKey:AccountID"`
}

// BudgetTemplate represents the budget_templates table
type BudgetTemplate struct {
	ID          string    `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	MerchantID  string    `json:"merchantId" gorm:"type:varchar(36);not null;index"`
	Name        string    `json:"name" gorm:"type:varchar(255);not null"`
	Description *string   `json:"description" gorm:"type:text"`
	IsActive    bool      `json:"isActive" gorm:"default:true;index"`
	CreatedAt   time.Time `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt   time.Time `json:"updatedAt" gorm:"autoUpdateTime"`

	// Relationships
	Merchant Merchant             `json:"merchant" gorm:"foreignKey:MerchantID"`
	Items    []BudgetTemplateItem `json:"items" gorm:"foreignKey:TemplateID"`
}

// BudgetTemplateItem represents the budget_template_items table
type BudgetTemplateItem struct {
	ID         string          `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	TemplateID string          `json:"templateId" gorm:"type:varchar(36);not null;index"`
	AccountID  string          `json:"accountId" gorm:"type:varchar(36);not null;index"`
	Amount     decimal.Decimal `json:"amount" gorm:"type:decimal(15,2);not null;default:0"`
	Notes      *string         `json:"notes" gorm:"type:text"`

	// Relationships
	Template BudgetTemplate `json:"template" gorm:"foreignKey:TemplateID"`
	Account  ChartOfAccount `json:"account" gorm:"foreignKey:AccountID"`
}

// CashFlowCategory represents the cash_flow_categories table
type CashFlowCategory struct {
	ID          string    `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	MerchantID  string    `json:"merchantId" gorm:"type:varchar(36);not null;index"`
	Name        string    `json:"name" gorm:"type:varchar(255);not null"`
	Description *string   `json:"description" gorm:"type:text"`
	Color       *string   `json:"color" gorm:"type:varchar(7)"`
	IsActive    bool      `json:"isActive" gorm:"default:true;index"`
	CreatedAt   time.Time `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt   time.Time `json:"updatedAt" gorm:"autoUpdateTime"`

	// Relationships
	Merchant       Merchant                `json:"merchant" gorm:"foreignKey:MerchantID"`
	CashFlowItems  []CashFlowItem          `json:"cashFlowItems" gorm:"foreignKey:CategoryID"`
	RecurringItems []RecurringCashFlowItem `json:"recurringItems" gorm:"foreignKey:CategoryID"`
}

// CashFlowItem represents the cash_flow_items table
type CashFlowItem struct {
	ID          string             `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	MerchantID  string             `json:"merchantId" gorm:"type:varchar(36);not null;index"`
	CategoryID  *string            `json:"categoryId" gorm:"type:varchar(36);index"`
	Type        CashFlowItemType   `json:"type" gorm:"type:varchar(50);not null;index"`
	Status      CashFlowItemStatus `json:"status" gorm:"type:varchar(50);default:'Projected';index"`
	Date        time.Time          `json:"date" gorm:"not null;index"`
	Amount      decimal.Decimal    `json:"amount" gorm:"type:decimal(15,2);not null"`
	Description string             `json:"description" gorm:"type:text;not null"`
	Reference   *string            `json:"reference" gorm:"type:varchar(255)"`
	Notes       *string            `json:"notes" gorm:"type:text"`
	CreatedAt   time.Time          `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt   time.Time          `json:"updatedAt" gorm:"autoUpdateTime"`

	// Relationships
	Merchant Merchant          `json:"merchant" gorm:"foreignKey:MerchantID"`
	Category *CashFlowCategory `json:"category" gorm:"foreignKey:CategoryID"`
}

// RecurringCashFlowItem represents the recurring_cash_flow_items table
type RecurringCashFlowItem struct {
	ID             string             `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	MerchantID     string             `json:"merchantId" gorm:"type:varchar(36);not null;index"`
	CategoryID     *string            `json:"categoryId" gorm:"type:varchar(36);index"`
	Type           CashFlowItemType   `json:"type" gorm:"type:varchar(50);not null;index"`
	Frequency      RecurringFrequency `json:"frequency" gorm:"type:varchar(50);not null"`
	Status         RecurringStatus    `json:"status" gorm:"type:varchar(50);default:'Active';index"`
	StartDate      time.Time          `json:"startDate" gorm:"not null"`
	EndDate        *time.Time         `json:"endDate"`
	NextDate       time.Time          `json:"nextDate" gorm:"not null;index"`
	Amount         decimal.Decimal    `json:"amount" gorm:"type:decimal(15,2);not null"`
	Description    string             `json:"description" gorm:"type:text;not null"`
	Notes          *string            `json:"notes" gorm:"type:text"`
	CreatedAt      time.Time          `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt      time.Time          `json:"updatedAt" gorm:"autoUpdateTime"`
	CustomInterval *int               `json:"customInterval"`

	// Relationships
	Merchant Merchant          `json:"merchant" gorm:"foreignKey:MerchantID"`
	Category *CashFlowCategory `json:"category" gorm:"foreignKey:CategoryID"`
}

// EmailTemplate represents the email_templates table
type EmailTemplate struct {
	ID         string    `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	MerchantID string    `json:"merchantId" gorm:"type:varchar(36);not null;index"`
	Name       string    `json:"name" gorm:"type:varchar(255);not null"`
	Type       string    `json:"type" gorm:"type:varchar(100);not null;index"`
	Subject    string    `json:"subject" gorm:"type:varchar(255);not null"`
	Body       string    `json:"body" gorm:"type:text;not null"`
	IsDefault  bool      `json:"isDefault" gorm:"default:false"`
	IsActive   bool      `json:"isActive" gorm:"default:true;index"`
	CreatedAt  time.Time `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt  time.Time `json:"updatedAt" gorm:"autoUpdateTime"`

	// Relationships
	Merchant  Merchant   `json:"merchant" gorm:"foreignKey:MerchantID"`
	EmailLogs []EmailLog `json:"emailLogs" gorm:"foreignKey:TemplateID"`
}

// EmailLog represents the email_logs table
type EmailLog struct {
	ID           string      `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	MerchantID   string      `json:"merchantId" gorm:"type:varchar(36);not null;index"`
	TemplateID   *string     `json:"templateId" gorm:"type:varchar(36);index"`
	ToEmail      string      `json:"toEmail" gorm:"type:varchar(255);not null;index"`
	FromEmail    string      `json:"fromEmail" gorm:"type:varchar(255);not null"`
	Subject      string      `json:"subject" gorm:"type:varchar(255);not null"`
	Body         string      `json:"body" gorm:"type:text;not null"`
	Status       EmailStatus `json:"status" gorm:"type:varchar(50);not null;index"`
	SentAt       *time.Time  `json:"sentAt"`
	ErrorMessage *string     `json:"errorMessage" gorm:"type:text"`
	CreatedAt    time.Time   `json:"createdAt" gorm:"autoCreateTime"`

	// Relationships
	Merchant Merchant       `json:"merchant" gorm:"foreignKey:MerchantID"`
	Template *EmailTemplate `json:"template" gorm:"foreignKey:TemplateID"`
}

// SalesOrder represents the sales_orders table
type SalesOrder struct {
	ID           string     `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	MerchantID   string     `json:"merchantId" gorm:"type:varchar(36);not null;index"`
	CustomerID   string     `json:"customerId" gorm:"type:varchar(36);not null;index"`
	OrderNumber  string     `json:"orderNumber" gorm:"type:varchar(100);not null;uniqueIndex:idx_merchant_order_number"`
	OrderDate    time.Time  `json:"orderDate" gorm:"not null;index"`
	DeliveryDate *time.Time `json:"deliveryDate"`
	Status       string     `json:"status" gorm:"type:varchar(50);default:'Draft';index"`
	Notes        *string    `json:"notes" gorm:"type:text"`
	CreatedAt    time.Time  `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt    time.Time  `json:"updatedAt" gorm:"autoUpdateTime"`

	// Relationships
	Merchant Merchant `json:"merchant" gorm:"foreignKey:MerchantID"`
	Customer Customer `json:"customer" gorm:"foreignKey:CustomerID"`
}

// PurchaseOrder represents the purchase_orders table
type PurchaseOrder struct {
	ID              string              `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	MerchantID      string              `json:"merchantId" gorm:"type:varchar(36);not null;index"`
	VendorID        string              `json:"vendorId" gorm:"type:varchar(36);not null;index"`
	OrderNumber     string              `json:"orderNumber" gorm:"type:varchar(100);not null;uniqueIndex:idx_merchant_po_number"`
	Status          PurchaseOrderStatus `json:"status" gorm:"type:varchar(50);default:'Draft';index"`
	OrderDate       time.Time           `json:"orderDate" gorm:"not null;index"`
	ExpectedDate    *time.Time          `json:"expectedDate" gorm:"index"`
	ReceivedDate    *time.Time          `json:"receivedDate" gorm:"index"`
	SubTotal        decimal.Decimal     `json:"subTotal" gorm:"type:decimal(15,2);default:0"`
	TaxAmount       decimal.Decimal     `json:"taxAmount" gorm:"type:decimal(15,2);default:0"`
	TotalAmount     decimal.Decimal     `json:"totalAmount" gorm:"type:decimal(15,2);default:0"`
	Currency        string              `json:"currency" gorm:"type:varchar(10);default:'USD'"`
	ExchangeRate    decimal.Decimal     `json:"exchangeRate" gorm:"type:decimal(10,4);default:1"`
	Terms           *string             `json:"terms" gorm:"type:text"`
	Notes           *string             `json:"notes" gorm:"type:text"`
	ShippingAddress *string             `json:"shippingAddress" gorm:"type:text"`
	BillingAddress  *string             `json:"billingAddress" gorm:"type:text"`
	Reference       *string             `json:"reference" gorm:"type:varchar(255)"`
	CreatedAt       time.Time           `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt       time.Time           `json:"updatedAt" gorm:"autoUpdateTime"`

	// Relationships
	Merchant Merchant            `json:"merchant" gorm:"foreignKey:MerchantID"`
	Vendor   Vendor              `json:"vendor" gorm:"foreignKey:VendorID"`
	Items    []PurchaseOrderItem `json:"items" gorm:"foreignKey:PurchaseOrderID"`
}

// PurchaseOrderItem represents the purchase_order_items table
type PurchaseOrderItem struct {
	ID               string          `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	PurchaseOrderID  string          `json:"purchaseOrderId" gorm:"type:varchar(36);not null;index"`
	Description      string          `json:"description" gorm:"type:text;not null"`
	Quantity         decimal.Decimal `json:"quantity" gorm:"type:decimal(10,2);not null;default:1"`
	UnitPrice        decimal.Decimal `json:"unitPrice" gorm:"type:decimal(15,2);not null;default:0"`
	TotalPrice       decimal.Decimal `json:"totalPrice" gorm:"type:decimal(15,2);not null;default:0"`
	TaxRateID        *string         `json:"taxRateId" gorm:"type:varchar(36)"`
	TaxAmount        decimal.Decimal `json:"taxAmount" gorm:"type:decimal(15,2);default:0"`
	ReceivedQuantity decimal.Decimal `json:"receivedQuantity" gorm:"type:decimal(10,2);default:0"`
	SortOrder        int             `json:"sortOrder" gorm:"default:0"`
	InventoryItemID  *string         `json:"inventoryItemId" gorm:"type:varchar(36)"`

	// Relationships
	PurchaseOrder PurchaseOrder  `json:"purchaseOrder" gorm:"foreignKey:PurchaseOrderID"`
	TaxRate       *TaxRate       `json:"taxRate" gorm:"foreignKey:TaxRateID"`
	InventoryItem *InventoryItem `json:"inventoryItem" gorm:"foreignKey:InventoryItemID"`
}

// TaxReport represents the tax_reports table
type TaxReport struct {
	ID         string        `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	MerchantID string        `json:"merchantId" gorm:"type:varchar(36);not null;index"`
	Type       TaxReportType `json:"type" gorm:"type:varchar(50);not null;index"`
	StartDate  time.Time     `json:"startDate" gorm:"not null"`
	EndDate    time.Time     `json:"endDate" gorm:"not null"`
	Data       string        `json:"data" gorm:"type:jsonb;not null"`
	CreatedAt  time.Time     `json:"createdAt" gorm:"autoCreateTime"`

	// Relationships
	Merchant Merchant `json:"merchant" gorm:"foreignKey:MerchantID"`
}

// Subscription represents the subscriptions table
type Subscription struct {
	ID                   string             `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	MerchantID           string             `json:"merchantId" gorm:"type:varchar(36);not null;uniqueIndex"`
	Plan                 SubscriptionPlan   `json:"plan" gorm:"type:varchar(50);not null"`
	Status               SubscriptionStatus `json:"status" gorm:"type:varchar(50);not null;index"`
	StartDate            time.Time          `json:"startDate" gorm:"not null"`
	EndDate              *time.Time         `json:"endDate"`
	TrialEndDate         *time.Time         `json:"trialEndDate"`
	BillingCycle         string             `json:"billingCycle" gorm:"type:varchar(20);default:'monthly'"`
	Amount               decimal.Decimal    `json:"amount" gorm:"type:decimal(10,2);not null"`
	Currency             string             `json:"currency" gorm:"type:varchar(10);default:'USD'"`
	StripeSubscriptionID *string            `json:"stripeSubscriptionId" gorm:"type:varchar(255)"`
	CreatedAt            time.Time          `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt            time.Time          `json:"updatedAt" gorm:"autoUpdateTime"`

	// Relationships
	Merchant     Merchant      `json:"merchant" gorm:"foreignKey:MerchantID"`
	UsageRecords []UsageRecord `json:"usageRecords" gorm:"foreignKey:SubscriptionID"`
}

// UsageRecord represents the usage_records table
type UsageRecord struct {
	ID             string    `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	SubscriptionID string    `json:"subscriptionId" gorm:"type:varchar(36);not null;index"`
	MetricName     string    `json:"metricName" gorm:"type:varchar(100);not null"`
	Value          int       `json:"value" gorm:"not null"`
	Date           time.Time `json:"date" gorm:"not null;index"`
	CreatedAt      time.Time `json:"createdAt" gorm:"autoCreateTime"`

	// Relationships
	Subscription Subscription `json:"subscription" gorm:"foreignKey:SubscriptionID"`
}

// BeforeCreate hooks for all remaining models
func (i *InvoiceTemplate) BeforeCreate(tx *gorm.DB) error {
	if i.ID == "" {
		i.ID = uuid.New().String()
	}
	return nil
}

func (i *InvoiceTemplateItem) BeforeCreate(tx *gorm.DB) error {
	if i.ID == "" {
		i.ID = uuid.New().String()
	}
	return nil
}

func (r *RecurringInvoice) BeforeCreate(tx *gorm.DB) error {
	if r.ID == "" {
		r.ID = uuid.New().String()
	}
	return nil
}

func (r *RecurringInvoiceItem) BeforeCreate(tx *gorm.DB) error {
	if r.ID == "" {
		r.ID = uuid.New().String()
	}
	return nil
}

func (b *BudgetItem) BeforeCreate(tx *gorm.DB) error {
	if b.ID == "" {
		b.ID = uuid.New().String()
	}
	return nil
}

func (b *BudgetTemplate) BeforeCreate(tx *gorm.DB) error {
	if b.ID == "" {
		b.ID = uuid.New().String()
	}
	return nil
}

func (b *BudgetTemplateItem) BeforeCreate(tx *gorm.DB) error {
	if b.ID == "" {
		b.ID = uuid.New().String()
	}
	return nil
}

func (c *CashFlowCategory) BeforeCreate(tx *gorm.DB) error {
	if c.ID == "" {
		c.ID = uuid.New().String()
	}
	return nil
}

func (c *CashFlowItem) BeforeCreate(tx *gorm.DB) error {
	if c.ID == "" {
		c.ID = uuid.New().String()
	}
	return nil
}

func (r *RecurringCashFlowItem) BeforeCreate(tx *gorm.DB) error {
	if r.ID == "" {
		r.ID = uuid.New().String()
	}
	return nil
}

func (e *EmailTemplate) BeforeCreate(tx *gorm.DB) error {
	if e.ID == "" {
		e.ID = uuid.New().String()
	}
	return nil
}

func (e *EmailLog) BeforeCreate(tx *gorm.DB) error {
	if e.ID == "" {
		e.ID = uuid.New().String()
	}
	return nil
}

func (s *SalesOrder) BeforeCreate(tx *gorm.DB) error {
	if s.ID == "" {
		s.ID = uuid.New().String()
	}
	return nil
}

func (p *PurchaseOrder) BeforeCreate(tx *gorm.DB) error {
	if p.ID == "" {
		p.ID = uuid.New().String()
	}
	return nil
}

func (p *PurchaseOrderItem) BeforeCreate(tx *gorm.DB) error {
	if p.ID == "" {
		p.ID = uuid.New().String()
	}
	return nil
}

func (t *TaxReport) BeforeCreate(tx *gorm.DB) error {
	if t.ID == "" {
		t.ID = uuid.New().String()
	}
	return nil
}

func (s *Subscription) BeforeCreate(tx *gorm.DB) error {
	if s.ID == "" {
		s.ID = uuid.New().String()
	}
	return nil
}

func (u *UsageRecord) BeforeCreate(tx *gorm.DB) error {
	if u.ID == "" {
		u.ID = uuid.New().String()
	}
	return nil
}
