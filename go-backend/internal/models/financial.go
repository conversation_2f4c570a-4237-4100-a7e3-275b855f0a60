package models

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// Financial Status Enums
type InvoiceStatus string
type BillStatus string
type ExpenseStatus string

const (
	InvoiceStatusDraft         InvoiceStatus = "Draft"
	InvoiceStatusSent          InvoiceStatus = "Sent"
	InvoiceStatusPartiallyPaid InvoiceStatus = "PartiallyPaid"
	InvoiceStatusPaid          InvoiceStatus = "Paid"
	InvoiceStatusOverdue       InvoiceStatus = "Overdue"
	InvoiceStatusVoid          InvoiceStatus = "Void"

	BillStatusDraft         BillStatus = "Draft"
	BillStatusOpen          BillStatus = "Open"
	BillStatusPartiallyPaid BillStatus = "PartiallyPaid"
	BillStatusPaid          BillStatus = "Paid"
	BillStatusVoid          BillStatus = "Void"

	ExpenseStatusPending  ExpenseStatus = "Pending"
	ExpenseStatusApproved ExpenseStatus = "Approved"
	ExpenseStatusRejected ExpenseStatus = "Rejected"
	ExpenseStatusPaid     ExpenseStatus = "Paid"
)

// Invoice represents the invoices table
type Invoice struct {
	ID              string          `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	MerchantID      string          `json:"merchantId" gorm:"type:varchar(36);not null;index"`
	CustomerID      string          `json:"customerId" gorm:"type:varchar(36);not null;index"`
	InvoiceNumber   string          `json:"invoiceNumber" gorm:"type:varchar(100);not null;uniqueIndex:idx_merchant_invoice_number"`
	Status          InvoiceStatus   `json:"status" gorm:"type:varchar(50);default:'Draft';index"`
	IssueDate       time.Time       `json:"issueDate" gorm:"not null;index"`
	DueDate         *time.Time      `json:"dueDate" gorm:"index"`
	SubTotal        decimal.Decimal `json:"subTotal" gorm:"type:decimal(15,2);not null;default:0"`
	TaxAmount       decimal.Decimal `json:"taxAmount" gorm:"type:decimal(15,2);not null;default:0"`
	DiscountAmount  decimal.Decimal `json:"discountAmount" gorm:"type:decimal(15,2);not null;default:0"`
	TotalAmount     decimal.Decimal `json:"totalAmount" gorm:"type:decimal(15,2);not null;default:0"`
	PaidAmount      decimal.Decimal `json:"paidAmount" gorm:"type:decimal(15,2);not null;default:0"`
	BalanceAmount   decimal.Decimal `json:"balanceAmount" gorm:"type:decimal(15,2);not null;default:0"`
	Notes           *string         `json:"notes" gorm:"type:text"`
	Terms           *string         `json:"terms" gorm:"type:text"`
	CreatedAt       time.Time       `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt       time.Time       `json:"updatedAt" gorm:"autoUpdateTime"`
	Currency        string          `json:"currency" gorm:"type:varchar(10);default:'USD'"`
	ExchangeRate    decimal.Decimal `json:"exchangeRate" gorm:"type:decimal(10,4);default:1"`
	Reference       *string         `json:"reference" gorm:"type:varchar(255)"`
	PoNumber        *string         `json:"poNumber" gorm:"type:varchar(100)"`
	ShippingAddress *string         `json:"shippingAddress" gorm:"type:text"`
	BillingAddress  *string         `json:"billingAddress" gorm:"type:text"`

	// Relationships
	Merchant       Merchant         `json:"merchant" gorm:"foreignKey:MerchantID"`
	Customer       Customer         `json:"customer" gorm:"foreignKey:CustomerID"`
	Items          []InvoiceItem    `json:"items" gorm:"foreignKey:InvoiceID"`
	Payments       []InvoicePayment `json:"payments" gorm:"foreignKey:InvoiceID"`
	CreditNotes    []CreditNote     `json:"creditNotes" gorm:"foreignKey:InvoiceID"`
	JournalEntries []JournalEntry   `json:"journalEntries" gorm:"foreignKey:SourceID;foreignKey:SourceType"`
}

// InvoiceItem represents the invoice_items table
type InvoiceItem struct {
	ID          string          `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	InvoiceID   string          `json:"invoiceId" gorm:"type:varchar(36);not null;index"`
	Description string          `json:"description" gorm:"type:text;not null"`
	Quantity    decimal.Decimal `json:"quantity" gorm:"type:decimal(10,2);not null;default:1"`
	UnitPrice   decimal.Decimal `json:"unitPrice" gorm:"type:decimal(15,2);not null;default:0"`
	TotalPrice  decimal.Decimal `json:"totalPrice" gorm:"type:decimal(15,2);not null;default:0"`
	TaxRateID   *string         `json:"taxRateId" gorm:"type:varchar(36)"`
	TaxAmount   decimal.Decimal `json:"taxAmount" gorm:"type:decimal(15,2);not null;default:0"`
	SortOrder   int             `json:"sortOrder" gorm:"default:0"`

	// Relationships
	Invoice Invoice  `json:"invoice" gorm:"foreignKey:InvoiceID"`
	TaxRate *TaxRate `json:"taxRate" gorm:"foreignKey:TaxRateID"`
}

// InvoicePayment represents the invoice_payments table
type InvoicePayment struct {
	ID            string          `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	InvoiceID     string          `json:"invoiceId" gorm:"type:varchar(36);not null;index"`
	Amount        decimal.Decimal `json:"amount" gorm:"type:decimal(15,2);not null"`
	PaymentDate   time.Time       `json:"paymentDate" gorm:"not null;index"`
	PaymentMethod *string         `json:"paymentMethod" gorm:"type:varchar(100)"`
	Reference     *string         `json:"reference" gorm:"type:varchar(255)"`
	Notes         *string         `json:"notes" gorm:"type:text"`
	CreatedAt     time.Time       `json:"createdAt" gorm:"autoCreateTime"`

	// Relationships
	Invoice Invoice `json:"invoice" gorm:"foreignKey:InvoiceID"`
}

// Bill represents the bills table
type Bill struct {
	ID             string          `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	MerchantID     string          `json:"merchantId" gorm:"type:varchar(36);not null;index"`
	VendorID       string          `json:"vendorId" gorm:"type:varchar(36);not null;index"`
	BillNumber     *string         `json:"billNumber" gorm:"type:varchar(100)"`
	VendorBillNo   *string         `json:"vendorBillNo" gorm:"type:varchar(100)"`
	Status         BillStatus      `json:"status" gorm:"type:varchar(50);default:'Draft';index"`
	BillDate       time.Time       `json:"billDate" gorm:"not null;index"`
	DueDate        *time.Time      `json:"dueDate" gorm:"index"`
	SubTotal       decimal.Decimal `json:"subTotal" gorm:"type:decimal(15,2);not null;default:0"`
	TaxAmount      decimal.Decimal `json:"taxAmount" gorm:"type:decimal(15,2);not null;default:0"`
	DiscountAmount decimal.Decimal `json:"discountAmount" gorm:"type:decimal(15,2);not null;default:0"`
	TotalAmount    decimal.Decimal `json:"totalAmount" gorm:"type:decimal(15,2);not null;default:0"`
	PaidAmount     decimal.Decimal `json:"paidAmount" gorm:"type:decimal(15,2);not null;default:0"`
	BalanceAmount  decimal.Decimal `json:"balanceAmount" gorm:"type:decimal(15,2);not null;default:0"`
	Notes          *string         `json:"notes" gorm:"type:text"`
	CreatedAt      time.Time       `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt      time.Time       `json:"updatedAt" gorm:"autoUpdateTime"`
	Currency       string          `json:"currency" gorm:"type:varchar(10);default:'USD'"`
	ExchangeRate   decimal.Decimal `json:"exchangeRate" gorm:"type:decimal(10,4);default:1"`
	Reference      *string         `json:"reference" gorm:"type:varchar(255)"`

	// Relationships
	Merchant       Merchant       `json:"merchant" gorm:"foreignKey:MerchantID"`
	Vendor         Vendor         `json:"vendor" gorm:"foreignKey:VendorID"`
	Items          []BillItem     `json:"items" gorm:"foreignKey:BillID"`
	Payments       []BillPayment  `json:"payments" gorm:"foreignKey:BillID"`
	JournalEntries []JournalEntry `json:"journalEntries" gorm:"foreignKey:SourceID;foreignKey:SourceType"`
}

// BillItem represents the bill_items table
type BillItem struct {
	ID          string          `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	BillID      string          `json:"billId" gorm:"type:varchar(36);not null;index"`
	Description string          `json:"description" gorm:"type:text;not null"`
	Quantity    decimal.Decimal `json:"quantity" gorm:"type:decimal(10,2);not null;default:1"`
	UnitPrice   decimal.Decimal `json:"unitPrice" gorm:"type:decimal(15,2);not null;default:0"`
	TotalPrice  decimal.Decimal `json:"totalPrice" gorm:"type:decimal(15,2);not null;default:0"`
	TaxRateID   *string         `json:"taxRateId" gorm:"type:varchar(36)"`
	TaxAmount   decimal.Decimal `json:"taxAmount" gorm:"type:decimal(15,2);not null;default:0"`
	SortOrder   int             `json:"sortOrder" gorm:"default:0"`

	// Relationships
	Bill    Bill     `json:"bill" gorm:"foreignKey:BillID"`
	TaxRate *TaxRate `json:"taxRate" gorm:"foreignKey:TaxRateID"`
}

// BillPayment represents the bill_payments table
type BillPayment struct {
	ID            string          `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	BillID        string          `json:"billId" gorm:"type:varchar(36);not null;index"`
	Amount        decimal.Decimal `json:"amount" gorm:"type:decimal(15,2);not null"`
	PaymentDate   time.Time       `json:"paymentDate" gorm:"not null;index"`
	PaymentMethod *string         `json:"paymentMethod" gorm:"type:varchar(100)"`
	Reference     *string         `json:"reference" gorm:"type:varchar(255)"`
	Notes         *string         `json:"notes" gorm:"type:text"`
	CreatedAt     time.Time       `json:"createdAt" gorm:"autoCreateTime"`

	// Relationships
	Bill Bill `json:"bill" gorm:"foreignKey:BillID"`
}

// Expense represents the expenses table
type Expense struct {
	ID           string          `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	MerchantID   string          `json:"merchantId" gorm:"type:varchar(36);not null;index"`
	VendorID     *string         `json:"vendorId" gorm:"type:varchar(36);index"`
	AccountID    string          `json:"accountId" gorm:"type:varchar(36);not null;index"`
	Amount       decimal.Decimal `json:"amount" gorm:"type:decimal(15,2);not null"`
	Description  string          `json:"description" gorm:"type:text;not null"`
	ExpenseDate  time.Time       `json:"expenseDate" gorm:"not null;index"`
	Status       ExpenseStatus   `json:"status" gorm:"type:varchar(50);default:'Pending';index"`
	Reference    *string         `json:"reference" gorm:"type:varchar(255)"`
	ReceiptURL   *string         `json:"receiptUrl" gorm:"type:text"`
	Notes        *string         `json:"notes" gorm:"type:text"`
	CreatedAt    time.Time       `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt    time.Time       `json:"updatedAt" gorm:"autoUpdateTime"`
	Currency     string          `json:"currency" gorm:"type:varchar(10);default:'USD'"`
	ExchangeRate decimal.Decimal `json:"exchangeRate" gorm:"type:decimal(10,4);default:1"`
	TaxAmount    decimal.Decimal `json:"taxAmount" gorm:"type:decimal(15,2);not null;default:0"`
	TaxRateID    *string         `json:"taxRateId" gorm:"type:varchar(36)"`

	// Relationships
	Merchant       Merchant       `json:"merchant" gorm:"foreignKey:MerchantID"`
	Vendor         *Vendor        `json:"vendor" gorm:"foreignKey:VendorID"`
	Account        ChartOfAccount `json:"account" gorm:"foreignKey:AccountID"`
	TaxRate        *TaxRate       `json:"taxRate" gorm:"foreignKey:TaxRateID"`
	JournalEntries []JournalEntry `json:"journalEntries" gorm:"foreignKey:SourceID;foreignKey:SourceType"`
}

// BeforeCreate hooks
func (i *Invoice) BeforeCreate(tx *gorm.DB) error {
	if i.ID == "" {
		i.ID = uuid.New().String()
	}
	return nil
}

func (ii *InvoiceItem) BeforeCreate(tx *gorm.DB) error {
	if ii.ID == "" {
		ii.ID = uuid.New().String()
	}
	return nil
}

func (ip *InvoicePayment) BeforeCreate(tx *gorm.DB) error {
	if ip.ID == "" {
		ip.ID = uuid.New().String()
	}
	return nil
}

func (b *Bill) BeforeCreate(tx *gorm.DB) error {
	if b.ID == "" {
		b.ID = uuid.New().String()
	}
	return nil
}

func (bi *BillItem) BeforeCreate(tx *gorm.DB) error {
	if bi.ID == "" {
		bi.ID = uuid.New().String()
	}
	return nil
}

func (bp *BillPayment) BeforeCreate(tx *gorm.DB) error {
	if bp.ID == "" {
		bp.ID = uuid.New().String()
	}
	return nil
}

func (e *Expense) BeforeCreate(tx *gorm.DB) error {
	if e.ID == "" {
		e.ID = uuid.New().String()
	}
	return nil
}
