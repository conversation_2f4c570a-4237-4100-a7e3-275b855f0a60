package handlers

import (
	"net/http"
	"strconv"

	"adc-account-backend/internal/models"
	"adc-account-backend/internal/services"

	"github.com/gin-gonic/gin"
)

type OrganizationHandler struct {
	service *services.OrganizationService
}

func NewOrganizationHandler(service *services.OrganizationService) *OrganizationHandler {
	return &OrganizationHandler{
		service: service,
	}
}

// GetAllOrganizations returns all organizations with pagination
func (h *OrganizationHandler) GetAllOrganizations(c *gin.Context) {
	page, _ := strconv.Atoi(c.<PERSON>fault<PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c.<PERSON><PERSON>ult<PERSON>("limit", "10"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	organizations, total, err := h.service.GetAllOrganizations(page, limit, userID.(string))
	if err != nil {
		c.J<PERSON>N(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"data":  organizations,
		"total": total,
		"page":  page,
		"limit": limit,
	})
}

// GetOrganizationByID returns an organization by ID
func (h *OrganizationHandler) GetOrganizationByID(c *gin.Context) {
	id := c.Param("id")

	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	organization, err := h.service.GetOrganizationByID(id, userID.(string))
	if err != nil {
		if err.Error() == "organization not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		if err.Error() == "access denied to this organization" {
			c.JSON(http.StatusForbidden, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, organization)
}

// GetOrganizationBySlug returns an organization by slug
func (h *OrganizationHandler) GetOrganizationBySlug(c *gin.Context) {
	slug := c.Param("slug")

	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	organization, err := h.service.GetOrganizationBySlug(slug, userID.(string))
	if err != nil {
		if err.Error() == "organization not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, organization)
}

// CreateOrganization creates a new organization
func (h *OrganizationHandler) CreateOrganization(c *gin.Context) {
	var req services.CreateOrganizationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	organization, err := h.service.CreateOrganization(req, userID.(string))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, organization)
}

// UpdateOrganization updates an existing organization
func (h *OrganizationHandler) UpdateOrganization(c *gin.Context) {
	id := c.Param("id")

	var req services.UpdateOrganizationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	organization, err := h.service.UpdateOrganization(id, req, userID.(string))
	if err != nil {
		if err.Error() == "organization not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		if err.Error() == "insufficient permissions to update this organization" {
			c.JSON(http.StatusForbidden, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, organization)
}

// DeleteOrganization deletes an organization
func (h *OrganizationHandler) DeleteOrganization(c *gin.Context) {
	id := c.Param("id")

	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	err := h.service.DeleteOrganization(id, userID.(string))
	if err != nil {
		if err.Error() == "organization not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		if err.Error() == "insufficient permissions to delete this organization" {
			c.JSON(http.StatusForbidden, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Organization deleted successfully"})
}

// AddUserToOrganization adds a user to an organization
func (h *OrganizationHandler) AddUserToOrganization(c *gin.Context) {
	organizationID := c.Param("id")

	var req struct {
		UserID          string `json:"userId" binding:"required"`
		PermissionLevel string `json:"permissionLevel" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Convert string to PermissionLevel
	var permissionLevel models.PermissionLevel
	switch req.PermissionLevel {
	case "Owner":
		permissionLevel = models.PermissionOwner
	case "Admin":
		permissionLevel = models.PermissionAdmin
	case "Manager":
		permissionLevel = models.PermissionManager
	case "Staff":
		permissionLevel = models.PermissionStaff
	case "ReadOnly":
		permissionLevel = models.PermissionReadOnly
	default:
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid permission level"})
		return
	}

	err := h.service.AddUserToOrganization(organizationID, userID.(string), req.UserID, permissionLevel)
	if err != nil {
		if err.Error() == "insufficient permissions to add users to this organization" {
			c.JSON(http.StatusForbidden, gin.H{"error": err.Error()})
			return
		}
		if err.Error() == "user already has access to this organization" {
			c.JSON(http.StatusConflict, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "User added to organization successfully"})
}

// RemoveUserFromOrganization removes a user from an organization
func (h *OrganizationHandler) RemoveUserFromOrganization(c *gin.Context) {
	organizationID := c.Param("id")
	targetUserID := c.Param("userId")

	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	err := h.service.RemoveUserFromOrganization(organizationID, userID.(string), targetUserID)
	if err != nil {
		if err.Error() == "insufficient permissions to remove users from this organization" {
			c.JSON(http.StatusForbidden, gin.H{"error": err.Error()})
			return
		}
		if err.Error() == "cannot remove the last owner from the organization" {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "User removed from organization successfully"})
}
