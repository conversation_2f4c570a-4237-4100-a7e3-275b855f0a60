package handlers

import (
	"net/http"
	"strconv"

	"adc-account-backend/internal/services"

	"github.com/gin-gonic/gin"
)

// BranchHandler handles branch-related requests
type BranchHandler struct {
	service    *services.BranchService
	orgService *services.OrganizationService
}

func NewBranchHandler(service *services.BranchService, orgService *services.OrganizationService) *BranchHandler {
	return &BranchHandler{
		service:    service,
		orgService: orgService,
	}
}

// GetAllBranches handles GET /api/branches
func (h *BranchHandler) GetAllBranches(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.J<PERSON>N(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Parse query parameters
	page := 1
	limit := 10

	if p := c.Query("page"); p != "" {
		if parsed, err := strconv.Atoi(p); err == nil && parsed > 0 {
			page = parsed
		}
	}
	if l := c.Query("limit"); l != "" {
		if parsed, err := strconv.Atoi(l); err == nil && parsed > 0 && parsed <= 100 {
			limit = parsed
		}
	}

	// Get branches from service
	branches, total, err := h.service.GetAllBranches(page, limit, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Calculate pagination
	totalPages := (int(total) + limit - 1) / limit

	response := gin.H{
		"branches": branches,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     page < totalPages,
			"hasPreviousPage": page > 1,
		},
	}

	c.JSON(http.StatusOK, response)
}

// GetBranchByID handles GET /api/branches/:id
func (h *BranchHandler) GetBranchByID(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	id := c.Param("id")
	branch, err := h.service.GetBranchByID(id, userID)
	if err != nil {
		if err.Error() == "branch not found or access denied" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Branch not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, branch)
}

// GetBranchesByOrganization handles GET /api/organizations/:id/branches
func (h *BranchHandler) GetBranchesByOrganization(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	organizationID := c.Param("id")

	// Parse query parameters
	page := 1
	limit := 10

	if p := c.Query("page"); p != "" {
		if parsed, err := strconv.Atoi(p); err == nil && parsed > 0 {
			page = parsed
		}
	}
	if l := c.Query("limit"); l != "" {
		if parsed, err := strconv.Atoi(l); err == nil && parsed > 0 && parsed <= 100 {
			limit = parsed
		}
	}

	// Get branches from service
	branches, total, err := h.service.GetBranchesByOrganization(organizationID, page, limit, userID)
	if err != nil {
		if err.Error() == "access denied to this organization" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied to this organization"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Calculate pagination
	totalPages := (int(total) + limit - 1) / limit

	response := gin.H{
		"branches": branches,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     page < totalPages,
			"hasPreviousPage": page > 1,
		},
	}

	c.JSON(http.StatusOK, response)
}

// CreateBranch handles POST /api/organizations/:id/branches
func (h *BranchHandler) CreateBranch(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	organizationID := c.Param("id")

	var req services.CreateBranchRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	// Set organization ID from URL parameter
	req.OrganizationID = organizationID

	branch, err := h.service.CreateBranch(req, userID)
	if err != nil {
		if err.Error() == "insufficient permissions to create branches in this organization" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Insufficient permissions to create branches"})
			return
		}
		if err.Error() == "organization not found or inactive" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid organization"})
			return
		}
		if err.Error() == "branch code already exists in this organization" {
			c.JSON(http.StatusConflict, gin.H{"error": "Branch code already exists"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, branch)
}

// UpdateBranch handles PUT /api/organizations/:id/branches/:branchId
func (h *BranchHandler) UpdateBranch(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	branchID := c.Param("branchId")

	var req services.UpdateBranchRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	branch, err := h.service.UpdateBranch(branchID, req, userID)
	if err != nil {
		if err.Error() == "branch not found or insufficient permissions" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Branch not found or insufficient permissions"})
			return
		}
		if err.Error() == "branch code already exists in this organization" {
			c.JSON(http.StatusConflict, gin.H{"error": "Branch code already exists"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, branch)
}

// DeleteBranch handles DELETE /api/organizations/:id/branches/:branchId
func (h *BranchHandler) DeleteBranch(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	branchID := c.Param("branchId")

	err := h.service.DeleteBranch(branchID, userID)
	if err != nil {
		if err.Error() == "branch not found or insufficient permissions" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Branch not found or insufficient permissions"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusNoContent, nil)
}

// Slug-based handlers

// GetBranchesByOrganizationSlug handles GET /api/organizations/slug/:slug/branches
func (h *BranchHandler) GetBranchesByOrganizationSlug(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	slug := c.Param("slug")

	// Get organization by slug to get the ID
	org, err := h.orgService.GetOrganizationBySlug(slug, userID)
	if err != nil {
		if err.Error() == "organization not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Organization not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Parse query parameters
	page := 1
	limit := 10

	if p := c.Query("page"); p != "" {
		if parsed, err := strconv.Atoi(p); err == nil && parsed > 0 {
			page = parsed
		}
	}
	if l := c.Query("limit"); l != "" {
		if parsed, err := strconv.Atoi(l); err == nil && parsed > 0 && parsed <= 100 {
			limit = parsed
		}
	}

	// Get branches from service using organization ID
	branches, total, err := h.service.GetBranchesByOrganization(org.ID, page, limit, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Calculate pagination
	totalPages := (int(total) + limit - 1) / limit

	response := gin.H{
		"branches": branches,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     page < totalPages,
			"hasPreviousPage": page > 1,
		},
	}

	c.JSON(http.StatusOK, response)
}

// CreateBranchBySlug handles POST /api/organizations/slug/:slug/branches
func (h *BranchHandler) CreateBranchBySlug(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	slug := c.Param("slug")

	// Get organization by slug to get the ID
	org, err := h.orgService.GetOrganizationBySlug(slug, userID)
	if err != nil {
		if err.Error() == "organization not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Organization not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	var req services.CreateBranchRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	// Set organization ID from the resolved organization
	req.OrganizationID = org.ID

	branch, err := h.service.CreateBranch(req, userID)
	if err != nil {
		if err.Error() == "insufficient permissions to create branches in this organization" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Insufficient permissions to create branches"})
			return
		}
		if err.Error() == "organization not found or inactive" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid organization"})
			return
		}
		if err.Error() == "branch code already exists in this organization" {
			c.JSON(http.StatusConflict, gin.H{"error": "Branch code already exists"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, branch)
}

// UpdateBranchBySlug handles PUT /api/organizations/slug/:slug/branches/:branchId
func (h *BranchHandler) UpdateBranchBySlug(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	slug := c.Param("slug")
	branchID := c.Param("branchId")

	// Get organization by slug to verify access
	_, err := h.orgService.GetOrganizationBySlug(slug, userID)
	if err != nil {
		if err.Error() == "organization not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Organization not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	var req services.UpdateBranchRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	branch, err := h.service.UpdateBranch(branchID, req, userID)
	if err != nil {
		if err.Error() == "branch not found or insufficient permissions" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Branch not found or insufficient permissions"})
			return
		}
		if err.Error() == "branch code already exists in this organization" {
			c.JSON(http.StatusConflict, gin.H{"error": "Branch code already exists"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, branch)
}

// DeleteBranchBySlug handles DELETE /api/organizations/slug/:slug/branches/:branchId
func (h *BranchHandler) DeleteBranchBySlug(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	slug := c.Param("slug")
	branchID := c.Param("branchId")

	// Get organization by slug to verify access
	_, err := h.orgService.GetOrganizationBySlug(slug, userID)
	if err != nil {
		if err.Error() == "organization not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Organization not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	err = h.service.DeleteBranch(branchID, userID)
	if err != nil {
		if err.Error() == "branch not found or insufficient permissions" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Branch not found or insufficient permissions"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusNoContent, nil)
}
