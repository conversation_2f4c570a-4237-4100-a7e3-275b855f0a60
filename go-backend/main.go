package main

import (
	"log"
	"os"

	"adc-account-backend/internal/config"
	"adc-account-backend/internal/database"
	"adc-account-backend/internal/middleware"
	"adc-account-backend/internal/routes"
	"adc-account-backend/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using system environment variables")
	}

	// Load configuration
	cfg := config.Load()

	// Set Gin mode
	gin.SetMode(cfg.GinMode)

	// Initialize database
	db, err := database.Initialize(cfg.DatabaseURL)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	// Run migrations
	// if err := database.RunMigrations(cfg.DatabaseURL); err != nil {
	// 	log.Fatalf("Failed to run migrations: %v", err)
	// }

	// Initialize services
	serviceContainer := services.NewContainer(db, cfg)

	// Initialize Gin router
	router := gin.New()

	// Add middleware
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(middleware.CORS(cfg))
	router.Use(middleware.RateLimit(cfg))
	router.Use(middleware.Security())

	// Health check endpoint
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"service": "adc-account-backend",
			"version": "1.0.0",
		})
	})

	// Setup API routes
	api := router.Group("/api")
	routes.SetupRoutes(api, serviceContainer)

	// Start server
	port := os.Getenv("PORT")
	if port == "" {
		port = "8050"
	}

	log.Printf("Server starting on port %s", port)
	if err := router.Run(":" + port); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}
