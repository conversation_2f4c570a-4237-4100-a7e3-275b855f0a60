/**
 * Dashboard Page Verification Test with P<PERSON>peteer
 *
 * This test verifies the dashboard page functionality including:
 * - Authentication flow
 * - All dashboard components rendering
 * - Data accuracy from backend APIs
 * - Interactive features (period selection, refresh, tabs)
 * - Error handling
 */

const puppeteer = require('puppeteer');

describe('Dashboard Page Verification', () => {
  let browser;
  let page;

  const FRONTEND_URL = 'http://localhost:3000';
  const BACKEND_URL = 'http://localhost:8050';

  // Test user credentials
  const TEST_USER = {
    email: '<EMAIL>',
    password: 'password123'
  };

  beforeAll(async () => {
    browser = await puppeteer.launch({
      headless: false, // Set to true for CI/CD
      slowMo: 100, // Slow down for better visibility
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    page = await browser.newPage();

    // Set viewport
    await page.setViewport({ width: 1920, height: 1080 });

    // Enable request interception to monitor API calls
    await page.setRequestInterception(true);

    // Track API requests
    const apiRequests = [];
    page.on('request', (request) => {
      if (request.url().includes('/api/dashboard')) {
        apiRequests.push({
          url: request.url(),
          method: request.method(),
          timestamp: Date.now()
        });
      }
      request.continue();
    });

    // Store API requests for verification
    page.apiRequests = apiRequests;
  });

  afterAll(async () => {
    await browser.close();
  });

  beforeEach(async () => {
    // Clear API requests before each test
    page.apiRequests.length = 0;
  });

  describe('Backend Health Check', () => {
    test('should verify backend is running', async () => {
      const response = await page.goto(`${BACKEND_URL}/health`);
      expect(response.status()).toBe(200);

      const healthData = await response.json();
      expect(healthData.status).toBe('ok');
      expect(healthData.service).toBe('adc-account-backend');
    });
  });

  describe('Authentication Flow', () => {
    test('should redirect to login when not authenticated', async () => {
      await page.goto(`${FRONTEND_URL}/en/dashboard`);

      // Should redirect to login or show login form
      await page.waitForSelector('input[type="email"], [data-testid="login-form"]', { timeout: 5000 });

      const currentUrl = page.url();
      expect(currentUrl).toMatch(/(login|auth)/);
    });

    test('should authenticate user and access dashboard', async () => {
      // Navigate to login page
      await page.goto(`${FRONTEND_URL}/en/auth/login`);

      // Fill login form
      await page.waitForSelector('input[type="email"]');
      await page.type('input[type="email"]', TEST_USER.email);
      await page.type('input[type="password"]', TEST_USER.password);

      // Submit form
      await page.click('button[type="submit"]');

      // Wait for redirect to dashboard
      await page.waitForNavigation({ waitUntil: 'networkidle0' });

      const currentUrl = page.url();
      expect(currentUrl).toMatch(/dashboard/);
    });
  });

  describe('Dashboard Page Loading', () => {
    test('should load dashboard page with all components', async () => {
      await page.goto(`${FRONTEND_URL}/en/dashboard`);

      // Wait for main dashboard elements
      await page.waitForSelector('h1', { timeout: 10000 });

      // Verify page title
      const title = await page.$eval('h1', el => el.textContent);
      expect(title).toBe('Dashboard');

      // Verify refresh button exists
      await page.waitForSelector('button:has-text("Refresh")');

      // Verify period selector exists
      await page.waitForSelector('[data-testid="period-selector"], .select-trigger');
    });

    test('should make all required API calls', async () => {
      await page.goto(`${FRONTEND_URL}/en/dashboard`);

      // Wait for all API calls to complete
      await page.waitForTimeout(3000);

      // Verify all dashboard API endpoints were called
      const expectedEndpoints = [
        '/api/dashboard/financial-summary',
        '/api/dashboard/accounts-receivable-summary',
        '/api/dashboard/accounts-payable-summary',
        '/api/dashboard/top-customers',
        '/api/dashboard/top-vendors',
        '/api/dashboard/recent-transactions',
        '/api/dashboard/cash-flow'
      ];

      expectedEndpoints.forEach(endpoint => {
        const found = page.apiRequests.some(req => req.url.includes(endpoint));
        expect(found).toBe(true);
      });
    });
  });

  describe('Financial Summary Cards', () => {
    test('should display all financial summary cards with data', async () => {
      await page.goto(`${FRONTEND_URL}/en/dashboard`);

      // Wait for cards to load
      await page.waitForSelector('[data-testid="revenue-card"], .card', { timeout: 10000 });

      // Verify all financial cards are present
      const cardTitles = [
        'Total Revenue',
        'Total Expenses',
        'Net Income',
        'Cash Balance',
        'Accounts Receivable',
        'Accounts Payable'
      ];

      for (const title of cardTitles) {
        const cardExists = await page.$(`text=${title}`) !== null;
        expect(cardExists).toBe(true);
      }

      // Verify cards show currency values
      const currencyElements = await page.$$('.text-2xl.font-bold');
      expect(currencyElements.length).toBeGreaterThan(0);

      // Check that at least one card shows a currency value
      const hasValidCurrency = await page.evaluate(() => {
        const elements = document.querySelectorAll('.text-2xl.font-bold');
        return Array.from(elements).some(el =>
          el.textContent.includes('$') || el.textContent.includes('USD')
        );
      });
      expect(hasValidCurrency).toBe(true);
    });
  });

  describe('Cash Flow Chart', () => {
    test('should display cash flow chart with data', async () => {
      await page.goto(`${FRONTEND_URL}/en/dashboard`);

      // Wait for cash flow section
      await page.waitForSelector('text=Cash Flow', { timeout: 10000 });

      // Verify cash flow table or chart exists
      const cashFlowExists = await page.$('text=Monthly Cash Flow') !== null;
      expect(cashFlowExists).toBe(true);

      // Check for income/expense data
      const hasIncomeData = await page.$('text=Income') !== null;
      const hasExpenseData = await page.$('text=Expenses') !== null;

      expect(hasIncomeData).toBe(true);
      expect(hasExpenseData).toBe(true);
    });
  });

  describe('Interactive Features', () => {
    test('should change period and refresh data', async () => {
      await page.goto(`${FRONTEND_URL}/en/dashboard`);

      // Wait for period selector
      await page.waitForSelector('[role="combobox"], .select-trigger');

      // Clear previous API requests
      page.apiRequests.length = 0;

      // Click period selector
      await page.click('[role="combobox"], .select-trigger');

      // Wait for dropdown options
      await page.waitForSelector('[role="option"], .select-item');

      // Select different period (e.g., "Last 90 Days")
      await page.click('text=Last 90 Days');

      // Wait for API calls to complete
      await page.waitForTimeout(2000);

      // Verify new API calls were made with updated period
      const periodApiCalls = page.apiRequests.filter(req =>
        req.url.includes('period=90days')
      );
      expect(periodApiCalls.length).toBeGreaterThan(0);
    });

    test('should refresh all data when refresh button is clicked', async () => {
      await page.goto(`${FRONTEND_URL}/en/dashboard`);

      // Wait for refresh button
      await page.waitForSelector('button:has-text("Refresh")');

      // Clear previous API requests
      page.apiRequests.length = 0;

      // Click refresh button
      await page.click('button:has-text("Refresh")');

      // Wait for API calls to complete
      await page.waitForTimeout(3000);

      // Verify all dashboard endpoints were called again
      const expectedEndpoints = [
        'financial-summary',
        'accounts-receivable-summary',
        'accounts-payable-summary',
        'top-customers',
        'top-vendors',
        'recent-transactions',
        'cash-flow'
      ];

      expectedEndpoints.forEach(endpoint => {
        const found = page.apiRequests.some(req => req.url.includes(endpoint));
        expect(found).toBe(true);
      });
    });
  });

  describe('Tabs Navigation', () => {
    test('should navigate between different tabs', async () => {
      await page.goto(`${FRONTEND_URL}/en/dashboard`);

      // Wait for tabs to load
      await page.waitForSelector('[role="tablist"], .tabs-list');

      const tabs = ['Top Customers', 'Top Vendors', 'Recent Transactions', 'Aging Summary'];

      for (const tabName of tabs) {
        // Click tab
        await page.click(`text=${tabName}`);

        // Wait for tab content to load
        await page.waitForTimeout(1000);

        // Verify tab is active
        const isActive = await page.evaluate((name) => {
          const tab = Array.from(document.querySelectorAll('[role="tab"]'))
            .find(el => el.textContent.includes(name));
          return tab && tab.getAttribute('aria-selected') === 'true';
        }, tabName);

        expect(isActive).toBe(true);
      }
    });

    test('should display correct data in each tab', async () => {
      await page.goto(`${FRONTEND_URL}/en/dashboard`);

      // Test Top Customers tab
      await page.click('text=Top Customers');
      await page.waitForSelector('text=Customer, text=Total Sales');

      // Test Top Vendors tab
      await page.click('text=Top Vendors');
      await page.waitForSelector('text=Vendor, text=Total Purchases');

      // Test Recent Transactions tab
      await page.click('text=Recent Transactions');
      await page.waitForSelector('text=Date, text=Description, text=Amount');

      // Test Aging Summary tab
      await page.click('text=Aging Summary');
      await page.waitForSelector('text=AR Aging, text=AP Aging');
    });
  });

  describe('Data Validation', () => {
    test('should validate backend data matches frontend display', async () => {
      // First get data directly from backend
      const financialResponse = await page.goto(`${BACKEND_URL}/api/dashboard/financial-summary?merchant_id=test-merchant`);
      const financialData = await financialResponse.json();

      // Navigate to dashboard
      await page.goto(`${FRONTEND_URL}/en/dashboard`);
      await page.waitForSelector('.text-2xl.font-bold', { timeout: 10000 });

      // Verify financial data matches
      const displayedRevenue = await page.$eval('.text-2xl.font-bold', el => el.textContent);
      expect(displayedRevenue).toContain(financialData.total_revenue.toString());
    });

    test('should handle loading states properly', async () => {
      await page.goto(`${FRONTEND_URL}/en/dashboard`);

      // Check for loading skeletons initially
      const hasLoadingState = await page.waitForSelector('.skeleton, [data-testid="loading"]', { timeout: 2000 })
        .then(() => true)
        .catch(() => false);

      // Loading state should appear briefly
      expect(hasLoadingState).toBe(true);

      // Wait for content to load
      await page.waitForSelector('.text-2xl.font-bold', { timeout: 10000 });

      // Loading state should be gone
      const stillLoading = await page.$('.skeleton, [data-testid="loading"]') !== null;
      expect(stillLoading).toBe(false);
    });

    test('should handle error states gracefully', async () => {
      // Simulate network error by blocking API requests
      await page.setRequestInterception(true);
      page.on('request', (request) => {
        if (request.url().includes('/api/dashboard/financial-summary')) {
          request.abort();
        } else {
          request.continue();
        }
      });

      await page.goto(`${FRONTEND_URL}/en/dashboard`);

      // Should show error state or retry option
      const hasErrorHandling = await page.waitForSelector('text=Error, text=Retry, [data-testid="error"]', { timeout: 5000 })
        .then(() => true)
        .catch(() => false);

      expect(hasErrorHandling).toBe(true);
    });
  });

  describe('Performance and Accessibility', () => {
    test('should load dashboard within acceptable time', async () => {
      const startTime = Date.now();

      await page.goto(`${FRONTEND_URL}/en/dashboard`);
      await page.waitForSelector('.text-2xl.font-bold', { timeout: 10000 });

      const loadTime = Date.now() - startTime;

      // Dashboard should load within 10 seconds
      expect(loadTime).toBeLessThan(10000);
    });

    test('should be accessible with keyboard navigation', async () => {
      await page.goto(`${FRONTEND_URL}/en/dashboard`);

      // Test tab navigation
      await page.keyboard.press('Tab');
      await page.keyboard.press('Tab');

      // Should be able to activate refresh button with keyboard
      const refreshButton = await page.$('button:has-text("Refresh")');
      await refreshButton.focus();
      await page.keyboard.press('Enter');

      // Should trigger refresh (check for API calls)
      await page.waitForTimeout(1000);
      expect(page.apiRequests.length).toBeGreaterThan(0);
    });
  });
});