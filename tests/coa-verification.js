/**
 * Chart of Accounts (COA) Page Verification Script
 *
 * This script uses <PERSON><PERSON><PERSON><PERSON> to verify the complete Chart of Accounts page functionality:
 * 1. Navigate to COA page
 * 2. Test page loading and UI elements
 * 3. Test search functionality
 * 4. Test filtering by account types
 * 5. Test pagination
 * 6. Test CRUD operations (if permissions allow)
 * 7. Test bank account linking functionality
 * 8. Take screenshots for documentation
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

// Test configuration
const CONFIG = {
  FRONTEND_URL: 'http://localhost:3001',
  BACKEND_URL: 'http://localhost:8050',
  COA_PAGE_URL: 'http://localhost:3001/en/chart-of-accounts',
  SCREENSHOTS_DIR: 'tests/puppeteer-screenshots',
  TEST_TIMEOUT: 30000
};

async function validateServers() {
  console.log('🔍 Validating servers...');

  try {
    // Check frontend
    const frontendResponse = await fetch(CONFIG.FRONTEND_URL);
    console.log(`✅ Frontend server (${CONFIG.FRONTEND_URL}): ${frontendResponse.status}`);

    // Check backend
    const backendResponse = await fetch(`${CONFIG.BACKEND_URL}/health`);
    console.log(`✅ Backend server (${CONFIG.BACKEND_URL}/health): ${backendResponse.status}`);

    return true;
  } catch (error) {
    console.error('❌ Server validation failed:', error.message);
    return false;
  }
}

async function createScreenshotsDir() {
  if (!fs.existsSync(CONFIG.SCREENSHOTS_DIR)) {
    fs.mkdirSync(CONFIG.SCREENSHOTS_DIR, { recursive: true });
    console.log(`📁 Created screenshots directory: ${CONFIG.SCREENSHOTS_DIR}`);
  }
}

async function takeScreenshot(page, filename, description) {
  const screenshotPath = path.join(CONFIG.SCREENSHOTS_DIR, filename);
  await page.screenshot({
    path: screenshotPath,
    fullPage: true
  });
  console.log(`📸 Screenshot: ${filename} - ${description}`);
}

async function waitForElement(page, selector, timeout = 10000) {
  try {
    await page.waitForSelector(selector, { timeout });
    return true;
  } catch (error) {
    console.warn(`⚠️ Element not found: ${selector}`);
    return false;
  }
}

async function verifyPageLoad(page) {
  console.log('📍 Step 1: Verifying page load...');

  // Navigate to COA page
  await page.goto(CONFIG.COA_PAGE_URL, {
    waitUntil: 'networkidle2',
    timeout: CONFIG.TEST_TIMEOUT
  });

  await takeScreenshot(page, '01-coa-page-load.png', 'Initial page load');

  // Check for main page elements
  const pageTitle = await page.$('h1');
  if (pageTitle) {
    const titleText = await page.evaluate(el => el.textContent, pageTitle);
    console.log(`✅ Page title: ${titleText}`);
  }

  // Check for search input
  const searchInput = await waitForElement(page, 'input[placeholder*="search" i], input[placeholder*="Search" i]');
  if (searchInput) {
    console.log('✅ Search input found');
  }

  // Check for tabs
  const tabs = await page.$$('[role="tablist"] button, .tabs button');
  console.log(`✅ Found ${tabs.length} filter tabs`);

  // Check for table
  const table = await waitForElement(page, 'table');
  if (table) {
    console.log('✅ Accounts table found');
  }

  return true;
}

async function verifySearchFunctionality(page) {
  console.log('📍 Step 2: Testing search functionality...');

  // Find search input
  const searchSelector = 'input[placeholder*="search" i], input[placeholder*="Search" i]';
  const searchInput = await page.$(searchSelector);

  if (searchInput) {
    // Test search with a common term
    await page.type(searchSelector, 'Cash', { delay: 100 });
    await new Promise(resolve => setTimeout(resolve, 2000)); // Wait for search results

    await takeScreenshot(page, '02-search-cash.png', 'Search for "Cash" accounts');

    // Clear search
    await page.evaluate((selector) => {
      const input = document.querySelector(selector);
      if (input) {
        input.value = '';
        input.dispatchEvent(new Event('input', { bubbles: true }));
      }
    }, searchSelector);

    await new Promise(resolve => setTimeout(resolve, 1000));
    console.log('✅ Search functionality tested');
  } else {
    console.warn('⚠️ Search input not found');
  }
}

async function verifyFilterTabs(page) {
  console.log('📍 Step 3: Testing filter tabs...');

  // Test different account type filters
  const filterTests = [
    { value: 'Asset', name: 'Assets' },
    { value: 'Liability', name: 'Liabilities' },
    { value: 'Revenue', name: 'Revenue' },
    { value: 'Expense', name: 'Expenses' }
  ];

  for (const filter of filterTests) {
    try {
      // Look for tab with the filter value or name
      const tabSelector = `[role="tab"][value="${filter.value}"], button[data-value="${filter.value}"], button:contains("${filter.name}")`;
      const tab = await page.$(tabSelector);

      if (tab) {
        await tab.click();
        await new Promise(resolve => setTimeout(resolve, 2000)); // Wait for filter results

        await takeScreenshot(page, `03-filter-${filter.value.toLowerCase()}.png`, `Filter by ${filter.name}`);
        console.log(`✅ ${filter.name} filter tested`);
      } else {
        // Try alternative selector
        const tabs = await page.$$('[role="tab"], .tabs button');
        for (const tabElement of tabs) {
          const tabText = await page.evaluate(el => el.textContent.trim(), tabElement);
          if (tabText.toLowerCase().includes(filter.name.toLowerCase())) {
            await tabElement.click();
            await new Promise(resolve => setTimeout(resolve, 2000));
            await takeScreenshot(page, `03-filter-${filter.value.toLowerCase()}.png`, `Filter by ${filter.name}`);
            console.log(`✅ ${filter.name} filter tested`);
            break;
          }
        }
      }
    } catch (error) {
      console.warn(`⚠️ Could not test ${filter.name} filter: ${error.message}`);
    }
  }

  // Return to "All" tab
  try {
    const allTab = await page.$('[role="tab"][value="all"], button[data-value="all"]');
    if (allTab) {
      await allTab.click();
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  } catch (error) {
    console.warn('⚠️ Could not return to "All" tab');
  }
}

async function verifyTableContent(page) {
  console.log('📍 Step 4: Verifying table content...');

  // Check table headers
  const headers = await page.$$eval('th', elements =>
    elements.map(el => el.textContent.trim())
  );
  console.log(`✅ Table headers: ${headers.join(', ')}`);

  // Count table rows
  const rows = await page.$$('tbody tr');
  console.log(`✅ Found ${rows.length} account rows`);

  // Check for account data in first row (if exists)
  if (rows.length > 0) {
    const firstRowData = await page.$$eval('tbody tr:first-child td', cells =>
      cells.map(cell => cell.textContent.trim())
    );
    console.log(`✅ First row data: ${firstRowData.slice(0, 3).join(' | ')}`);
  }

  await takeScreenshot(page, '04-table-content.png', 'Table content verification');
}

async function verifyPagination(page) {
  console.log('📍 Step 5: Testing pagination...');

  // Look for pagination controls
  const paginationControls = await page.$$('button[aria-label*="page" i], .pagination button');

  if (paginationControls.length > 0) {
    console.log(`✅ Found ${paginationControls.length} pagination controls`);

    // Test next page if available
    const nextButton = await page.$('button[aria-label*="next" i], button:contains("Next")');
    if (nextButton) {
      const isDisabled = await page.evaluate(btn => btn.disabled, nextButton);
      if (!isDisabled) {
        await nextButton.click();
        await new Promise(resolve => setTimeout(resolve, 2000));
        await takeScreenshot(page, '05-pagination-next.png', 'Next page navigation');

        // Go back to first page
        const firstButton = await page.$('button[aria-label*="first" i], button:contains("First")');
        if (firstButton) {
          await firstButton.click();
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
    }
  } else {
    console.log('ℹ️ No pagination controls found (single page)');
  }
}

async function verifyActionButtons(page) {
  console.log('📍 Step 6: Verifying action buttons...');

  // Check for Add New Account button
  const addButton = await page.$('button:contains("Add"), button:contains("New"), button:contains("Create")');
  if (addButton) {
    console.log('✅ Add New Account button found');
  }

  // Check for action menus in table rows
  const actionMenus = await page.$$('button[aria-label*="menu" i], button:contains("⋮"), button:contains("...")');
  console.log(`✅ Found ${actionMenus.length} action menu buttons`);

  await takeScreenshot(page, '06-action-buttons.png', 'Action buttons verification');
}

async function verifyApiEndpoints() {
  console.log('📍 Step 7: Verifying API endpoints...');

  try {
    // Test the endpoint the frontend is trying to call
    const frontendEndpoint = 'http://localhost:8050/api/coa';
    const frontendResponse = await fetch(frontendEndpoint);
    console.log(`Frontend expects: ${frontendEndpoint} - Status: ${frontendResponse.status}`);

    // Test the actual backend endpoint
    const backendEndpoint = 'http://localhost:8050/api/accounts';
    const backendResponse = await fetch(backendEndpoint);
    console.log(`Backend provides: ${backendEndpoint} - Status: ${backendResponse.status}`);

    // Test merchant-specific endpoint
    const merchantEndpoint = 'http://localhost:8050/api/merchants/1/accounts';
    const merchantResponse = await fetch(merchantEndpoint);
    console.log(`Merchant endpoint: ${merchantEndpoint} - Status: ${merchantResponse.status}`);

    return {
      frontendEndpointExists: frontendResponse.status !== 404,
      backendEndpointExists: backendResponse.status !== 404,
      merchantEndpointExists: merchantResponse.status !== 404
    };
  } catch (error) {
    console.error('❌ API endpoint verification failed:', error.message);
    return { error: error.message };
  }
}

async function generateVerificationReport(page, apiResults) {
  console.log('\n📋 CHART OF ACCOUNTS VERIFICATION REPORT');
  console.log('==========================================');

  // Page Load Analysis
  const pageTitle = await page.evaluate(() => document.querySelector('h1')?.textContent || 'No title');
  const hasSearchInput = await page.evaluate(() => !!document.querySelector('input[placeholder*="search"], input[placeholder*="Search"]'));
  const hasTable = await page.evaluate(() => !!document.querySelector('table'));
  const errorMessage = await page.evaluate(() => {
    const errorEl = document.querySelector('.error, [role="alert"]');
    return errorEl ? errorEl.textContent.trim() : null;
  });

  console.log(`✅ Page Title: ${pageTitle}`);
  console.log(`${hasSearchInput ? '✅' : '❌'} Search Input: ${hasSearchInput ? 'Found' : 'Not Found'}`);
  console.log(`${hasTable ? '✅' : '❌'} Data Table: ${hasTable ? 'Found' : 'Not Found'}`);

  if (errorMessage) {
    console.log(`❌ Error Message: ${errorMessage}`);
  }

  // API Endpoint Analysis
  console.log('\n🔗 API ENDPOINT ANALYSIS:');
  console.log(`Frontend calls: /api/coa`);
  console.log(`Backend provides: /api/accounts (global) or /api/merchants/:id/accounts (merchant-specific)`);
  console.log(`Status: ${apiResults.frontendEndpointExists ? 'MISMATCH DETECTED' : 'ENDPOINT MISMATCH CONFIRMED'}`);

  // Recommendations
  console.log('\n💡 RECOMMENDATIONS:');
  console.log('1. Update frontend API calls to match backend endpoints');
  console.log('2. Implement authentication for API access');
  console.log('3. Add proper error handling for API failures');
  console.log('4. Consider adding a /api/coa alias endpoint in backend');

  return {
    pageLoaded: !!pageTitle,
    hasSearchInput,
    hasTable,
    errorMessage,
    apiMismatch: !apiResults.frontendEndpointExists
  };
}

async function runCoaVerification() {
  console.log('🚀 Starting Chart of Accounts verification...');

  // Launch browser
  const browser = await puppeteer.launch({
    headless: false, // Set to true for headless mode
    defaultViewport: { width: 1280, height: 720 },
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });

  const page = await browser.newPage();

  try {
    // Run verification steps
    await verifyPageLoad(page);
    await verifySearchFunctionality(page);
    await verifyFilterTabs(page);
    await verifyTableContent(page);
    await verifyPagination(page);
    await verifyActionButtons(page);

    // Verify API endpoints
    const apiResults = await verifyApiEndpoints();

    // Generate comprehensive report
    const report = await generateVerificationReport(page, apiResults);

    // Final screenshot
    await takeScreenshot(page, '07-final-state.png', 'Final page state');

    console.log('\n🎉 Chart of Accounts verification completed!');
    return report;

  } catch (error) {
    console.error('❌ Error during COA verification:', error.message);
    await takeScreenshot(page, '99-error.png', 'Error state');
    throw error;
  } finally {
    await browser.close();
  }
}

async function main() {
  console.log('🎯 Chart of Accounts Page Verification');
  console.log('=====================================');

  // Create screenshots directory
  await createScreenshotsDir();

  // Validate servers
  const serversOk = await validateServers();
  if (!serversOk) {
    console.log('❌ Servers not available - exiting');
    process.exit(1);
  }

  // Run COA verification
  await runCoaVerification();

  console.log('=====================================');
  console.log('🏁 Verification completed!');
  console.log(`📁 Screenshots saved to: ${CONFIG.SCREENSHOTS_DIR}`);
}

// Run the verification
main().catch(console.error);
