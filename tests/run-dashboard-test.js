#!/usr/bin/env node

/**
 * Dashboard Test Runner
 * 
 * This script runs the dashboard verification test with P<PERSON>peteer
 * and provides detailed reporting of the results.
 */

const puppeteer = require('puppeteer');
const path = require('path');

// Configuration
const CONFIG = {
  FRONTEND_URL: 'http://localhost:3000',
  BACKEND_URL: 'http://localhost:8050',
  TEST_USER: {
    email: '<EMAIL>',
    password: 'password123'
  },
  HEADLESS: process.env.HEADLESS !== 'false', // Set HEADLESS=false to see browser
  TIMEOUT: 30000
};

class DashboardTester {
  constructor() {
    this.browser = null;
    this.page = null;
    this.results = {
      passed: 0,
      failed: 0,
      tests: []
    };
  }

  async init() {
    console.log('🚀 Starting Dashboard Verification Test...\n');
    
    this.browser = await puppeteer.launch({
      headless: CONFIG.HEADLESS,
      slowMo: 50,
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
      defaultViewport: { width: 1920, height: 1080 }
    });
    
    this.page = await this.browser.newPage();
    
    // Enable request interception
    await this.page.setRequestInterception(true);
    this.page.apiRequests = [];
    
    this.page.on('request', (request) => {
      if (request.url().includes('/api/dashboard')) {
        this.page.apiRequests.push({
          url: request.url(),
          method: request.method(),
          timestamp: Date.now()
        });
      }
      request.continue();
    });

    // Handle console logs
    this.page.on('console', (msg) => {
      if (msg.type() === 'error') {
        console.log('❌ Browser Error:', msg.text());
      }
    });
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
    }
  }

  async runTest(name, testFn) {
    console.log(`🧪 Running: ${name}`);
    const startTime = Date.now();
    
    try {
      await testFn();
      const duration = Date.now() - startTime;
      console.log(`✅ PASSED: ${name} (${duration}ms)\n`);
      
      this.results.passed++;
      this.results.tests.push({ name, status: 'PASSED', duration });
      
      return true;
    } catch (error) {
      const duration = Date.now() - startTime;
      console.log(`❌ FAILED: ${name} (${duration}ms)`);
      console.log(`   Error: ${error.message}\n`);
      
      this.results.failed++;
      this.results.tests.push({ name, status: 'FAILED', duration, error: error.message });
      
      return false;
    }
  }

  async checkBackendHealth() {
    return this.runTest('Backend Health Check', async () => {
      const response = await this.page.goto(`${CONFIG.BACKEND_URL}/health`);
      if (response.status() !== 200) {
        throw new Error(`Backend health check failed with status ${response.status()}`);
      }
      
      const data = await response.json();
      if (data.status !== 'ok') {
        throw new Error('Backend is not healthy');
      }
    });
  }

  async checkDashboardLoading() {
    return this.runTest('Dashboard Page Loading', async () => {
      await this.page.goto(`${CONFIG.FRONTEND_URL}/en/dashboard`);
      
      // Wait for main dashboard elements
      await this.page.waitForSelector('h1', { timeout: CONFIG.TIMEOUT });
      
      const title = await this.page.$eval('h1', el => el.textContent);
      if (title !== 'Dashboard') {
        throw new Error(`Expected title 'Dashboard', got '${title}'`);
      }
      
      // Check for essential elements
      await this.page.waitForSelector('button:has-text("Refresh")', { timeout: 5000 });
      await this.page.waitForSelector('[role="combobox"], .select-trigger', { timeout: 5000 });
    });
  }

  async checkAPIRequests() {
    return this.runTest('API Requests Verification', async () => {
      await this.page.goto(`${CONFIG.FRONTEND_URL}/en/dashboard`);
      
      // Clear previous requests
      this.page.apiRequests.length = 0;
      
      // Wait for API calls
      await this.page.waitForTimeout(5000);
      
      const expectedEndpoints = [
        'financial-summary',
        'accounts-receivable-summary',
        'accounts-payable-summary',
        'top-customers',
        'top-vendors',
        'recent-transactions',
        'cash-flow'
      ];
      
      const missingEndpoints = expectedEndpoints.filter(endpoint => 
        !this.page.apiRequests.some(req => req.url.includes(endpoint))
      );
      
      if (missingEndpoints.length > 0) {
        throw new Error(`Missing API calls for: ${missingEndpoints.join(', ')}`);
      }
      
      console.log(`   📡 Made ${this.page.apiRequests.length} API requests`);
    });
  }

  async checkFinancialCards() {
    return this.runTest('Financial Summary Cards', async () => {
      await this.page.goto(`${CONFIG.FRONTEND_URL}/en/dashboard`);
      
      // Wait for cards to load
      await this.page.waitForSelector('.card, [data-testid="revenue-card"]', { timeout: CONFIG.TIMEOUT });
      
      const cardTitles = [
        'Total Revenue',
        'Total Expenses',
        'Net Income',
        'Cash Balance',
        'Accounts Receivable',
        'Accounts Payable'
      ];
      
      for (const title of cardTitles) {
        const element = await this.page.$(`text=${title}`);
        if (!element) {
          throw new Error(`Card with title '${title}' not found`);
        }
      }
      
      // Check for currency values
      const currencyElements = await this.page.$$('.text-2xl.font-bold');
      if (currencyElements.length === 0) {
        throw new Error('No currency values found in cards');
      }
      
      console.log(`   💰 Found ${currencyElements.length} financial value displays`);
    });
  }

  async checkTabNavigation() {
    return this.runTest('Tab Navigation', async () => {
      await this.page.goto(`${CONFIG.FRONTEND_URL}/en/dashboard`);
      
      // Wait for tabs
      await this.page.waitForSelector('[role="tablist"], .tabs-list', { timeout: CONFIG.TIMEOUT });
      
      const tabs = ['Top Customers', 'Top Vendors', 'Recent Transactions', 'Aging Summary'];
      
      for (const tabName of tabs) {
        await this.page.click(`text=${tabName}`);
        await this.page.waitForTimeout(1000);
        
        // Verify tab content is visible
        const tabContent = await this.page.$('[role="tabpanel"]:not([hidden])');
        if (!tabContent) {
          throw new Error(`Tab content for '${tabName}' not visible`);
        }
      }
      
      console.log(`   📑 Successfully navigated through ${tabs.length} tabs`);
    });
  }

  async checkRefreshFunctionality() {
    return this.runTest('Refresh Functionality', async () => {
      await this.page.goto(`${CONFIG.FRONTEND_URL}/en/dashboard`);
      
      // Wait for initial load
      await this.page.waitForSelector('button:has-text("Refresh")', { timeout: CONFIG.TIMEOUT });
      await this.page.waitForTimeout(2000);
      
      // Clear API requests
      this.page.apiRequests.length = 0;
      
      // Click refresh
      await this.page.click('button:has-text("Refresh")');
      
      // Wait for new requests
      await this.page.waitForTimeout(3000);
      
      if (this.page.apiRequests.length === 0) {
        throw new Error('No API requests made after refresh');
      }
      
      console.log(`   🔄 Refresh triggered ${this.page.apiRequests.length} API requests`);
    });
  }

  async checkDataValidation() {
    return this.runTest('Data Validation', async () => {
      // Get backend data directly
      const response = await this.page.goto(`${CONFIG.BACKEND_URL}/api/dashboard/financial-summary?merchant_id=test-merchant`);
      
      if (response.status() !== 200) {
        throw new Error('Failed to fetch backend data');
      }
      
      const backendData = await response.json();
      
      // Navigate to dashboard
      await this.page.goto(`${CONFIG.FRONTEND_URL}/en/dashboard`);
      await this.page.waitForSelector('.text-2xl.font-bold', { timeout: CONFIG.TIMEOUT });
      
      // Basic validation that data is displayed
      const displayedValues = await this.page.$$eval('.text-2xl.font-bold', 
        elements => elements.map(el => el.textContent)
      );
      
      if (displayedValues.length === 0) {
        throw new Error('No financial values displayed on dashboard');
      }
      
      console.log(`   📊 Validated ${displayedValues.length} data points`);
    });
  }

  async runAllTests() {
    try {
      await this.init();
      
      // Run all tests
      await this.checkBackendHealth();
      await this.checkDashboardLoading();
      await this.checkAPIRequests();
      await this.checkFinancialCards();
      await this.checkTabNavigation();
      await this.checkRefreshFunctionality();
      await this.checkDataValidation();
      
    } catch (error) {
      console.error('❌ Test runner error:', error.message);
      this.results.failed++;
    } finally {
      await this.cleanup();
      this.printResults();
    }
  }

  printResults() {
    console.log('\n' + '='.repeat(60));
    console.log('📋 DASHBOARD VERIFICATION TEST RESULTS');
    console.log('='.repeat(60));
    
    console.log(`✅ Passed: ${this.results.passed}`);
    console.log(`❌ Failed: ${this.results.failed}`);
    console.log(`📊 Total:  ${this.results.passed + this.results.failed}`);
    
    if (this.results.failed > 0) {
      console.log('\n❌ Failed Tests:');
      this.results.tests
        .filter(test => test.status === 'FAILED')
        .forEach(test => {
          console.log(`   • ${test.name}: ${test.error}`);
        });
    }
    
    console.log('\n' + '='.repeat(60));
    
    if (this.results.failed === 0) {
      console.log('🎉 All tests passed! Dashboard is working correctly.');
      process.exit(0);
    } else {
      console.log('⚠️  Some tests failed. Please check the dashboard implementation.');
      process.exit(1);
    }
  }
}

// Run the tests
if (require.main === module) {
  const tester = new DashboardTester();
  tester.runAllTests().catch(console.error);
}

module.exports = DashboardTester;
